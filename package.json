{"name": "nex-move", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "check-all": "npm run type-check && npm run lint && npm run format:check", "analyze": "ANALYZE=true npm run build", "analyze:server": "ANALYZE=true BUNDLE_ANALYZE=server npm run build", "analyze:browser": "ANALYZE=true BUNDLE_ANALYZE=browser npm run build"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^3.10.0", "@mui/material": "^6.4.2", "@mui/x-date-pickers": "^7.25.0", "@portabletext/react": "^3.2.1", "@react-google-maps/api": "^2.20.6", "@sanity/image-url": "^1.1.0", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.67.2", "axios": "^1.8.2", "clsx": "^2.1.1", "dayjs": "^1.11.13", "embla-carousel-react": "^8.5.2", "framer-motion": "^12.0.6", "next": "15.1.0", "next-sanity": "^9.8.57", "nextjs-toploader": "^3.8.16", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "resend": "^4.6.0", "sonner": "^2.0.3", "tailwind-merge": "^2.6.0", "zod": "^3.24.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^15.4.6", "@types/node": "^20.19.7", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "fluid-tailwind": "^1.0.4", "postcss": "^8", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.1", "typescript": "^5"}}