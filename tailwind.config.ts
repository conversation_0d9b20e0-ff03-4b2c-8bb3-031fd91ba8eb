import typography from "@tailwindcss/typography"
import fluid, { extract, fontSize, screens } from "fluid-tailwind"
import type { Config } from "tailwindcss"

export default {
  darkMode: "class",
  content: {
    files: [
      "./pages/**/*.{js,ts,jsx,tsx,mdx}",
      "./components/**/*.{js,ts,jsx,tsx,mdx}",
      "./app/**/*.{js,ts,jsx,tsx,mdx}",
      "./containers/**/*.{js,ts,jsx,tsx,mdx}",
      "./screens/**/*.{js,ts,jsx,tsx,mdx}",
    ],
    extract,
  },
  theme: {
    extend: {
      screens,
      fontSize,
      colors: {
        primary: "#ff5b00",
        secondary: "#d32e0e",
        darkGray: "#131415",
      },
      fontFamily: {
        lato: ["var(--font-lato)"],
        inter: ["var(--font-inter)"],
        geist: ["var(--font-geist)"],
        manrope: ["var(--font-manrope)"],
      },
      keyframes: {
        slide_to_left: {
          from: { transform: "translateX(0)" },
          to: { transform: "translateX(-100%)" },
        },
        slide_to_right: {
          from: { transform: "translateX(-100%)" },
          to: { transform: "translateX(0)" },
        },
      },
      animation: {
        "slide-left": "20s slide_to_left linear infinite",
        "slide-right": "20s slide_to_right linear infinite",
      },
      backgroundImage: {
        mistGradient:
          "linear-gradient(90deg, #FFF 55.76%, #C5CFD1 70.25%, #F9F9F9 78.66%, #D3DADC 100%)",
      },
    },
  },
  plugins: [fluid, typography],
} satisfies Config
