# Payment API Documentation

This document describes the API endpoints and data structure for handling payments in the NexMove website, including payment link creation, form prefill, phone verification, and payment verification.

# Payment API Documentation

This document describes the API endpoints and data structure for handling payments in the NexMove website, including payment link creation, form prefill, phone verification, and payment verification.

## API Endpoints

### 1. Create Payment (POST)

- **Endpoint:** `/api/payment`
- **Description:** Create a new payment entry when a payment link is generated.
- **Request Body (JSON):**

```json
{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "phone": "+************",
  "amount": 5000,
  "paymentMethod": { "name": "CREDIT CARD", "id": 1 },
  "message": "Payment for moving services",
  "phoneVerified": false,
  "paymentVerified": false
}
```

- **Response:**
  - `201 Created` with payment object

**paymentMethod** should be a dropdown value of type `ddValue`:

- `CREDIT CARD` (id: 1)
- `DEBIT CARD` (id: 2)
- `UPI` (id: 3)
- `NETBANKING` (id: 4)
- `E-WALLETS` (id: 5)
- Example: `{ "name": "UPI", "id": 3 }`

---

### 2. Get Payment (GET)

- **Endpoint:** `/api/payment/{id}`
- **Description:** Retrieve payment details by ID.
- **Response:**

```json
{
  "id": "string",
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+************",
  "amount": 5000,
  "paymentMethod": { "name": "CREDIT CARD", "id": 1 },
  "message": "Payment for moving services",
  "phoneVerified": true,
  "paymentVerified": true
}
```

---

### 3. Update Payment (PUT/PATCH)

- **Endpoint:** `/api/payment/{id}`
- **Description:** Update payment details, such as verification status.
- **Request Body (JSON):**

```json
{
  "phoneVerified": true,
  "paymentVerified": true
}
```

- **Response:**
  - `200 OK` with updated payment object

---

### 4. Delete Payment (DELETE)

- **Endpoint:** `/api/payment/{id}`
- **Description:** Delete a payment entry by ID.
- **Response:**
  - `204 No Content`

---

## Payment Process Steps (Current Implementation)

1. **Admin creates a payment link** in Razorpay Dashboard with customer details (name, email, phone) and amount.
2. **Customer receives the pay-now URL** via email, e.g. `/pay-now?pay-id=plink_xxx`.
3. **Customer clicks the link** and the form is auto-filled with name, email, phone (without +91), and amount, fetched directly from Razorpay using the payment link API.
4. **Customer verifies their phone number** via OTP.
5. **Customer submits the form**. The site (optionally) sends a POST request to the backend to create a DB entry (currently commented out in code).
6. **User is redirected to the Razorpay payment checkout page** using the `short_url` property from Razorpay.
7. **After payment**, Razorpay can redirect the user back to the site using the `callback_url` if configured in the payment link.

**Note:**

- The Razorpay SDK popup is no longer used. Instead, the user is redirected to the Razorpay-hosted checkout page for a native payment experience.
- The phone number is prefilled without the +91 country code.
- The backend POST is optional and can be enabled when the backend is ready.

---

## Example Payment JSON Object

```json
{
  "id": "string",
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+************",
  "amount": 5000,
  "paymentMethod": { "name": "CREDIT CARD", "id": 1 },
  "message": "Payment for moving services",
  "phoneVerified": true,
  "paymentVerified": true
}
```

---

For further details, refer to the implementation in the `/api/payment` route.
