"use client"

import { Loader } from "@/app/components/common"
import JobApplicationModal from "@/app/components/modals/JobApplicationModal"
import { Button } from "@/app/components/ui"
import { <PERSON>Header, SectionWrapper } from "@/app/shared/components"
import {
  formatJobPosting,
  getJobPostingById,
  JobPosting,
} from "@/app/shared/services/job-application"
import { QUERY_KEYS } from "@/lib/react-query"
import { useQuery } from "@tanstack/react-query"
import { useParams, useRouter } from "next/navigation"
import { useState } from "react"
import {
  CalendarIcon,
  ChatOutlineIcon,
  ChecklistIcon,
  GlobalArrowRightIcon,
  PersonIcon,
  SpeedoMeterIcon,
  WorldwideIcon,
} from "../Icons"
import LocationIcon from "../Icons/LocationIcon"
// import { Icons } from "@/config"

// Function to fetch job posting by ID
const fetchJobPosting = async (id: number): Promise<JobPosting> => {
  if (!id || isNaN(id)) {
    throw new Error("Invalid job ID")
  }
  return await getJobPostingById(id)
}

// Job Header Card Component
const JobHeaderCard = ({
  formattedJob,
  jobPosting,
}: {
  formattedJob: ReturnType<typeof formatJobPosting>
  jobPosting: JobPosting
}) => {
  return (
    <div className='overflow-hidden rounded-t-xl bg-primary'>
      <div className='relative p-8'>
        {/* Status Badge */}
        <div className='absolute right-4 top-4 flex items-center rounded-full bg-white/20 px-4 py-1.5 backdrop-blur-sm'>
          <span className='mr-2 h-2 w-2 animate-pulse rounded-full bg-green-400'></span>
          <span className='text-sm font-medium text-white'>
            {jobPosting.jobPostingStatus?.name || "Published"}
          </span>
        </div>

        <div className='flex flex-col gap-3'>
          <h1 className='text-3xl font-bold text-white'>
            {formattedJob.title}
          </h1>

          <div className='flex flex-wrap items-center gap-3 text-white/90'>
            <span className='rounded-full bg-white/20 px-3 py-1 text-sm font-medium backdrop-blur-sm'>
              {formattedJob.type}
            </span>
            {jobPosting.jobPosition?.jobCode && (
              <span className='rounded-full bg-white/20 px-3 py-1 text-sm font-medium backdrop-blur-sm'>
                Job Code: {jobPosting.jobPosition.jobCode}
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

// Job Details Card Component
const JobDetailsCard = ({
  formattedJob,
}: {
  formattedJob: ReturnType<typeof formatJobPosting>
}) => {
  return (
    <div className='overflow-hidden rounded-b-xl border border-t-0 border-zinc-100 bg-white shadow-sm'>
      <div className='p-6'>
        <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
          <div className='flex items-center'>
            <span className='mr-4 flex-shrink-0 rounded-full bg-primary/10 p-3 text-primary'>
              <LocationIcon className='h-6 w-6' />
            </span>
            <div>
              <p className='text-xs font-medium uppercase text-zinc-500'>
                Location
              </p>
              <p className='text-lg font-medium'>
                {formattedJob.locations.join(", ")}
              </p>
            </div>
          </div>

          <div className='flex items-center'>
            <span className='mr-4 flex-shrink-0 rounded-full bg-primary/10 p-3 text-primary'>
              <CalendarIcon className='h-6 w-6' />
            </span>
            <div>
              <p className='text-xs font-medium uppercase text-zinc-500'>
                Posted On
              </p>
              <p className='text-lg font-medium'>
                {new Date(formattedJob.createdDate).toLocaleDateString(
                  "en-US",
                  {
                    year: "numeric",
                    month: "short",
                    day: "numeric",
                  },
                )}
              </p>
            </div>
          </div>

          <div className='flex items-center'>
            <span className='mr-4 flex-shrink-0 rounded-full bg-primary/10 p-3 text-primary'>
              <PersonIcon className='h-6 w-6' />
            </span>
            <div>
              <p className='text-xs font-medium uppercase text-zinc-500'>
                Experience
              </p>
              <p className='text-lg font-medium'>
                {formattedJob.minimumExperienceInYears} years minimum
              </p>
            </div>
          </div>

          <div className='flex items-center'>
            <span className='mr-4 flex-shrink-0 rounded-full bg-primary/10 p-3 text-primary'>
              <WorldwideIcon className='h-6 w-6' />
            </span>
            <div>
              <p className='text-xs font-medium uppercase text-zinc-500'>
                Department
              </p>
              <p className='text-lg font-medium'>
                {formattedJob.department || "Not specified"}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Job Description Section Component
const JobDescriptionSection = ({ description }: { description: string }) => {
  return (
    <div className='overflow-hidden rounded-xl border border-zinc-100 bg-white shadow-sm'>
      <div className='border-b border-zinc-100 bg-primary/5 p-6'>
        <div className='flex items-center'>
          <span className='mr-4 flex-shrink-0 rounded-full bg-primary/20 p-3 text-primary'>
            <ChatOutlineIcon className='h-6 w-6' />
          </span>
          <h3 className='text-xl font-semibold'>Job Description</h3>
        </div>
      </div>
      <div className='p-6'>
        <p className='whitespace-pre-line leading-relaxed text-gray-700'>
          {description || "No description provided."}
        </p>
      </div>
    </div>
  )
}

// Job Requirements List Component
const JobRequirementsList = ({ requirements }: { requirements: string[] }) => {
  if (!requirements || requirements.length === 0) {
    return null
  }

  return (
    <div className='overflow-hidden rounded-xl border border-zinc-100 bg-white shadow-sm'>
      <div className='border-b border-zinc-100 bg-primary/5 p-6'>
        <div className='flex items-center'>
          <span className='mr-4 flex-shrink-0 rounded-full bg-primary/20 p-3 text-primary'>
            <ChecklistIcon className='h-6 w-6' />
          </span>
          <h3 className='text-xl font-semibold'>Requirements</h3>
        </div>
      </div>
      <div className='p-6'>
        <ul className='space-y-4'>
          {requirements.map((requirement, index) => (
            <li key={index} className='group flex items-start'>
              <span className='mr-3 mt-1 flex-shrink-0 text-primary transition-transform group-hover:scale-110'>
                <GlobalArrowRightIcon className='h-5 w-5' />
              </span>
              <span className='text-gray-700'>{requirement}</span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  )
}

// Apply Button Component
const ApplyButton = ({ onClick }: { onClick: () => void }) => {
  return (
    <div className='overflow-hidden rounded-xl border border-zinc-100 bg-white shadow-sm'>
      <div className='border-b border-zinc-100 bg-primary/5 p-6'>
        <div className='flex items-center'>
          <span className='mr-4 flex-shrink-0 rounded-full bg-primary/20 p-3 text-primary'>
            <ChatOutlineIcon className='h-6 w-6' />
          </span>
          <h3 className='text-xl font-semibold'>Apply Now</h3>
        </div>
      </div>
      <div className='p-6'>
        <div className='text-center'>
          <p className='mb-6 text-lg font-medium text-gray-700'>
            Ready to join our team?
          </p>
          <Button
            size='lg'
            className='mx-auto block w-full max-w-md bg-primary text-white transition-colors hover:bg-primary/90'
            onClick={onClick}
          >
            Apply for this Position
          </Button>
        </div>
      </div>
    </div>
  )
}

// Job Details Info Component
const JobDetailsInfo = ({ jobPosting }: { jobPosting: JobPosting }) => {
  return (
    <div className='overflow-hidden rounded-xl border border-zinc-100 bg-white shadow-sm'>
      <div className='border-b border-zinc-100 bg-primary/5 p-6'>
        <div className='flex items-center'>
          <span className='mr-4 flex-shrink-0 rounded-full bg-primary/20 p-3 text-primary'>
            <SpeedoMeterIcon className='h-6 w-6' />
          </span>
          <h3 className='text-xl font-semibold'>Job Details</h3>
        </div>
      </div>
      <div className='p-6'>
        <div className='space-y-4'>
          {jobPosting.jobPosition?.jobCode && (
            <div className='flex items-center justify-between rounded-lg bg-zinc-50 p-3'>
              <span className='text-gray-600'>Job Code</span>
              <span className='font-medium'>
                {jobPosting.jobPosition.jobCode}
              </span>
            </div>
          )}

          <div className='flex items-center justify-between rounded-lg bg-zinc-50 p-3'>
            <span className='text-gray-600'>Status</span>
            <span className='flex items-center font-medium'>
              <span className='mr-2 h-2 w-2 rounded-full bg-green-400'></span>
              {jobPosting.jobPostingStatus?.name || "Published"}
            </span>
          </div>

          {jobPosting.shouldKeepOpenUntilStopped !== undefined && (
            <div className='flex items-center justify-between rounded-lg bg-zinc-50 p-3'>
              <span className='text-gray-600'>Posting Type</span>
              <span className='font-medium'>
                {jobPosting.shouldKeepOpenUntilStopped
                  ? "Open Until Filled"
                  : "Fixed Duration"}
              </span>
            </div>
          )}

          <div className='flex items-center justify-between rounded-lg bg-zinc-50 p-3'>
            <span className='text-gray-600'>Posted</span>
            <span className='font-medium'>
              {new Date(jobPosting.createdDate).toLocaleDateString("en-US", {
                year: "numeric",
                month: "short",
                day: "numeric",
              })}
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}

// Quick Actions Component
const QuickActions = ({ router }: { router: ReturnType<typeof useRouter> }) => {
  return (
    <div className='overflow-hidden rounded-xl border border-zinc-100 bg-white shadow-sm'>
      <div className='border-b border-zinc-100 bg-primary/5 p-6'>
        <div className='flex items-center'>
          <span className='mr-4 flex-shrink-0 rounded-full bg-primary/20 p-3 text-primary'>
            <GlobalArrowRightIcon className='h-6 w-6' />
          </span>
          <h3 className='text-xl font-semibold'>Quick Actions</h3>
        </div>
      </div>
      <div className='p-6'>
        <div className='space-y-3'>
          <button
            onClick={() => router.push("/job-openings")}
            className='group flex w-full items-center justify-between rounded-lg border border-zinc-200 p-4 transition-colors hover:bg-zinc-50'
          >
            <span className='font-medium transition-colors group-hover:text-primary'>
              View All Openings
            </span>
            <GlobalArrowRightIcon className='h-5 w-5 transition-transform group-hover:translate-x-1' />
          </button>

          <button
            onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}
            className='group flex w-full items-center justify-between rounded-lg border border-zinc-200 p-4 transition-colors hover:bg-zinc-50'
          >
            <span className='font-medium transition-colors group-hover:text-primary'>
              Back to Top
            </span>
            <GlobalArrowRightIcon className='h-5 w-5 rotate-[-90deg] transition-transform group-hover:translate-y-[-4px]' />
          </button>
        </div>
      </div>
    </div>
  )
}

const JobDetailsScreen = () => {
  const params = useParams()
  const router = useRouter()
  const [isModalOpen, setIsModalOpen] = useState(false)

  const jobId = typeof params?.id === "string" ? parseInt(params?.id, 10) : 0

  // Use React Query to fetch job posting
  const {
    data: jobPosting,
    isLoading,
    isError,
  } = useQuery({
    queryKey: [QUERY_KEYS.jobDetails, jobId],
    queryFn: () => fetchJobPosting(jobId),
    enabled: !!jobId && !isNaN(jobId),
  })

  if (isLoading) {
    return (
      <div className='flex min-h-screen items-center justify-center'>
        <Loader />
      </div>
    )
  }

  if (isError || !jobPosting) {
    return (
      <div className='flex min-h-screen flex-col items-center justify-center'>
        <h1 className='mb-4 text-2xl font-bold'>Job Not Found</h1>
        <Button onClick={() => router.push("/job-openings")}>
          Back to Job Listings
        </Button>
      </div>
    )
  }

  const formattedJob = formatJobPosting(jobPosting)

  return (
    <main>
      <PageHeader secondLine='JOB DETAILS' />

      <SectionWrapper>
        <div className='grid grid-cols-1 gap-6 lg:grid-cols-3'>
          <div className='space-y-6 lg:col-span-2'>
            {/* Combined header and details card */}
            <div>
              <JobHeaderCard
                formattedJob={formattedJob}
                jobPosting={jobPosting}
              />
              <JobDetailsCard formattedJob={formattedJob} />
            </div>

            <JobDescriptionSection description={formattedJob.description} />
            <JobRequirementsList requirements={formattedJob.requirements} />
          </div>

          <div className='lg:col-span-1'>
            <div className='sticky top-24 space-y-6'>
              <ApplyButton onClick={() => setIsModalOpen(true)} />
              <JobDetailsInfo jobPosting={jobPosting} />
              <QuickActions router={router} />
            </div>
          </div>
        </div>
      </SectionWrapper>

      <JobApplicationModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        jobPosting={jobPosting}
      />
    </main>
  )
}

export default JobDetailsScreen
