import { QuickActionCards } from "@/app/shared/components"
import { AboutNexmove } from "@/containers/home-page/about-nexmove"
import { CustomerTestimonials } from "@/containers/home-page/customer-testimonials"
import { GoogleReviews } from "@/containers/home-page/google-reviews"
import { <PERSON> } from "@/containers/home-page/hero"
import { HowToStart } from "@/containers/home-page/how-to-start"
import { OurPartnersMarquee } from "@/containers/home-page/our-partners-marquee"
import { OurServices } from "@/containers/home-page/our-services"
import { ValueAddedService } from "@/containers/home-page/value-added-service"
import { WhyChooseNexMove } from "@/containers/home-page/why-choose-nexmove"

const HomeScreen = () => {
  return (
    <main>
      <Hero />
      {/* <FeatureHighlights /> */}
      <OurServices />
      <WhyChooseNexMove />
      <ValueAddedService />
      <HowToStart />
      <AboutNexmove />
      <CustomerTestimonials />
      <GoogleReviews />
      <OurPartnersMarquee />
      <QuickActionCards />
    </main>
  )
}

export default HomeScreen
