import {
  In<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>er,
  QuickActionC<PERSON>,
} from "@/app/shared/components"
import { CurrentOpening } from "@/containers/job-openings-page/current-openings"
import { EmployeeTestimonials } from "@/containers/job-openings-page/employee-testimonials"
import { HowToApply } from "@/containers/job-openings-page/how-to-apply"
import { WhyWorkWithNex } from "@/containers/job-openings-page/why-work-with-nex"

const JobOpeningsScreen = () => {
  return (
    <main>
      <PageHeader secondLine='JOB OPENINGS' />
      <IntroText
        subtitle='Join the Nex Move Team!'
        description="Are you passionate about logistics, customer service, or innovative solutions? At Nex Move, we're always looking for talented individuals to join our team and help us deliver exceptional relocation and logistics services to our customers."
      />
      <CurrentOpening />
      <WhyWorkWithNex />
      <HowToApply />
      <EmployeeTestimonials />

      <div>
        <QuickActionCards />
      </div>
    </main>
  )
}

export default JobOpeningsScreen
