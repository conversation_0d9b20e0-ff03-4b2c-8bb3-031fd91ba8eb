import {
  In<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>eader,
  QuickActionC<PERSON>,
} from "@/app/shared/components"
import { OurServices } from "@/containers/services-and-pricing/our-services"
import { OurPricing } from "@/containers/services-and-pricing/our-pricing"
import { Wrapper } from "@/app/shared/components/Wrapper"

const ServicesAndPricingScreen = () => {
  return (
    <main>
      <PageHeader secondLine='SERVICES & PRICING' />
      <IntroText
        subtitle='One stop shop for all express service'
        description='At Nex Move, we provide a wide range of relocation and logistics services designed to meet your needs with efficiency and reliability. Explore our offerings and transparent pricing to find the perfect solution for you.'
      />
      <OurServices />
      <OurPricing />
      <Wrapper className='~text-xl/3xl ~mb-5/10 max-sm:!leading-tight'>
        Experience hassle-free relocation and logistics with Nex Move. Your
        satisfaction is our priority!
      </Wrapper>
      <QuickActionCards />
    </main>
  )
}

export default ServicesAndPricingScreen
