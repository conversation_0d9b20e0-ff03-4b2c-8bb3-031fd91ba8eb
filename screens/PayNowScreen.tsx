"use client"
import { TextBanner } from "@/app/components/common"
import PayNowForm from "@/app/components/forms/PayNowForm"
import { PayNowIntro } from "@/containers/pay-now-page/intro"
import { NeedAssistance } from "@/containers/pay-now-page/need-assistance"
import { PaymentInstructions } from "@/containers/pay-now-page/payment-instructions"
import { PaymentOptions } from "@/containers/pay-now-page/payment-options"
import { useSearchParams } from "next/navigation"
import { useEffect, useState } from "react"
import { toast } from "sonner"

const PayNowScreen = () => {
  const searchParams = useSearchParams()
  const payId = searchParams?.get("pay-id")
  const [statusOpen, setStatusOpen] = useState(false)
  const [statusType, setStatusType] = useState<"success" | "failed" | null>(
    null,
  )
  const [paymentMeta, setPaymentMeta] = useState<{
    paymentId?: string
    linkId?: string
  }>({})

  useEffect(() => {
    if (!payId) {
      toast.error(
        "Missing payment link ID. Please use a valid pay-now link ID.",
      )
    }
  }, [payId])

  // Detect payment status from callback URL params (Razorpay Payment Links)
  useEffect(() => {
    const linkStatus =
      searchParams?.get("razorpay_payment_link_status") ||
      searchParams?.get("status") ||
      searchParams?.get("payment_status") ||
      ""

    const paymentId = searchParams?.get("razorpay_payment_id") || ""
    const linkId =
      searchParams?.get("razorpay_payment_link_id") ||
      searchParams?.get("pay-id") ||
      ""

    const norm = linkStatus.toLowerCase()
    const isSuccess = ["paid", "success", "successful"].includes(norm)
    const isFailed = [
      "failed",
      "cancelled",
      "canceled",
      "cancelled_by_user",
      "error",
    ].includes(norm)

    if (isSuccess || isFailed || (!!paymentId && !norm)) {
      setStatusType(isSuccess || (!!paymentId && !norm) ? "success" : "failed")
      setPaymentMeta({ paymentId, linkId })
      setStatusOpen(true)

      // Clean status params from URL, preserve pay-id when present
      try {
        const url = new URL(window.location.href)
        ;[
          "razorpay_payment_link_status",
          "status",
          "payment_status",
          "razorpay_payment_id",
          "razorpay_payment_link_id",
          "razorpay_signature",
        ].forEach((p) => url.searchParams.delete(p))
        window.history.replaceState({}, "", url.toString())
      } catch {}
    }
  }, [searchParams])

  const isSuccess = statusType === "success"

  return (
    <main>
      <PayNowIntro />
      <PaymentInstructions />
      <PaymentOptions />
      <PayNowForm />
      <NeedAssistance />
      <TextBanner text='Thank you for trusting Nex Move. We look forward to serving you!' />
      {statusOpen && statusType && (
        <div className='fixed inset-0 z-50 flex items-center justify-center'>
          <div
            className='absolute inset-0 bg-black/40'
            onClick={() => setStatusOpen(false)}
          />
          <div className='relative z-10 w-[90%] max-w-md rounded-lg bg-white p-6 shadow-xl'>
            <div className='flex flex-col items-center gap-3 text-center'>
              <div
                className={`flex h-16 w-16 items-center justify-center rounded-full ${isSuccess ? "bg-green-100" : "bg-red-100"}`}
              >
                <span
                  className={`text-3xl ${isSuccess ? "text-green-600" : "text-red-600"}`}
                >
                  {isSuccess ? "✓" : "✕"}
                </span>
              </div>
              <h3 className='mt-2 text-xl font-semibold'>
                {isSuccess ? "Payment Successful" : "Payment Failed"}
              </h3>
              {paymentMeta.paymentId && (
                <p className='text-sm text-gray-600'>
                  Payment ID:{" "}
                  <span className='font-medium'>{paymentMeta.paymentId}</span>
                </p>
              )}
              {paymentMeta.linkId && (
                <p className='text-sm text-gray-600'>
                  Link ID:{" "}
                  <span className='font-medium'>{paymentMeta.linkId}</span>
                </p>
              )}
              <div className='mt-2 text-sm text-gray-700'>
                {isSuccess
                  ? "Thank you! Your payment has been received."
                  : "We couldn't complete your payment. You can try again using the same link or contact support."}
              </div>
              <button
                onClick={() => setStatusOpen(false)}
                className={`mt-4 rounded px-4 py-2 text-sm font-medium text-white ${isSuccess ? "bg-green-600 hover:bg-green-700" : "bg-red-600 hover:bg-red-700"}`}
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </main>
  )
}

export default PayNowScreen
