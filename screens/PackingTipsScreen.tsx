import {
  Intro<PERSON>ex<PERSON>,
  PageHeader,
  QuickActionCards,
} from "@/app/shared/components"
import { PackingNeeds } from "@/containers/packing-tips-page/packing-needs"
import { PackingTips } from "@/containers/packing-tips-page/packing-tips"

const PackingTipsScreen = () => {
  return (
    <main>
      <PageHeader
        firstLine='PACKING TIPS FOR A'
        secondLine='STRESS-FREE MOVE - BY NEX MOVE'
      />
      <IntroText
        subtitle='MOVING CHECKLIST FOR A STRESS-FREE RELOCATION'
        description="Packing is a critical part of any relocation, and with the right tips, you can make the process smooth and hassle-free. Whether you're preparing for a household or office move, these expert packing tips from NEX Move will help you stay organized and protect your belongings."
      />
      <PackingTips />
      <PackingNeeds />
      <div className='~pt-8/10'>
        <IntroText description='Ready to pack and move? Let NEX Move make it easy! Contact us today to book our expert packing services and experience a hassle-free relocation.' />
        <QuickActionCards />
      </div>
    </main>
  )
}

export default PackingTipsScreen
