import { <PERSON>Header, QuickActionC<PERSON> } from "@/app/shared/components"
import { CertificatesAndAffiliations } from "@/containers/about-us-page/certificates-and-affiliations"
import { IndustriesServed } from "@/containers/about-us-page/industries-served"
import { MessageFromCeo } from "@/containers/about-us-page/message-from-ceo"
import { OurStory } from "@/containers/about-us-page/our-story"
import { OurValues } from "@/containers/about-us-page/our-values"
import { WhatWeOffer } from "@/containers/about-us-page/what-we-offer"
import { WhoWeAre } from "@/containers/about-us-page/who-we-are"

const AboutUs = () => {
  return (
    <main>
      <PageHeader firstLine='About' secondLine='NexMove' />
      <WhoWeAre />
      <OurStory />
      <OurValues />
      <WhatWeOffer />
      <MessageFromCeo />
      <IndustriesServed />
      <CertificatesAndAffiliations />
      <QuickActionCards />
    </main>
  )
}

export default AboutUs
