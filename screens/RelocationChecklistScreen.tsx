import {
  Intro<PERSON>ex<PERSON>,
  <PERSON>Header,
  QuickActionCards,
} from "@/app/shared/components"
import { OurProcess } from "@/containers/relocation-checklist-page/our-process"
import { PackerAndMovers } from "@/containers/relocation-checklist-page/packers-and-movers"

const RelocationChecklistScreen = () => {
  return (
    <main>
      <PageHeader
        firstLine='MOVING CHECKLIST FOR'
        secondLine='A STRESS-FREE RELOCATION'
      />
      <IntroText
        subtitle='MOVING CHECKLIST FOR A STRESS-FREE RELOCATION'
        description='Planning a house or office move? NEX Packers & Movers has you covered! Use our comprehensive moving checklist to ensure a smooth and hassle-free relocation.'
      />
      <OurProcess />
      <PackerAndMovers />
      <div className='~pt-8/10'>
        <IntroText
          description='Ready to make your move? Contact NEX Packers & Movers today for a seamless relocation experience!'
          className='~pb-2/3'
        />
        <QuickActionCards />
      </div>
    </main>
  )
}

export default RelocationChecklistScreen
