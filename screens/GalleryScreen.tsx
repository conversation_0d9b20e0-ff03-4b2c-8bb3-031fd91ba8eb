import { TextBanner } from "@/app/components/common"
import BannerMedia from "@/containers/gallery-page/banner-media/BannerMedia"
import CompanyMedia from "@/containers/gallery-page/company-media/CompanyMedia"
import CustomerMedia from "@/containers/gallery-page/customer-media/CustomerMedia"
import CustomerMediaSection from "@/containers/gallery-page/customer-media/CustomerMedia"
import FleetMedia from "@/containers/gallery-page/fleet-media/FleetMedia"
import { Gallery } from "@/containers/gallery-page/gallery-section"
import { GalleryIntro } from "@/containers/gallery-page/intro"
import { PackingProcess } from "@/containers/gallery-page/packing-process"
import TeamInAction from "@/containers/gallery-page/team-in-action/TeamInAction"

const GalleryScreen = () => {
  return (
    <main>
      <GalleryIntro />
      <TeamInAction />
      <PackingProcess />
      <CustomerMedia />
      <FleetMedia />
      <BannerMedia />
      <CompanyMedia />
      {/* <Gallery /> */}
      <TextBanner
        text='Thank you for choosing Nex Move. We appreciate your understanding and are here to assist with any questions or concerns regarding our refund and cancellation policy.'
        className='mx-auto max-w-screen-lg'
      />
    </main>
  )
}

export default GalleryScreen
