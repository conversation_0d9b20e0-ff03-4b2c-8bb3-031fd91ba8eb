"use client"

import { createContext, use<PERSON>ontext, useEffect, useState } from "react"
import {
  createUserService,
  User,
  verifyPhoneService,
} from "../app/shared/services/quote-calculator"

export type TAuthContext = {
  user: User | null
  register: (user: {
    name: string
    email: string
    phone: string
    service: string
  }) => Promise<void>
  logout: () => void
  verifyPhone: (otp: string) => Promise<void>
  isLoading: boolean
}

export const AuthContext = createContext<TAuthContext>({
  user: null,
  register: async () => {},
  logout: () => {},
  verifyPhone: async () => {},
  isLoading: false,
})
const LOCAL_STORAGE_KEY = "userDataKey"

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  const register = async (user: {
    name: string
    email: string
    phone: string
    service: string
  }) => {
    try {
      setIsLoading(true)
      const response = await createUserService(user)
      setUser(response)
      localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(response))
    } catch (error) {
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const logout = () => {
    localStorage.removeItem(LOCAL_STORAGE_KEY)
    setUser(null)
  }

  const verifyPhone = async (otp: string) => {
    const phone_no = user?.phone
    if (!phone_no) {
      throw { message: "Phone number not found", status: 400 }
    }

    try {
      setIsLoading(true)
      const response = await verifyPhoneService({
        phone: Number(phone_no),
        code: Number(otp),
      })
      setUser(response)
      localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(response))
    } catch (error) {
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    const user = JSON.parse(localStorage.getItem(LOCAL_STORAGE_KEY) || "null")
    if (user) {
      setUser(user)
    }
  }, [])

  return (
    <AuthContext.Provider
      value={{ user, register, logout, verifyPhone, isLoading }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
