// -------------------------------------------------------------------------
// Extracts the state code (e.g., "CA", "NY") from a Google Maps PlaceResult

export const extractStateCode = (
  location: google.maps.places.PlaceResult | null,
): string => {
  if (!location || !location.address_components) return ""

  const stateComponent = location.address_components.find((component) =>
    component.types.includes("administrative_area_level_1"),
  )

  return stateComponent ? stateComponent.short_name : ""
}

//-------------------------------------------------------------------------
// Calculates the distance between two locations using the Google Maps API

export const calculateDistance = (
  pickupLocation: google.maps.places.PlaceResult,
  dropoffLocation: google.maps.places.PlaceResult,
): Promise<string | null> => {
  return new Promise((resolve) => {
    if (!pickupLocation || !dropoffLocation) {
      resolve(null)
      return
    }

    const pickupPlace = pickupLocation.geometry?.location
    const dropoffPlace = dropoffLocation.geometry?.location

    if (!pickupPlace || !dropoffPlace) {
      resolve(null)
      return
    }

    const service = new google.maps.DistanceMatrixService()
    service.getDistanceMatrix(
      {
        origins: [pickupPlace],
        destinations: [dropoffPlace],
        travelMode: google.maps.TravelMode.DRIVING,
      },
      (response, status) => {
        if (status === google.maps.DistanceMatrixStatus.OK && response) {
          const result = response.rows[0].elements[0]

          if (result.status === google.maps.DistanceMatrixElementStatus.OK) {
            resolve(result.distance.text)
          } else {
            resolve(null)
          }
        } else {
          resolve(null)
        }
      },
    )
  })
}

// -------------------------------------------------------------------------
// Extracts the city name from a Google Maps PlaceResult

export const extractCityName = (
  location: google.maps.places.PlaceResult | null,
): string => {
  if (!location || !location.address_components) return ""

  // Find the locality component which represents the city
  const cityComponent = location.address_components.find((component) =>
    component.types.includes("locality"),
  )

  // Return the city name or empty string if not found
  return cityComponent ? cityComponent.long_name : ""
}
