import { uploadFile } from "@/app/shared/services/file-upload"
import {
  MAR<PERSON><PERSON>LACE_CATEGORIES,
  MOCK_MARKETPLACE_PRODUCTS,
  MOCK_ORDERS,
  MOCK_PRODUCT_CATEGORIES,
  MOCK_PRODUCTS,
  MOCK_SELLER_STATS,
  TMarketplaceCategory,
  TMarketplaceProduct,
  TOrder,
  TProduct,
  TProductCategory,
  TSellerStats,
} from "@/config/seller-dashboard"

// Simulate API delay
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

export class SellerDashboardService {
  // Product services
  static async getProducts(): Promise<TProduct[]> {
    await delay(500)
    return MOCK_PRODUCTS
  }

  static async getProduct(id: string): Promise<TProduct | null> {
    await delay(300)
    return MOCK_PRODUCTS.find((product) => product.id === id) || null
  }

  static async createProduct(
    productData: Omit<
      TProduct,
      "id" | "createdAt" | "updatedAt" | "views" | "sales"
    >,
  ): Promise<TProduct> {
    await delay(800)
    const newProduct: TProduct = {
      ...productData,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      views: 0,
      sales: 0,
    }
    return newProduct
  }

  static async updateProduct(
    id: string,
    updates: Partial<TProduct>,
  ): Promise<TProduct> {
    await delay(600)
    const product = MOCK_PRODUCTS.find((p) => p.id === id)
    if (!product) {
      throw new Error("Product not found")
    }
    return {
      ...product,
      ...updates,
      updatedAt: new Date().toISOString(),
    }
  }

  static async deleteProduct(id: string): Promise<boolean> {
    await delay(400)
    const index = MOCK_PRODUCTS.findIndex((p) => p.id === id)
    if (index === -1) {
      throw new Error("Product not found")
    }
    return true
  }

  static async uploadProductImages(files: File[]): Promise<string[]> {
    await delay(1200)
    // Simulate image upload and return URLs
    return files.map(
      (_, index) => `/uploaded-images/product-${Date.now()}-${index}.jpg`,
    )
  }

  // Order services
  static async getOrders(): Promise<TOrder[]> {
    await delay(500)
    return MOCK_ORDERS
  }

  static async getOrder(id: string): Promise<TOrder | null> {
    await delay(300)
    return MOCK_ORDERS.find((order) => order.id === id) || null
  }

  static async updateOrderStatus(
    id: string,
    status: TOrder["status"],
  ): Promise<TOrder> {
    await delay(600)
    const order = MOCK_ORDERS.find((o) => o.id === id)
    if (!order) {
      throw new Error("Order not found")
    }
    return {
      ...order,
      status,
    }
  }

  // Analytics services
  static async getSellerStats(): Promise<TSellerStats> {
    await delay(400)
    return MOCK_SELLER_STATS
  }

  static async getRevenueData(
    period: "week" | "month" | "year",
  ): Promise<{ date: string; revenue: number }[]> {
    await delay(600)

    // Mock revenue data
    const mockData = {
      week: [
        { date: "2025-06-19", revenue: 15000 },
        { date: "2025-06-20", revenue: 22000 },
        { date: "2025-06-21", revenue: 18000 },
        { date: "2025-06-22", revenue: 25000 },
        { date: "2025-06-23", revenue: 30000 },
        { date: "2025-06-24", revenue: 28000 },
        { date: "2025-06-25", revenue: 32000 },
      ],
      month: Array.from({ length: 30 }, (_, i) => ({
        date: `2025-06-${String(i + 1).padStart(2, "0")}`,
        revenue: Math.floor(Math.random() * 40000) + 10000,
      })),
      year: Array.from({ length: 12 }, (_, i) => ({
        date: `2025-${String(i + 1).padStart(2, "0")}`,
        revenue: Math.floor(Math.random() * 500000) + 100000,
      })),
    }

    return mockData[period]
  }

  static async getTopProducts(): Promise<TProduct[]> {
    await delay(400)
    return MOCK_PRODUCTS.filter((p) => p.status === "active")
      .sort((a, b) => b.sales - a.sales)
      .slice(0, 5)
  }

  // Category services
  static async getProductCategories(): Promise<TProductCategory[]> {
    await delay(300)
    return MOCK_PRODUCT_CATEGORIES
  }

  // Marketplace Product services
  static async getMarketplaceProducts(): Promise<TMarketplaceProduct[]> {
    await delay(500)
    return MOCK_MARKETPLACE_PRODUCTS
  }

  static async getMarketplaceProduct(
    id: string,
  ): Promise<TMarketplaceProduct | null> {
    await delay(300)
    return (
      MOCK_MARKETPLACE_PRODUCTS.find((product) => product.id === id) || null
    )
  }

  static async createMarketplaceProduct(
    productData: Omit<TMarketplaceProduct, "id">,
  ): Promise<TMarketplaceProduct> {
    await delay(800)
    const newProduct: TMarketplaceProduct = {
      ...productData,
      id: `mp-${Date.now()}`,
    }
    return newProduct
  }

  static async updateMarketplaceProduct(
    id: string,
    updates: Partial<TMarketplaceProduct>,
  ): Promise<TMarketplaceProduct> {
    await delay(600)
    const product = MOCK_MARKETPLACE_PRODUCTS.find((p) => p.id === id)
    if (!product) {
      throw new Error("Marketplace product not found")
    }
    return {
      ...product,
      ...updates,
    }
  }

  static async deleteMarketplaceProduct(id: string): Promise<boolean> {
    await delay(400)
    const index = MOCK_MARKETPLACE_PRODUCTS.findIndex((p) => p.id === id)
    if (index === -1) {
      throw new Error("Marketplace product not found")
    }
    return true
  }

  static async uploadMarketplacePictures(
    files: File[],
    category: TMarketplaceCategory["id"],
  ): Promise<string[]> {
    await delay(1200)

    // Get minimum required images for category
    const categoryConfig = MARKETPLACE_CATEGORIES.find(
      (cat) => cat.id === category,
    )
    const minimumImages = categoryConfig?.minimumImages || 4

    if (files.length < minimumImages) {
      throw new Error(
        `At least ${minimumImages} images are required for ${categoryConfig?.name || "this category"}`,
      )
    }

    // Validate file types and sizes
    for (const file of files) {
      if (!file.type.startsWith("image/")) {
        throw new Error(`File ${file.name} is not a valid image`)
      }
      if (file.size > 5 * 1024 * 1024) {
        throw new Error(`File ${file.name} is too large. Maximum size is 5MB`)
      }
    }

    try {
      // Upload files using the existing uploadFile service
      const uploadPromises = files.map((file) =>
        uploadFile(file, "marketplace"),
      )
      const uploadedUrls = await Promise.all(uploadPromises)
      return uploadedUrls
    } catch (error) {
      console.error("Error uploading marketplace pictures:", error)
      throw new Error("Failed to upload pictures. Please try again.")
    }
  }

  static async getMarketplaceCategories(): Promise<TMarketplaceCategory[]> {
    await delay(200)
    return MARKETPLACE_CATEGORIES
  }

  // Dashboard summary
  static async getDashboardSummary(): Promise<{
    stats: TSellerStats
    recentOrders: TOrder[]
    topProducts: TProduct[]
  }> {
    await delay(800)

    const [stats, orders, products] = await Promise.all([
      this.getSellerStats(),
      this.getOrders(),
      this.getTopProducts(),
    ])

    return {
      stats,
      recentOrders: orders.slice(0, 5),
      topProducts: products,
    }
  }
}

// Export for easier imports
export const {
  getProducts,
  getProduct,
  createProduct,
  updateProduct,
  deleteProduct,
  uploadProductImages,
  getOrders,
  getOrder,
  updateOrderStatus,
  getSellerStats,
  getRevenueData,
  getTopProducts,
  getProductCategories,
  getMarketplaceProducts,
  getMarketplaceProduct,
  createMarketplaceProduct,
  updateMarketplaceProduct,
  deleteMarketplaceProduct,
  uploadMarketplacePictures,
  getMarketplaceCategories,
  getDashboardSummary,
} = SellerDashboardService
