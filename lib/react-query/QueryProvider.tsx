"use client"

import { QueryClientProvider } from "@tanstack/react-query"
import { useState } from "react"
import { queryClient } from "./queryClient"

const QueryProvider = ({ children }: { children: React.ReactNode }) => {
  const [queryClientInstance] = useState(queryClient)
  return (
    <QueryClientProvider client={queryClientInstance}>
      {children}
    </QueryClientProvider>
  )
}

export default QueryProvider
