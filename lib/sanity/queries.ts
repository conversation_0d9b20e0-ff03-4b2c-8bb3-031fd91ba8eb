export const getAllPostsMinimalQuery = `*[_type == "post"] | order(publishedAt desc) {
  _id,
  title,
  small_description,
  slug,
  mainImage,
  "author": {
    "name": author.name,
    "profile": author.profileImage
  },
  isTrending,
  readingTime,
  publishedAt
}`

export const getPostBySlugQuery = `*[_type == "post" && slug.current == $slug][0] {
  _id,
  title,
  small_description,
  body,
  mainImage,
  publishedAt,
  "author": {
    "name": author.name,
    "profile": author.profileImage
  },
  isTrending,
  readingTime,
}`
