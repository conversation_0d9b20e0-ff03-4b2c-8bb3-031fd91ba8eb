import { PortableTextBlock } from "next-sanity"

export type PostSummary = {
  _id: string
  title: string
  slug: { current: string }
  mainImage: string
  small_description: string
  readingTime: string
  isTrending: string
  author: {
    name: string
    profile?: string
  }
  publishedAt: string
}

export type Post = {
  _id: string
  title: string
  slug: { current: string }
  mainImage: string
  small_description: string
  readingTime: string
  isTrending: string
  author: {
    name: string
    profile?: string
  }
  publishedAt: string
  body: PortableTextBlock[]
}
