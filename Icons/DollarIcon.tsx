import React from "react"

const DollarIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width='36'
    height='36'
    viewBox='0 0 36 36'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <path
      d='M18 0.5C14.5388 0.5 11.1554 1.52636 8.27753 3.44928C5.39967 5.37221 3.15665 8.10533 1.83212 11.303C0.507582 14.5007 0.161024 18.0194 0.836265 21.4141C1.51151 24.8087 3.17822 27.9269 5.62564 30.3744C8.07306 32.8218 11.1913 34.4885 14.5859 35.1637C17.9806 35.839 21.4993 35.4924 24.697 34.1679C27.8947 32.8434 30.6278 30.6003 32.5507 27.7225C34.4737 24.8446 35.5 21.4612 35.5 18C35.495 13.3602 33.6497 8.91192 30.3689 5.63111C27.0881 2.3503 22.6398 0.504963 18 0.5ZM18 33C15.0333 33 12.1332 32.1203 9.66645 30.472C7.19972 28.8238 5.27713 26.4811 4.14181 23.7403C3.0065 20.9994 2.70945 17.9834 3.28823 15.0736C3.86701 12.1639 5.29562 9.49119 7.39341 7.3934C9.49119 5.29561 12.1639 3.867 15.0737 3.28822C17.9834 2.70944 20.9994 3.00649 23.7403 4.14181C26.4812 5.27712 28.8238 7.19971 30.4721 9.66645C32.1203 12.1332 33 15.0333 33 18C32.9957 21.9769 31.414 25.7897 28.6019 28.6019C25.7897 31.414 21.9769 32.9957 18 33Z'
      fill='black'
    />
    <path
      d='M21.125 16.75H19.25V13H23C23.3315 13 23.6495 12.8683 23.8839 12.6339C24.1183 12.3995 24.25 12.0815 24.25 11.75C24.25 11.4185 24.1183 11.1005 23.8839 10.8661C23.6495 10.6317 23.3315 10.5 23 10.5H19.25V8C19.25 7.66848 19.1183 7.35054 18.8839 7.11612C18.6495 6.8817 18.3315 6.75 18 6.75C17.6685 6.75 17.3505 6.8817 17.1161 7.11612C16.8817 7.35054 16.75 7.66848 16.75 8V10.5H14.875C13.7147 10.5 12.6019 10.9609 11.7814 11.7814C10.9609 12.6019 10.5 13.7147 10.5 14.875C10.5 16.0353 10.9609 17.1481 11.7814 17.9686C12.6019 18.7891 13.7147 19.25 14.875 19.25H16.75V23H13C12.6685 23 12.3505 23.1317 12.1161 23.3661C11.8817 23.6005 11.75 23.9185 11.75 24.25C11.75 24.5815 11.8817 24.8995 12.1161 25.1339C12.3505 25.3683 12.6685 25.5 13 25.5H16.75V28C16.75 28.3315 16.8817 28.6495 17.1161 28.8839C17.3505 29.1183 17.6685 29.25 18 29.25C18.3315 29.25 18.6495 29.1183 18.8839 28.8839C19.1183 28.6495 19.25 28.3315 19.25 28V25.5H21.125C22.2853 25.5 23.3981 25.0391 24.2186 24.2186C25.0391 23.3981 25.5 22.2853 25.5 21.125C25.5 19.9647 25.0391 18.8519 24.2186 18.0314C23.3981 17.2109 22.2853 16.75 21.125 16.75ZM16.75 16.75H14.875C14.3777 16.75 13.9008 16.5525 13.5492 16.2008C13.1975 15.8492 13 15.3723 13 14.875C13 14.3777 13.1975 13.9008 13.5492 13.5492C13.9008 13.1975 14.3777 13 14.875 13H16.75V16.75ZM21.125 23H19.25V19.25H21.125C21.6223 19.25 22.0992 19.4475 22.4508 19.7992C22.8025 20.1508 23 20.6277 23 21.125C23 21.6223 22.8025 22.0992 22.4508 22.4508C22.0992 22.8025 21.6223 23 21.125 23Z'
      fill='#FF5B00'
    />
  </svg>
)

export default DollarIcon
