import React from "react"

const EmailIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='28'
    height='28'
    viewBox='0 0 33 33'
    fill='none'
    {...props}
  >
    <g clipPath='url(#clip0_875_35342)'>
      <path
        d='M33.041 13.9516C33.041 13.9516 33.0409 13.9516 33.0408 13.9515C33.04 13.9507 33.0393 13.95 33.0385 13.9492C33.0385 13.9492 33.0385 13.9492 33.0385 13.9492C33.0342 13.945 33.0298 13.9408 33.0253 13.9366C33.0241 13.9355 33.0229 13.9343 33.0217 13.9332C33.0193 13.931 33.0169 13.9288 33.0145 13.9266C33.0145 13.9266 33.0145 13.9265 33.0144 13.9264C33.0133 13.9254 33.0122 13.9244 33.0111 13.9234C33.0109 13.9234 33.0108 13.9232 33.0107 13.9231C33.0106 13.923 33.0104 13.9229 33.0103 13.9227C33.01 13.9225 33.0098 13.9223 33.0095 13.922C33.0091 13.9217 33.0087 13.9213 33.0083 13.9209C33.0081 13.9208 33.0079 13.9206 33.0077 13.9204C33.0075 13.9202 33.0073 13.92 33.007 13.9198C33.0063 13.9191 33.0056 13.9185 33.0049 13.9179C33.0048 13.9178 33.0047 13.9177 33.0046 13.9176C33.0044 13.9174 33.0042 13.9173 33.004 13.9171C33.0038 13.9169 33.0035 13.9167 33.0033 13.9165C33.0033 13.9164 33.0031 13.9163 33.0031 13.9162C33.0023 13.9156 33.0016 13.9149 33.0008 13.9143C33.0008 13.9142 33.0006 13.9141 33.0006 13.914C32.9997 13.9132 32.9988 13.9124 32.9979 13.9117C32.9972 13.9111 32.9965 13.9105 32.9959 13.9099L27.4089 9.07513V0.976562C27.4089 0.43724 26.9716 0 26.4323 0H6.90104C6.36172 0 5.92448 0.43724 5.92448 0.976562V9.07513L0.3375 13.91C0.336849 13.9106 0.336198 13.9112 0.335482 13.9118C0.33457 13.9126 0.333724 13.9133 0.332812 13.9141C0.332747 13.9142 0.332617 13.9143 0.332552 13.9144C0.331836 13.915 0.33112 13.9157 0.330339 13.9163C0.330273 13.9164 0.330208 13.9165 0.330078 13.9166C0.329883 13.9168 0.329622 13.917 0.329427 13.9172C0.329232 13.9173 0.329036 13.9175 0.328841 13.9177C0.328711 13.9178 0.328581 13.9179 0.328516 13.918C0.327799 13.9186 0.327083 13.9193 0.326367 13.9199C0.326107 13.9201 0.325846 13.9203 0.325651 13.9206C0.325521 13.9207 0.325326 13.9208 0.32513 13.921C0.32474 13.9214 0.324349 13.9217 0.323893 13.9221C0.323633 13.9223 0.323372 13.9226 0.323112 13.9229C0.322982 13.923 0.322786 13.9231 0.322656 13.9232C0.322591 13.9233 0.322461 13.9234 0.322331 13.9236C0.321224 13.9246 0.320117 13.9256 0.31901 13.9266C0.31901 13.9266 0.318945 13.9266 0.31888 13.9267C0.316471 13.9289 0.314063 13.9311 0.311719 13.9333C0.310482 13.9344 0.30931 13.9356 0.308073 13.9367C0.303646 13.9409 0.299284 13.9451 0.294922 13.9493C0.294141 13.9501 0.293359 13.9508 0.292578 13.9516C0.292578 13.9516 0.292513 13.9517 0.292448 13.9518C0.10625 14.1344 0.000260417 14.3848 0 14.6473C0 14.648 0 14.6486 0 14.6493V30.4036C0 32.013 1.30964 33.3333 2.92969 33.3333H30.4036C32.02 33.3333 33.3333 32.0166 33.3333 30.4036V14.6493C33.3333 14.6486 33.3333 14.648 33.3333 14.6473C33.3331 14.3848 33.2271 14.1344 33.041 13.9516ZM27.4089 11.6581L30.8663 14.6501L27.4089 17.656V11.6581ZM7.8776 1.95312H25.4557C25.4557 2.58594 25.4557 18.7832 25.4557 19.354L22.243 22.1471L20.5159 20.6253C18.3215 18.7051 15.0118 18.7051 12.8148 20.6275L11.0904 22.1471L7.8776 19.354C7.8776 18.784 7.8776 2.58503 7.8776 1.95312ZM5.92448 11.6581V17.656L2.46699 14.6501L5.92448 11.6581ZM1.95312 30.1984V16.7915L9.61191 23.4497L1.95312 30.1984ZM3.56621 31.3802L14.1034 22.0951C15.5648 20.8165 17.7686 20.8165 19.2272 22.0928L29.7671 31.3802H3.56621ZM31.3802 30.1984L23.7214 23.4497L31.3802 16.7915V30.1984Z'
        fill='black'
      />
      <path
        d='M20 4.6875C15.4768 4.6875 11.7969 8.36742 11.7969 12.8906C11.7969 17.4803 15.5391 21.0959 19.9884 21.0959C21.2372 21.0959 22.5116 20.8143 23.7432 20.2477C24.3312 19.9773 24.5885 19.2813 24.318 18.6934C24.0476 18.1053 23.3516 17.848 22.7637 18.1185C18.3821 20.1341 14.1406 17.0063 14.1406 12.8906C14.1406 9.65977 16.7691 7.03125 20 7.03125C23.2309 7.03125 25.8594 9.65977 25.8594 12.8906C25.8594 13.5368 25.3337 14.0625 24.6875 14.0625C24.0413 14.0625 23.5156 13.5368 23.5156 12.8906C23.5156 10.9521 21.9385 9.375 20 9.375C18.0615 9.375 16.4844 10.9521 16.4844 12.8906C16.4844 14.8291 18.0615 16.4063 20 16.4063C20.8998 16.4063 21.7212 16.0661 22.3438 15.5082C22.9663 16.0662 23.7877 16.4063 24.6875 16.4063C26.626 16.4063 28.2031 14.8291 28.2031 12.8906C28.2031 8.36742 24.5232 4.6875 20 4.6875ZM20 14.0625C19.3538 14.0625 18.8281 13.5368 18.8281 12.8906C18.8281 12.2445 19.3538 11.7188 20 11.7188C20.6462 11.7188 21.1719 12.2445 21.1719 12.8906C21.1719 13.5368 20.6462 14.0625 20 14.0625Z'
        fill='#FF5B00'
      />
    </g>
    <defs>
      <clipPath id='clip0_875_35342'>
        <rect width='33' height='33' fill='white' />
      </clipPath>
    </defs>
  </svg>
)

export default EmailIcon
