import React from "react"

const BoxesIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='40'
    height='36'
    viewBox='0 0 40 36'
    fill='none'
    {...props}
  >
    <path
      d='M2 30.2669V19.924L11.0486 16.0469L20.0971 19.9269V30.2669L11.0486 34.1412L2 30.2669Z'
      stroke='black'
      strokeWidth='2.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M2 19.9043L11.0486 23.7814L20.0971 19.9043'
      stroke='black'
      strokeWidth='2.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M20.0938 19.9043L29.1423 23.7814L38.1909 19.9043'
      stroke='black'
      strokeWidth='2.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M11.0526 16.0415L20.0983 19.9186L29.1469 16.0415M11.0469 23.7872V34.1444M29.1469 23.7872V34.1444M20.0926 30.2672V19.9244L29.1412 16.0472L38.1897 19.9272V30.2672L29.1412 34.1415L20.0926 30.2672Z'
      stroke='black'
      strokeWidth='2.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M20.1406 9.35547V19.8555M20.1406 9.35547L29.1406 5.85547M20.1406 9.35547L11.1406 5.85547M20.1406 19.8555L28.5468 16.1194C28.9079 15.9589 29.1406 15.6008 29.1406 15.2056V5.85547M20.1406 19.8555L11.7345 16.1194C11.3734 15.9589 11.1406 15.6008 11.1406 15.2056V5.85547M29.1406 5.85547L20.5468 2.03597C20.2882 1.92106 19.9931 1.92106 19.7345 2.03597L11.1406 5.85547'
      stroke='#FF5B00'
      strokeWidth='2.5'
      strokeLinecap='round'
    />
  </svg>
)

export default BoxesIcon
