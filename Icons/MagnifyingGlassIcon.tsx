import React from "react"

const MagnifyingGlassIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='40'
    height='40'
    viewBox='0 0 40 40'
    fill='none'
    {...props}
  >
    <path
      d='M36.3268 33.6732L28.9065 26.2498C31.1314 23.3505 32.1701 19.7135 31.812 16.0766C31.4539 12.4396 29.7257 9.07505 26.9782 6.66539C24.2306 4.25574 20.6693 2.98143 17.0167 3.10097C13.3641 3.22051 9.89374 4.72495 7.30959 7.3091C4.72544 9.89325 3.221 13.3636 3.10146 17.0162C2.98192 20.6688 4.25623 24.2301 6.66588 26.9777C9.07554 29.7253 12.4401 31.4534 16.0771 31.8115C19.714 32.1696 23.351 31.1309 26.2503 28.906L33.6768 36.3342C33.8512 36.5086 34.0583 36.6469 34.2862 36.7413C34.5141 36.8357 34.7583 36.8843 35.005 36.8843C35.2516 36.8843 35.4959 36.8357 35.7237 36.7413C35.9516 36.6469 36.1587 36.5086 36.3331 36.3342C36.5075 36.1597 36.6459 35.9527 36.7402 35.7248C36.8346 35.4969 36.8832 35.2527 36.8832 35.006C36.8832 34.7594 36.8346 34.5151 36.7402 34.2873C36.6459 34.0594 36.5075 33.8523 36.3331 33.6779L36.3268 33.6732ZM6.87527 17.4998C6.87527 15.3984 7.49842 13.3441 8.66591 11.5969C9.8334 9.84958 11.4928 8.48775 13.4343 7.68357C15.3757 6.87938 17.5121 6.66897 19.5731 7.07894C21.6342 7.48891 23.5274 8.50084 25.0133 9.98678C26.4992 11.4727 27.5112 13.3659 27.9211 15.427C28.3311 17.488 28.1207 19.6243 27.3165 21.5658C26.5123 23.5073 25.1505 25.1667 23.4032 26.3342C21.6559 27.5016 19.6017 28.1248 17.5003 28.1248C14.6832 28.1219 11.9824 27.0015 9.99046 25.0096C7.99852 23.0177 6.87817 20.3168 6.87527 17.4998Z'
      fill='black'
    />
    <path
      d='M11.4993 13.5C10.166 14.6667 8.29931 18.1 11.4993 22.5'
      stroke='#FF5B00'
      strokeWidth='2.5'
      strokeLinecap='round'
    />
  </svg>
)

export default MagnifyingGlassIcon
