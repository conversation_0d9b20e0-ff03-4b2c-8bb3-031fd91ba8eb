import React from "react"

const PlaneIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='40'
    height='41'
    viewBox='0 0 40 41'
    fill='none'
    {...props}
  >
    <g clipPath='url(#clip0_875_33848)'>
      <path
        d='M35.8346 26.7852H35.8345H23.9879L19.9457 37.5646L19.7024 38.2134H19.0094H17.1423H16.1423V37.2134V26.7852H4.28557C3.64149 26.7852 3.02379 26.5294 2.56835 26.0739C2.11291 25.6185 1.85705 25.0008 1.85705 24.3567V14.357V13.357H2.85705H4.59985H5.38062L5.56999 14.1145L6.80915 19.0711H11.4282H12.4282V20.0711V21.9282H13.2852V20.0711V19.0711H14.2852H18.5708H19.5708V20.0711V21.9282H20.4279V20.0711V19.0711H21.4279H25.7134H26.7134V20.0711V21.9282H27.5705V20.0711V19.0711H28.5705H29.8447H29.8452C30.8028 19.0716 31.7424 19.3321 32.5634 19.8251C32.5635 19.8251 32.5636 19.8252 32.5637 19.8252L37.0197 22.4988L35.8346 26.7852ZM35.8346 26.7852C36.3393 26.7852 36.8301 26.6196 37.2317 26.314C37.6333 26.0083 37.9236 25.5794 38.0581 25.0929C38.1925 24.6065 38.1638 24.0894 37.9763 23.6208C37.7888 23.1525 37.4531 22.7586 37.0204 22.4993L35.8346 26.7852ZM37.4628 21.7643L37.4631 21.7645C38.0567 22.1204 38.5172 22.6612 38.774 23.3039C39.0309 23.9466 39.0699 24.6558 38.8852 25.3228L39.8489 25.5898L38.8852 25.3228C38.7004 25.9899 38.302 26.5779 37.7511 26.9968C37.2001 27.4158 36.527 27.6425 35.8348 27.6423H35.8345H25.2749H24.5819L24.3385 28.2912L20.7498 37.862C20.7497 37.8625 20.7495 37.863 20.7493 37.8635C20.615 38.2171 20.3767 38.5218 20.0657 38.7373C19.7548 38.9527 19.3859 39.0689 19.0076 39.0705H17.1423C16.6498 39.0705 16.1774 38.8748 15.8292 38.5266C15.4809 38.1783 15.2852 37.706 15.2852 37.2134V28.6423V27.6423H14.2852H4.28657C3.41519 27.6414 2.57974 27.2949 1.96357 26.6787C1.34752 26.0627 1.00099 25.2274 1 24.3562V14.357C1 13.8645 1.19565 13.3922 1.54392 13.0439C1.89218 12.6957 2.36453 12.5 2.85705 12.5H4.5997C4.59975 12.5 4.5998 12.5 4.59985 12.5C5.01391 12.5001 5.41606 12.6386 5.7424 12.8934C6.06879 13.1483 6.30061 13.505 6.40103 13.9067L7.28813 17.4565L7.47745 18.2141H8.2583H29.8433H29.8434C30.9574 18.2139 32.0505 18.5167 33.0057 19.09L33.0058 19.0901L37.4628 21.7643Z'
        stroke='black'
        strokeWidth='2'
      />

      <path
        d='M19.5 9.00001H21.1537L24.1537 16.5H26.8463L23.475 8.07126C23.2908 7.6064 22.9708 7.20784 22.5568 6.92755C22.1427 6.64727 21.6538 6.49828 21.1537 6.50002H19.5C18.837 6.50002 18.2011 6.76341 17.7322 7.23225C17.2634 7.70109 17 8.33697 17 9.00001V16.5H19.5V9.00001Z'
        fill='black'
      />
      <path
        d='M13 19.5V22'
        stroke='#FF5B00'
        strokeWidth='3'
        strokeLinecap='round'
      />
      <path
        d='M20 19.5V22'
        stroke='#FF5B00'
        strokeWidth='2.7'
        strokeLinecap='round'
      />
      <path
        d='M20 19.5V22'
        stroke='#FF5B00'
        strokeWidth='3'
        strokeLinecap='round'
      />
      <path
        d='M27.1016 19.5V22'
        stroke='#FF5B00'
        strokeWidth='3'
        strokeLinecap='round'
      />
    </g>
    <defs>
      <clipPath id='clip0_875_33848'>
        <rect
          width='40'
          height='40'
          fill='white'
          transform='translate(0 0.5)'
        />
      </clipPath>
    </defs>
  </svg>
)

export default PlaneIcon
