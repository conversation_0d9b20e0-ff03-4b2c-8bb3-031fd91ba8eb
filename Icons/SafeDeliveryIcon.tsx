import React from "react"

const SafeDeliveryIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='40'
    height='47'
    viewBox='0 0 40 47'
    fill='none'
    {...props}
  >
    <g clipPath='url(#clip0_875_33812)'>
      <path
        d='M0 27.5H4.79003C5.37803 27.5 5.95804 27.632 6.48404 27.888L10.5681 29.864C11.0941 30.118 11.6741 30.25 12.2641 30.25H14.3481C16.3641 30.25 18.0001 31.832 18.0001 33.784C18.0001 33.864 17.9461 33.932 17.8681 33.954L12.7861 35.36C11.8742 35.612 10.9018 35.5238 10.0501 35.112L5.68404 33'
        stroke='black'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M18.0001 32.5002L27.1862 29.6782C27.9859 29.4328 28.8427 29.4463 29.6343 29.7167C30.4259 29.9872 31.1118 30.5007 31.5942 31.1842C32.3322 32.2042 32.0322 33.6682 30.9562 34.2882L15.9261 42.9622C15.4561 43.2342 14.9357 43.4076 14.3966 43.4719C13.8574 43.5362 13.3108 43.49 12.7901 43.3362L0 39.5402M18.0001 9.50004H22.0001M22.0001 23.5001H18.0001C14.2281 23.5001 12.3441 23.5001 11.1721 22.3281C10.0001 21.1561 10.0001 19.2721 10.0001 15.5001V11.5C10.0001 7.72803 10.0001 5.84401 11.1721 4.67201C12.3441 3.5 14.2281 3.5 18.0001 3.5H22.0001C25.7722 3.5 27.6562 3.5 28.8282 4.67201C30.0002 5.84401 30.0002 7.72803 30.0002 11.5V15.5001C30.0002 19.2721 30.0002 21.1561 28.8282 22.3281C27.6562 23.5001 25.7722 23.5001 22.0001 23.5001Z'
        stroke='black'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <rect
        x='9.85'
        y='3.85'
        width='20.3'
        height='19.3'
        rx='2.15'
        stroke='#FF5B00'
        strokeWidth='3.7'
      />
      <path
        d='M18 9.5H22'
        stroke='#FF5B00'
        strokeWidth='3'
        strokeLinecap='round'
      />
    </g>
    <defs>
      <clipPath id='clip0_875_33812'>
        <rect
          width='40'
          height='46'
          fill='white'
          transform='translate(0 0.5)'
        />
      </clipPath>
    </defs>
  </svg>
)

export default SafeDeliveryIcon
