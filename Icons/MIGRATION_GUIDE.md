# Migration Guide: From Icons.tsx to Individual Icon Components

This guide helps you migrate from the old `config/Icons.tsx` structure to the new individual icon components in the `Icons/` directory.

## Before (Old Structure)

```tsx
import { Icons } from "@/config"

// Global icons
<Icons.global.logo />
<Icons.global.calculator />
<Icons.global.chatOutline />
<Icons.global.calendar />
<Icons.global.drink />
<Icons.global.speedoMeter />
<Icons.global.deliveryBox />
<Icons.global.worldwide />

// Flat icons
<Icons.archery />
<Icons.smilyRating />
<Icons.globe />
<Icons.webService />
<Icons.linkedIn />
<Icons.instagram />
<Icons.youtube />
<Icons.facebook />
<Icons.twitter />
<Icons.team />
<Icons.greenEnergy />
<Icons.dollar />
<Icons.shield />
<Icons.sustainability />
<Icons.lightBulb />
<Icons.person />
<Icons.payment />
<Icons.checklist />
<Icons.email />
<Icons.customerSupport />
<Icons.shareSession />
<Icons.clock />
<Icons.deliveryVehicle />
```

## After (New Structure)

```tsx
import {
  LogoIcon,
  CalculatorIcon,
  ChatOutlineIcon,
  CalendarIcon,
  DrinkIcon,
  SpeedoMeterIcon,
  DeliveryBoxIcon,
  WorldwideIcon,
  ArcheryIcon,
  SmilyRatingIcon,
  GlobeIcon,
  WebServiceIcon,
  LinkedInIcon,
  InstagramIcon,
  YouTubeIcon,
  FacebookIcon,
  TwitterIcon,
  TeamIcon,
  GreenEnergyIcon,
  DollarIcon,
  ShieldIcon,
  SustainabilityIcon,
  LightBulbIcon,
  PersonIcon,
  PaymentIcon,
  ChecklistIcon,
  EmailIcon,
  CustomerSupportIcon,
  ShareSessionIcon,
  ClockIcon,
  DeliveryVehicleIcon,
  ArrowRightIcon,
  ArrowLeftIcon
} from "@/Icons"

// Usage
<LogoIcon />
<CalculatorIcon />
<ChatOutlineIcon />
<CalendarIcon />
<DrinkIcon />
<SpeedoMeterIcon />
<DeliveryBoxIcon />
<WorldwideIcon />
<ArcheryIcon />
<SmilyRatingIcon />
<GlobeIcon />
<WebServiceIcon />
<LinkedInIcon />
<InstagramIcon />
<YouTubeIcon />
<FacebookIcon />
<TwitterIcon />
<TeamIcon />
<GreenEnergyIcon />
<DollarIcon />
<ShieldIcon />
<SustainabilityIcon />
<LightBulbIcon />
<PersonIcon />
<PaymentIcon />
<ChecklistIcon />
<EmailIcon />
<CustomerSupportIcon />
<ShareSessionIcon />
<ClockIcon />
<DeliveryVehicleIcon />
<GlobalArrowRightIcon />
<GlobalArrowLeftIcon />
```

## Migration Steps

### 1. Update Import Statements

**Before:**

```tsx
import { Icons } from "@/config"
```

**After:**

```tsx
import { LogoIcon, CalculatorIcon /* other icons */ } from "@/Icons"
```

### 2. Update Icon Usage

**Before:**

```tsx
<Icons.global.logo className="w-8 h-8" />
<Icons.archery className="w-6 h-6" />
```

**After:**

```tsx
<LogoIcon className="w-8 h-8" />
<ArcheryIcon className="w-6 h-6" />
```

### 3. Update Configuration Files

Update all configuration files that reference `Icons.global.*` or `Icons.*`:

**Before:**

```tsx
icon: Icons.global.calculator
icon: Icons.archery
```

**After:**

```tsx
icon: CalculatorIcon
icon: ArcheryIcon
```

## Icon Mapping Reference

| Old Reference              | New Component         |
| -------------------------- | --------------------- |
| `Icons.global.logo`        | `LogoIcon`            |
| `Icons.global.calculator`  | `CalculatorIcon`      |
| `Icons.global.chatOutline` | `ChatOutlineIcon`     |
| `Icons.global.calendar`    | `CalendarIcon`        |
| `Icons.global.drink`       | `DrinkIcon`           |
| `Icons.global.speedoMeter` | `SpeedoMeterIcon`     |
| `Icons.global.deliveryBox` | `DeliveryBoxIcon`     |
| `Icons.global.worldwide`   | `WorldwideIcon`       |
| `Icons.archery`            | `ArcheryIcon`         |
| `Icons.smilyRating`        | `SmilyRatingIcon`     |
| `Icons.globe`              | `GlobeIcon`           |
| `Icons.webService`         | `WebServiceIcon`      |
| `Icons.linkedIn`           | `LinkedInIcon`        |
| `Icons.instagram`          | `InstagramIcon`       |
| `Icons.youtube`            | `YouTubeIcon`         |
| `Icons.facebook`           | `FacebookIcon`        |
| `Icons.twitter`            | `TwitterIcon`         |
| `Icons.team`               | `TeamIcon`            |
| `Icons.greenEnergy`        | `GreenEnergyIcon`     |
| `Icons.dollar`             | `DollarIcon`          |
| `Icons.shield`             | `ShieldIcon`          |
| `Icons.sustainability`     | `SustainabilityIcon`  |
| `Icons.lightBulb`          | `LightBulbIcon`       |
| `Icons.person`             | `PersonIcon`          |
| `Icons.payment`            | `PaymentIcon`         |
| `Icons.checklist`          | `ChecklistIcon`       |
| `Icons.email`              | `EmailIcon`           |
| `Icons.customerSupport`    | `CustomerSupportIcon` |
| `Icons.shareSession`       | `ShareSessionIcon`    |
| `Icons.clock`              | `ClockIcon`           |
| `Icons.deliveryVehicle`    | `DeliveryVehicleIcon` |
| `Icons.arrowRight`         | `ArrowRightIcon`      |
| `Icons.arrowLeft`          | `ArrowLeftIcon`       |

## Benefits of Migration

1. **Tree Shaking**: Only used icons are bundled
2. **Better Performance**: Smaller bundle sizes
3. **Improved Developer Experience**: Better autocomplete and IntelliSense
4. **Maintainability**: Each icon is in its own file
5. **Type Safety**: Better TypeScript support
6. **Consistency**: All icons follow the same pattern

## Automated Migration Script

You can use this find-and-replace pattern to help with migration:

```bash
# Replace Icons.global.* patterns
find . -name "*.tsx" -o -name "*.ts" | xargs sed -i 's/Icons\.global\.logo/LogoIcon/g'
find . -name "*.tsx" -o -name "*.ts" | xargs sed -i 's/Icons\.global\.calculator/CalculatorIcon/g'
# ... continue for other icons

# Replace Icons.* patterns
find . -name "*.tsx" -o -name "*.ts" | xargs sed -i 's/Icons\.archery/ArcheryIcon/g'
find . -name "*.tsx" -o -name "*.ts" | xargs sed -i 's/Icons\.smilyRating/SmilyRatingIcon/g'
# ... continue for other icons
```

## Testing

After migration, ensure:

1. All icons render correctly
2. No console errors
3. Bundle size is optimized
4. All icon references are updated
5. TypeScript compilation succeeds
