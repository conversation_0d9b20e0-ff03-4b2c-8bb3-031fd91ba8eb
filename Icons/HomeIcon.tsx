import React from "react"

const HomeIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='40'
    height='40'
    viewBox='0 0 40 40'
    fill='none'
    {...props}
  >
    <path
      d='M31.6665 17.4079L32.9017 18.3686L31.6665 17.4087L31.6665 17.4079ZM19.9998 8.33352L26.4461 13.3475L20.7672 8.93156L19.9999 8.33489L19.2326 8.93156L8.81589 17.0316L8.3332 17.4069V17.1667L10.3332 15.6667L11.1665 15.0417L11.6665 14.6667V14.0417V12.9167V12.4572L11.9091 14.6248L13.6705 13.2551L19.9998 8.33352ZM8.3332 17.4087L7.18362 18.3021L7.85063 17.7834L8.3332 17.408V17.4087Z'
      stroke='black'
      strokeWidth='2.5'
    />
    <path
      opacity='0.5'
      d='M16.0308 24.2108C15.9015 24.4513 15.832 24.7221 15.832 25.0002C15.832 24.7147 15.904 24.4458 16.0308 24.2108ZM22.4987 23.3335C22.7841 23.3335 23.053 23.4055 23.288 23.5322C23.0475 23.4029 22.7767 23.3335 22.4987 23.3335ZM23.9665 24.2107C24.0934 24.4458 24.1654 24.7146 24.1654 25.0002C24.1654 24.7221 24.0959 24.4512 23.9665 24.2107Z'
      stroke='#FF5B00'
      strokeWidth='2.5'
    />
    <path
      d='M16 33V25.5C16 24.3954 16.8954 23.5 18 23.5H22C23.1046 23.5 24 24.3954 24 25.5V33'
      stroke='#FF5B00'
      strokeWidth='3'
      strokeLinecap='round'
    />
  </svg>
)

export default HomeIcon
