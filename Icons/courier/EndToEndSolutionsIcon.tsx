import React from "react"

interface EndToEndSolutionsIconProps extends React.SVGProps<SVGSVGElement> {}

const EndToEndSolutionsIcon: React.FC<EndToEndSolutionsIconProps> = (props) => {
  return (
    <svg
      width='27'
      height='27'
      viewBox='0 0 27 27'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <g clipPath='url(#clip0_2090_864)'>
        <path
          d='M6.51589 13.786H19.9173C22.8756 13.786 25.2767 11.4654 25.2767 8.61619C25.2767 5.76701 22.8699 3.44633 19.9173 3.44633H6.84331C6.51589 1.51625 4.64327 0.235261 2.71319 0.654612C0.984142 1.02797 -0.187692 2.68233 0.0248644 4.43437C0.266124 6.39891 2.09281 7.74308 4.02289 7.42141C5.47045 7.17439 6.60205 6.04279 6.84907 4.59523H19.923C22.2437 4.59523 24.1336 6.39891 24.1336 8.61624C24.1336 10.8335 22.2438 12.6373 19.923 12.6373H6.51589C3.5576 12.6373 1.15071 14.9579 1.15071 17.8071C1.15071 20.6563 3.55754 22.977 6.51012 22.977H15.8446L13.386 25.4413C13.1562 25.6595 13.1505 26.0272 13.3746 26.2512C13.5986 26.4752 13.9605 26.4867 14.1845 26.2627C14.1903 26.2569 14.196 26.2512 14.196 26.2512L17.6368 22.8104C17.8608 22.5864 17.8608 22.2245 17.6368 22.0005L14.196 18.5539C13.9662 18.3356 13.6043 18.3413 13.386 18.5654C13.1735 18.7894 13.1735 19.1398 13.386 19.3638L15.8503 21.8281H6.51589C4.18945 21.8281 2.2996 20.0244 2.2996 17.8071C2.2996 15.5898 4.18945 13.786 6.51589 13.786ZM3.44844 6.31275C2.17897 6.31275 1.15071 5.28454 1.15071 4.01502C1.15071 2.74555 2.17892 1.71729 3.44844 1.71729C4.71791 1.71729 5.74617 2.7455 5.74617 4.01502C5.74617 5.28449 4.71791 6.31275 3.44844 6.31275Z'
          fill='black'
        />
        <path
          d='M23.554 18.9502C21.6526 18.9502 20.1074 20.4954 20.1074 22.3968C20.1074 24.2981 21.6526 25.8433 23.554 25.8433C25.4554 25.8433 27.0006 24.2981 27.0006 22.3968C27.0006 20.4954 25.4611 18.956 23.554 18.9502ZM23.554 24.6945C22.2845 24.6945 21.2563 23.6663 21.2563 22.3968C21.2563 21.1272 22.2845 20.099 23.554 20.099C24.8235 20.099 25.8517 21.1272 25.8517 22.3968C25.8517 23.6663 24.8235 24.6945 23.554 24.6945Z'
          fill='#FF5B00'
        />
      </g>
      <defs>
        <clipPath id='clip0_2090_864'>
          <rect width='27' height='27' fill='white' />
        </clipPath>
      </defs>
    </svg>
  )
}

export default EndToEndSolutionsIcon
