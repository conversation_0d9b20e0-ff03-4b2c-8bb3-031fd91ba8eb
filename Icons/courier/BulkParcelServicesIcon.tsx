import React from "react"

interface BulkParcelServicesIconProps extends React.SVGProps<SVGSVGElement> {}

const BulkParcelServicesIcon: React.FC<BulkParcelServicesIconProps> = (
  props,
) => {
  return (
    <svg
      width='27'
      height='27'
      viewBox='0 0 27 27'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M6.31161 10.3091H5.61035V3.68182C5.61141 3.37818 5.74287 3.08956 5.97116 2.88933C6.16227 2.72137 6.40806 2.62914 6.6623 2.62983H16.6067C16.8255 2.62946 17.0387 2.69717 17.2169 2.8241L19.1963 4.23828C19.3173 4.32276 19.4179 4.43324 19.4905 4.56159C19.587 4.72254 19.6374 4.907 19.6364 5.09495V8.58043H18.9351V5.09495C18.9354 5.03219 18.9179 4.97044 18.8849 4.91718C18.8624 4.87547 18.8305 4.83934 18.7916 4.81197L16.8094 3.39569C16.7501 3.3536 16.6793 3.33119 16.6067 3.33119H6.6623C6.57919 3.33051 6.49856 3.35993 6.4358 3.41467C6.35797 3.48164 6.31272 3.57915 6.31167 3.68188L6.31161 10.3091Z'
        fill='black'
      />
      <path
        d='M11.221 7.88965H10.1691C9.7816 7.88965 9.46777 7.57583 9.46777 7.18833V6.48702C9.46777 6.09953 9.7816 5.78571 10.1691 5.78571H11.221C11.6085 5.78571 11.9223 6.09953 11.9223 6.48702V7.18833C11.9223 7.57577 11.6085 7.88965 11.221 7.88965ZM10.1691 6.48702V7.18833H11.221V6.48702H10.1691Z'
        fill='#FF5B00'
      />
      <path
        d='M8.76577 10.2635H8.06445V4.73375C8.06445 4.54021 8.22155 4.38312 8.41508 4.38312H19.2852V5.08443H8.76577V10.2635Z'
        fill='black'
      />
      <path
        d='M8.21136 5.01885L5.75684 3.26564L6.68886 3.06995L9.05922 4.76288L8.21136 5.01885Z'
        fill='black'
      />
      <path
        d='M13.9355 4.84911L11.7188 3.26561L12.5045 2.96576L14.9829 4.73584L13.9355 4.84911Z'
        fill='black'
      />
      <path
        d='M25.9482 19.461H23.7181V18.7597H25.9482C26.1417 18.7597 26.2988 18.6026 26.2988 18.409V10.6947C26.2992 10.6316 26.2816 10.5699 26.2487 10.5162C26.2255 10.4745 26.1932 10.4387 26.1547 10.411L24.1732 8.99548C24.1139 8.95377 24.0431 8.9313 23.9705 8.9313H15.5549C15.4697 8.93199 15.3877 8.96321 15.3235 9.01895C15.2478 9.08555 15.2042 9.18132 15.2042 9.28193V14.8923H14.5029V9.28193C14.5022 8.97581 14.6355 8.68514 14.8676 8.48559C15.0591 8.3208 15.3031 8.22999 15.5556 8.22925H23.9711C24.1892 8.22888 24.4024 8.29691 24.5802 8.42315L26.5596 9.83765C26.6806 9.92213 26.7809 10.0322 26.8535 10.1606C26.9502 10.3219 27.0011 10.5067 27 10.6946V18.4089C27.0001 18.9901 26.5292 19.461 25.9482 19.461Z'
        fill='black'
      />
      <path
        d='M17.6583 14.8921H16.957V10.3337C16.957 10.1401 17.1141 9.98303 17.3077 9.98303H26.649V10.6843H17.6583V14.8921Z'
        fill='black'
      />
      <path
        d='M17.1039 10.6191L14.6494 8.8658L15.3609 8.5127L17.5114 10.0486L17.1039 10.6191Z'
        fill='black'
      />
      <path
        d='M21.5794 10.3091L19.5586 8.86581L20.3066 8.5387L22.4206 10.0486L21.5794 10.3091Z'
        fill='black'
      />
      <path
        d='M19.762 12.4481H18.71C18.5165 12.4481 18.3594 12.291 18.3594 12.0975C18.3594 11.9039 18.5165 11.7468 18.71 11.7468H19.762C19.9555 11.7468 20.1126 11.9039 20.1126 12.0975C20.1126 12.291 19.9555 12.4481 19.762 12.4481Z'
        fill='#FF5B00'
      />
      <path
        d='M8.06498 20.5236H3.02964C2.91916 20.5229 2.80911 20.5061 2.70353 20.4734C2.60534 20.4384 2.51174 20.391 2.4251 20.3332L0.441123 18.9151C0.164795 18.717 0.000685547 18.3983 0 18.0585V11.035C0 10.454 0.470918 9.98303 1.05195 9.98303H10.5195C10.7376 9.98266 10.9504 10.0504 11.1285 10.1766L13.1076 11.5914C13.2286 11.6759 13.3292 11.786 13.4018 11.9144C13.4989 12.0753 13.5498 12.2601 13.5491 12.448V14.8921H12.8478V12.448C12.8482 12.3849 12.8306 12.3232 12.7976 12.2695C12.7745 12.2278 12.7426 12.192 12.7037 12.1643L10.7225 10.7488C10.6632 10.7067 10.592 10.6843 10.5195 10.6843H1.052C0.966779 10.685 0.884725 10.7162 0.820547 10.7719C0.745137 10.8385 0.702053 10.9343 0.701314 11.0349V18.0585C0.701314 18.1724 0.756369 18.279 0.849287 18.345L2.82693 19.7581C2.85989 19.7795 2.89533 19.797 2.93214 19.8103C2.96441 19.818 2.99737 19.8219 3.03064 19.8223H8.06493L8.06498 20.5236Z'
        fill='black'
      />
      <path
        d='M3.15542 20.1725H2.4541V12.0872C2.4541 11.8937 2.6112 11.7366 2.80473 11.7366H13.1983V12.4379H3.15542V20.1725Z'
        fill='black'
      />
      <path
        d='M2.60106 12.3723L0.146484 10.6191L0.961758 10.34L3.47909 12.1381L2.60106 12.3723Z'
        fill='black'
      />
      <path
        d='M7.56386 11.9992L5.63184 10.619L6.62277 10.4651L8.49384 11.8017L7.56386 11.9992Z'
        fill='black'
      />
      <path
        d='M5.83519 17.0066H4.78325C4.58971 17.0066 4.43262 16.8495 4.43262 16.6559C4.43262 16.4624 4.58971 16.3053 4.78325 16.3053H5.83519C6.02873 16.3053 6.18582 16.4624 6.18582 16.6559C6.18582 16.8495 6.02873 17.0066 5.83519 17.0066Z'
        fill='#FF5B00'
      />
      <path
        d='M6.88714 18.4091H4.78325C4.58971 18.4091 4.43262 18.2521 4.43262 18.0585C4.43262 17.865 4.58971 17.7079 4.78325 17.7079H6.88714C7.08067 17.7079 7.23777 17.865 7.23777 18.0585C7.23777 18.2521 7.08067 18.4091 6.88714 18.4091Z'
        fill='#FF5B00'
      />
      <path
        d='M23.0165 24.3701H10.7438C10.5247 24.3705 10.3108 24.3021 10.1323 24.1745L8.1543 22.7614C7.87835 22.5636 7.71424 22.2449 7.71387 21.9054V15.5937C7.71387 15.0127 8.18479 14.5418 8.76581 14.5418H21.0385C21.2581 14.5411 21.4723 14.6098 21.6504 14.7381L23.6277 16.1506C23.9041 16.348 24.0682 16.6667 24.0685 17.0065V23.3182C24.0685 23.8992 23.5976 24.3701 23.0165 24.3701ZM8.56173 22.1908L10.5401 23.6036C10.5994 23.6463 10.6709 23.6691 10.7438 23.6688H23.0165C23.2101 23.6688 23.3672 23.5117 23.3672 23.3182V17.0065C23.3672 16.8932 23.3125 16.787 23.2203 16.7211L21.2426 15.3083C21.183 15.2658 21.1118 15.2431 21.0385 15.2431H8.76581C8.57228 15.2431 8.41518 15.4002 8.41518 15.5937V21.9054C8.41513 22.0186 8.4695 22.1249 8.56173 22.1908Z'
        fill='black'
      />
      <path
        d='M12.9739 21.2143H11.9219C11.7284 21.2143 11.5713 21.0572 11.5713 20.8637C11.5713 20.6702 11.7284 20.5131 11.9219 20.5131H12.9739C13.1674 20.5131 13.3245 20.6702 13.3245 20.8637C13.3245 21.0572 13.1674 21.2143 12.9739 21.2143Z'
        fill='#FF5B00'
      />
      <path
        d='M14.0258 22.6169H11.9219C11.7284 22.6169 11.5713 22.4598 11.5713 22.2663C11.5713 22.0727 11.7284 21.9156 11.9219 21.9156H14.0258C14.2193 21.9156 14.3764 22.0727 14.3764 22.2663C14.3764 22.4598 14.2193 22.6169 14.0258 22.6169Z'
        fill='#FF5B00'
      />
      <path
        d='M10.8703 24.0195H10.1689V16.6457C10.1689 16.4521 10.326 16.295 10.5196 16.295H23.7184V16.9964H10.8703V24.0195Z'
        fill='black'
      />
      <path
        d='M10.3158 16.9308L7.86133 15.1776L8.6496 14.8792L11.1371 16.6559L10.3158 16.9308Z'
        fill='black'
      />
      <path
        d='M14.873 15.1707L15.2803 14.6001L17.7354 16.3525L17.3282 16.923L14.873 15.1707Z'
        fill='black'
      />
    </svg>
  )
}

export default BulkParcelServicesIcon
