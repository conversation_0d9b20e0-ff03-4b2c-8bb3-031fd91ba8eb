import * as React from "react"

interface GlobalReachIconProps extends React.SVGProps<SVGSVGElement> {}

const GlobalReachIcon: React.FC<GlobalReachIconProps> = (props) => (
  <svg
    width='27'
    height='27'
    viewBox='0 0 27 27'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <g clipPath='url(#clip0_2090_852)'>
      <path
        d='M21.8747 19.1834H21.8671C21.7559 19.1834 21.6491 19.2274 21.5701 19.3057C21.4911 19.3841 21.4463 19.4904 21.4453 19.6017C21.4444 19.7129 21.4874 19.8201 21.5651 19.8997C21.6427 19.9794 21.7487 20.0252 21.8599 20.0271H21.8731C22.4369 20.0271 22.9927 19.4021 23.2992 18.8633C23.6327 18.2762 23.8776 17.4345 23.4695 16.9943C23.395 16.9138 23.2921 16.8656 23.1826 16.8597C23.073 16.8539 22.9655 16.8909 22.8829 16.963C22.8002 17.035 22.7488 17.1364 22.7396 17.2457C22.7304 17.355 22.7642 17.4636 22.8336 17.5484C22.8567 17.6351 22.8353 17.9717 22.5656 18.4463C22.2896 18.9317 21.9695 19.1696 21.8747 19.1834Z'
        fill='black'
      />
      <path
        d='M13.6506 11.5746L8.47255 10.16C8.41106 10.1434 8.34661 10.1408 8.28399 10.1525C8.22136 10.1641 8.16217 10.1898 8.11083 10.2275L7.34329 10.7936C7.29084 10.8323 7.24807 10.8826 7.21833 10.9406C7.18859 10.9986 7.1727 11.0627 7.17191 11.1279C7.17111 11.193 7.18543 11.2575 7.21374 11.3162C7.24204 11.3749 7.28357 11.4263 7.33506 11.4662L10.6369 14.0315L10.2556 14.3129C10.1666 14.3785 10.1069 14.4764 10.0894 14.5856C10.0719 14.6948 10.098 14.8065 10.1621 14.8967L10.8423 15.8537C10.8985 15.9328 10.9802 15.9901 11.0736 16.016C11.1671 16.0418 11.2666 16.0347 11.3554 15.9958L16.2742 13.8409C16.3028 13.8284 16.33 13.8127 16.3553 13.7942L17.2412 13.1408L16.9679 13.6931C16.926 13.7779 16.9138 13.8744 16.9332 13.967C16.9527 14.0596 17.0027 14.1429 17.0752 14.2037C17.1477 14.2644 17.2386 14.299 17.3331 14.302C17.4277 14.3049 17.5205 14.2759 17.5966 14.2198L19.8912 12.5275C19.9289 12.4997 19.9616 12.4659 19.9882 12.4273L21.8232 9.76165L26.4366 6.35925C26.5294 6.2907 26.6005 6.19687 26.6414 6.08899L26.943 5.30198C26.9891 5.17517 27.0072 5.03991 26.9962 4.90544C26.9853 4.77097 26.9454 4.64045 26.8793 4.52281C26.8143 4.3981 26.7232 4.2889 26.6121 4.20269C26.5009 4.11647 26.3725 4.05527 26.2356 4.02327L25.4732 3.85621C25.1578 3.77952 24.8296 3.77202 24.511 3.83421C24.1924 3.8964 23.8911 4.02681 23.6277 4.21649L13.6506 11.5746ZM25.2926 4.6804L26.0549 4.84746C26.091 4.86104 26.1204 4.88821 26.1367 4.92317C26.1513 4.94598 26.1579 4.97304 26.1553 5L25.8784 5.72253L23.1247 7.75335C23.1283 7.66211 23.102 7.57219 23.05 7.49713C22.998 7.42208 22.923 7.36596 22.8364 7.33722C22.7497 7.30849 22.656 7.30871 22.5695 7.33783C22.4829 7.36696 22.4082 7.42342 22.3565 7.49871L19.3334 11.8905L18.4958 12.5083L19.712 10.0504C19.7615 9.95016 19.7691 9.83434 19.7332 9.72842C19.6974 9.62251 19.6209 9.53516 19.5207 9.48557C19.4205 9.43597 19.3047 9.42819 19.1987 9.46392C19.0927 9.49966 19.0053 9.576 18.9556 9.67616L18.0581 11.4899L15.8924 13.0871L11.3311 15.0853L11.0899 14.7459L11.5862 14.3798C11.6386 14.3412 11.6815 14.2909 11.7112 14.2329C11.741 14.1749 11.7569 14.1108 11.7577 14.0456C11.7586 13.9804 11.7443 13.9159 11.7159 13.8572C11.6876 13.7985 11.6461 13.7471 11.5946 13.7071L8.29272 11.1418L8.44722 11.0278L13.6253 12.4423C13.6867 12.4591 13.7512 12.4618 13.8139 12.4501C13.8765 12.4384 13.9357 12.4128 13.987 12.375L24.1285 4.89567C24.2956 4.7781 24.4861 4.698 24.687 4.66085C24.8879 4.6237 25.0945 4.63037 25.2926 4.6804Z'
        fill='#FF5B00'
      />
      <path
        d='M8.8472 15.6734L8.48939 15.8969C8.3945 15.9562 8.32703 16.0508 8.30185 16.1598C8.27666 16.2688 8.29581 16.3834 8.35509 16.4783C8.41436 16.5732 8.50891 16.6406 8.61793 16.6658C8.72694 16.691 8.8415 16.6718 8.9364 16.6126L9.29421 16.3891C9.3412 16.3597 9.38195 16.3214 9.41413 16.2763C9.44631 16.2312 9.46929 16.1802 9.48176 16.1262C9.49423 16.0722 9.49595 16.0163 9.48681 15.9617C9.47768 15.9071 9.45787 15.8547 9.42852 15.8078C9.39917 15.7608 9.36085 15.72 9.31575 15.6878C9.27065 15.6557 9.21966 15.6327 9.16568 15.6202C9.05666 15.595 8.9421 15.6142 8.8472 15.6734Z'
        fill='#FF5B00'
      />
      <path
        d='M6.98056 16.8399L6.22621 17.3115C6.13144 17.3708 6.06413 17.4654 6.03906 17.5744C6.01399 17.6834 6.03321 17.7979 6.0925 17.8927C6.15179 17.9876 6.2463 18.055 6.35526 18.0802C6.46422 18.1054 6.57873 18.0863 6.67363 18.0271L7.42797 17.5556C7.47503 17.5262 7.51584 17.4879 7.54807 17.4427C7.58029 17.3976 7.60331 17.3466 7.6158 17.2926C7.62829 17.2385 7.63001 17.1826 7.62086 17.1279C7.61171 17.0732 7.59187 17.0208 7.56247 16.9738C7.53307 16.9268 7.49469 16.886 7.44953 16.8538C7.40437 16.8216 7.3533 16.7987 7.29926 16.7863C7.24521 16.7738 7.18925 16.7722 7.13456 16.7814C7.07988 16.7906 7.02755 16.8105 6.98056 16.8399Z'
        fill='#FF5B00'
      />
      <path
        d='M4.71737 18.2544L3.96302 18.7257C3.86813 18.785 3.80067 18.8795 3.77548 18.9885C3.75029 19.0976 3.76944 19.2121 3.82872 19.307C3.888 19.4019 3.98254 19.4694 4.09156 19.4946C4.20058 19.5197 4.31513 19.5006 4.41003 19.4413L5.16438 18.97C5.21137 18.9406 5.25212 18.9023 5.28429 18.8572C5.31647 18.8121 5.33946 18.7611 5.35193 18.7072C5.3644 18.6532 5.36612 18.5973 5.35698 18.5426C5.34785 18.488 5.32804 18.4357 5.29869 18.3887C5.26934 18.3417 5.23102 18.3009 5.18592 18.2688C5.14082 18.2366 5.08983 18.2136 5.03585 18.2011C4.98187 18.1887 4.92596 18.1869 4.87131 18.1961C4.81667 18.2052 4.76436 18.225 4.71737 18.2544Z'
        fill='#FF5B00'
      />
      <path
        d='M3.03594 19.803C3.00662 19.756 2.96831 19.7152 2.92322 19.683C2.87812 19.6508 2.82712 19.6278 2.77313 19.6153C2.71914 19.6028 2.66322 19.6011 2.60857 19.6103C2.55391 19.6194 2.5016 19.6393 2.45463 19.6687L2.09682 19.8922C2.00192 19.9515 1.93446 20.046 1.90927 20.155C1.88408 20.264 1.90323 20.3786 1.96251 20.4735C2.02178 20.5684 2.11633 20.6359 2.22535 20.661C2.33437 20.6862 2.44892 20.6671 2.54382 20.6078L2.90163 20.3843C2.99651 20.325 3.06395 20.2305 3.08914 20.1214C3.11433 20.0124 3.09519 19.8979 3.03594 19.803Z'
        fill='#FF5B00'
      />
      <path
        d='M21.9982 10.9956L21.4536 11.7867C21.4607 11.8027 21.4654 11.8197 21.4675 11.8372C21.4488 11.8467 21.4283 11.8524 21.4073 11.854L20.8555 12.6556C20.9192 12.671 20.9839 12.6821 21.0491 12.6889C21.2683 12.7103 21.7821 12.7592 22.098 12.3966C22.5028 11.9321 22.2214 11.4124 22.0704 11.1333C22.0459 11.0879 22.0198 11.0396 21.9982 10.9956Z'
        fill='black'
      />
      <path
        d='M14.3413 18.3691C14.3926 18.4613 14.4367 18.5573 14.4734 18.6562C14.5565 18.9708 14.5958 19.2954 14.5902 19.6207C14.5916 19.939 14.6217 20.2564 14.6802 20.5693C14.8031 21.2586 15.1177 21.8994 15.5878 22.4183C15.7067 22.5443 15.8503 22.6445 16.0097 22.7126C16.169 22.7807 16.3407 22.8152 16.5139 22.814C16.9432 22.7946 17.3557 22.6417 17.6939 22.3767C18.2355 22.01 18.7269 21.5742 19.1558 21.0804C19.4414 20.7678 19.6541 20.3958 19.7785 19.9911C19.8575 19.644 19.9071 19.2908 19.9268 18.9354C19.9401 18.5506 20.0046 18.1693 20.1186 17.8016C20.2973 17.3907 20.5398 17.0108 20.8373 16.6758C20.9797 16.4962 21.1268 16.3102 21.2611 16.1147C21.3068 16.0484 21.3655 15.9779 21.4265 15.9038C21.6712 15.6057 21.9757 15.2349 21.9167 14.785C21.896 14.6521 21.8472 14.5251 21.7738 14.4124C21.7004 14.2997 21.6039 14.2039 21.4907 14.1312C21.2577 13.972 21.0061 13.842 20.7416 13.7439C20.5171 13.6717 20.3103 13.5533 20.1344 13.3964L19.4498 13.9012C19.716 14.188 20.0505 14.4025 20.422 14.5248C20.6252 14.5987 20.8189 14.6967 20.9988 14.8165C21.0314 14.8361 21.0591 14.8628 21.08 14.8946C21.0891 14.985 20.8684 15.2536 20.7743 15.3682C20.7024 15.4557 20.6282 15.5464 20.566 15.6364C20.4474 15.809 20.3155 15.9756 20.1757 16.1522C19.8203 16.5562 19.5333 17.0155 19.3262 17.5122C19.1845 17.9503 19.1036 18.4058 19.0858 18.8659C19.0695 19.1748 19.0276 19.4817 18.9607 19.7837C18.8647 20.0687 18.7079 20.3296 18.5012 20.5481C18.1187 20.9832 17.682 21.3674 17.2017 21.6913C16.8946 21.9118 16.4701 22.1153 16.1924 21.8298C15.8339 21.4236 15.5957 20.9254 15.5048 20.3913C15.4578 20.1277 15.434 19.8605 15.4335 19.5927C15.4387 19.1872 15.3853 18.783 15.2749 18.3928C15.2246 18.2505 15.1629 18.1126 15.0903 17.9804C14.9972 17.8451 14.9438 17.6865 14.9362 17.5224C15.0065 16.8171 14.831 16.2679 14.4151 15.8901C14.3325 15.816 14.2427 15.7503 14.1471 15.694L12.918 16.2325C13.251 16.2182 13.5789 16.3178 13.8478 16.5147C14.0581 16.7057 14.1394 17.0079 14.0966 17.4386C14.0814 17.7665 14.1668 18.0911 14.3413 18.3691Z'
        fill='black'
      />
      <path
        d='M0.843755 13.5C0.844842 12.9377 0.883271 12.3761 0.95879 11.8189C1.41675 11.8482 2.79576 11.9972 2.84313 12.7285C2.79279 13.3379 2.80066 13.9507 2.86661 14.5586C2.96652 15.4345 3.59398 15.9721 4.14769 16.4467C4.41485 16.6578 4.65764 16.8981 4.87155 17.163L5.58994 16.714C5.33139 16.3751 5.03136 16.0701 4.69687 15.8059C4.19795 15.3787 3.76722 15.0095 3.70501 14.4628C3.64427 13.9002 3.638 13.3331 3.68626 12.7693C3.6875 12.7538 3.68791 12.7381 3.6875 12.7225C3.65687 11.7313 2.69282 11.0906 1.09601 10.9823C1.2458 10.248 1.46113 9.52861 1.73941 8.83275C1.90259 8.92572 2.08605 8.97733 2.27376 8.98307C2.28901 8.98358 2.30404 8.98389 2.31929 8.98389C3.10392 8.98389 3.92521 8.25848 4.25521 7.86751C4.63321 7.41905 5.1072 7.20163 5.65597 6.94981C5.74578 6.9085 5.83642 6.86689 5.92706 6.82415C6.39281 6.60487 7.04231 6.22574 7.30413 5.46902C7.44121 4.98539 7.45284 4.47482 7.33791 3.98545C7.2402 3.54729 7.07365 3.12739 6.84445 2.74138C8.3952 1.77799 10.1372 1.16401 11.9493 0.942153C11.955 0.947427 11.9599 0.953392 11.966 0.958374C11.9942 1.11951 12.004 1.28333 11.9952 1.44668L11.9946 1.53176C11.9973 1.93468 11.9851 2.2102 11.8014 2.3616C11.6359 2.47819 11.4534 2.56836 11.2602 2.62888C11.1467 2.67203 11.0334 2.7154 10.9253 2.76504C10.3833 2.98275 9.88529 3.29704 9.45553 3.69263C9.11956 4.03519 9.02254 4.37344 9.16714 4.69788C9.21089 4.79597 9.2904 4.87368 9.38946 4.91518C9.48852 4.95668 9.59968 4.95884 9.70028 4.92123C9.80088 4.88361 9.88335 4.80905 9.93088 4.71274C9.97841 4.61643 9.98743 4.50562 9.95609 4.40289C9.9862 4.3596 10.0205 4.31938 10.0585 4.28279C10.4177 3.96365 10.8299 3.70964 11.2765 3.53216C11.3682 3.49014 11.464 3.45409 11.5602 3.41753C11.8403 3.32682 12.1033 3.1899 12.3382 3.01244C12.8452 2.59489 12.8412 1.97732 12.8384 1.52681L12.8388 1.45636C12.8456 1.25816 12.834 1.05977 12.8043 0.863699C13.0347 0.851157 13.2665 0.84374 13.5 0.84374C14.6877 0.843986 15.8695 1.01199 17.0102 1.34276C16.7086 1.83935 16.8283 2.51156 16.91 2.9629C16.9214 3.0248 16.9323 3.08485 16.9417 3.14211C16.9729 3.33039 17.0678 3.90357 16.937 4.06744C16.9294 4.07702 16.9092 4.10235 16.8169 4.10771C16.8076 4.10833 16.7984 4.10853 16.7893 4.10853C16.4482 4.08186 14.1404 3.95507 13.9695 5.30887L13.9709 5.30908C13.9666 5.33458 13.9637 5.3603 13.9623 5.38612C13.932 5.89369 13.8197 6.12131 13.7322 6.22111C13.6615 6.27157 13.5863 6.31526 13.5074 6.35161C13.2845 6.44205 13.0906 6.59166 12.9465 6.78429C12.8324 6.96628 12.4851 7.5201 12.7566 7.99028C13.0738 8.53966 13.9213 8.50855 14.9953 8.46922C15.4774 8.44359 15.9605 8.4429 16.4426 8.46714L17.3945 7.7651C16.6631 7.56492 15.7786 7.59588 14.9644 7.62608C14.4608 7.64441 13.7136 7.67202 13.5027 7.56789C13.534 7.45169 13.5851 7.34175 13.6537 7.24283C13.7283 7.18817 13.8082 7.14117 13.8922 7.10256C14.0689 7.02563 14.2295 6.91581 14.3652 6.77894C14.7508 6.33904 14.7912 5.65957 14.8046 5.43638L14.8054 5.42351L14.8066 5.41455C14.8565 5.01935 15.9806 4.88989 16.737 4.95076C16.7471 4.95158 16.7576 4.9521 16.7677 4.9522C16.8021 4.95251 16.8361 4.95199 16.8684 4.94993C17.0082 4.94679 17.1457 4.91334 17.2714 4.85191C17.397 4.79047 17.5078 4.70251 17.5962 4.59408C17.9618 4.13647 17.8442 3.42764 17.7742 3.00422C17.7639 2.94305 17.7523 2.87888 17.7404 2.81265C17.6166 2.12844 17.6079 1.7799 17.8938 1.64951C17.9005 1.64648 17.9059 1.64205 17.9123 1.63873C19.4813 2.22623 20.9182 3.11901 22.14 4.26542L22.8349 3.75288C20.5462 1.56154 17.5596 0.245125 14.3981 0.0341277C11.2365 -0.17687 8.10148 0.731 5.54191 2.59877C2.98233 4.46655 1.16135 7.17518 0.397806 10.2504C-0.365737 13.3256 -0.0231745 16.5715 1.36551 19.4195C1.44756 19.325 1.54324 19.2431 1.6494 19.1768L2.0644 18.9174C1.25901 17.2251 0.841973 15.3742 0.843755 13.5ZM6.14339 3.2112C6.30583 3.49815 6.42789 3.80614 6.50611 4.12646C6.59282 4.47636 6.59318 4.8421 6.50714 5.19217C6.38354 5.54915 6.10256 5.80891 5.56719 6.06094C5.47902 6.10266 5.39106 6.14293 5.30394 6.18299C4.71706 6.45233 4.11 6.73084 3.61026 7.32338C3.31363 7.6748 2.69317 8.15394 2.30447 8.13983C2.26286 8.13881 2.22188 8.12944 2.18396 8.1123C2.14603 8.09515 2.11193 8.07057 2.08368 8.04001C3.0106 6.11546 4.40666 4.45492 6.14339 3.2112Z'
        fill='black'
      />
      <path
        d='M13.5001 27C15.761 27.0002 17.9858 26.4325 19.9702 25.3491C21.9546 24.2656 23.635 22.701 24.8573 20.799C26.0797 18.897 26.8047 16.7184 26.9658 14.4633C27.1269 12.2081 26.719 9.94858 25.7796 7.89209L25.0812 8.40714C25.2913 8.8813 25.4718 9.368 25.6217 9.86447C25.4202 9.90788 25.2212 9.9622 25.0257 10.0272C24.624 10.1548 24.2773 10.2656 23.8447 10.173C23.673 10.1172 23.5118 10.033 23.368 9.92378C23.3089 9.88493 23.2477 9.84501 23.1844 9.80608L22.4382 10.3565L22.4183 10.3854C22.5947 10.434 22.7599 10.5167 22.9045 10.6289C23.1336 10.7989 23.3927 10.9242 23.6682 10.9981C24.211 11.0945 24.7699 11.0367 25.2815 10.8312C25.4629 10.7693 25.6481 10.7193 25.836 10.6812C26.4474 13.3445 26.1796 16.1341 25.0728 18.6325C23.966 21.1309 22.0798 23.2035 19.6965 24.5402C17.3131 25.8768 14.561 26.4054 11.852 26.047C9.14311 25.6886 6.62315 24.4623 4.66946 22.5519C4.82972 22.4872 4.97445 22.3893 5.09413 22.2647C5.60767 21.7225 5.51374 20.8342 5.44947 20.4772C5.41923 20.2366 5.43507 19.9924 5.49615 19.7577L4.58866 20.3248C4.59117 20.4262 4.60135 20.5272 4.61911 20.627C4.70295 21.0925 4.64898 21.5076 4.4815 21.6844C4.41065 21.7426 4.32754 21.784 4.2384 21.8056C4.14926 21.8271 4.05641 21.8282 3.96679 21.8087C3.76163 21.5736 3.56183 21.3337 3.37421 21.0838L2.99115 21.3233C2.8742 21.3955 2.74623 21.4481 2.6123 21.479C3.86468 23.1911 5.50346 24.5834 7.39536 25.5428C9.28726 26.5021 11.3789 27.0014 13.5001 27Z'
        fill='black'
      />
      <path
        d='M13.7948 10.4198L14.6835 9.76438C13.7784 9.5167 12.7399 9.32437 11.6763 9.5938C11.383 9.66934 11.1008 9.78278 10.8369 9.93127L12.3081 10.3331C12.805 10.2811 13.3072 10.3104 13.7948 10.4198Z'
        fill='black'
      />
    </g>
    <defs>
      <clipPath id='clip0_2090_852'>
        <rect width='27' height='27' fill='white' />
      </clipPath>
    </defs>
  </svg>
)

export default GlobalReachIcon
