import React from "react"

interface CodIconProps extends React.SVGProps<SVGSVGElement> {}

const CodIcon: React.FC<CodIconProps> = (props) => {
  return (
    <svg
      width='27'
      height='27'
      viewBox='0 0 27 27'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M25.9453 16.0861L24.0469 15.0019V11.8125C24.0459 11.4771 23.9122 11.1558 23.6751 10.9187C23.4379 10.6815 23.1166 10.5479 22.7812 10.5469H18.5625V6.75C18.5625 6.63811 18.5181 6.53081 18.4389 6.45169C18.3598 6.37257 18.2525 6.32812 18.1406 6.32812H1.26562C1.15374 6.32812 1.04643 6.37257 0.967314 6.45169C0.888197 6.53081 0.84375 6.63811 0.84375 6.75V21.0938C0.84375 21.2056 0.888197 21.3129 0.967314 21.3921C1.04643 21.4712 1.15374 21.5156 1.26562 21.5156H2.57344C2.66109 21.9491 2.88306 22.3439 3.20783 22.644C3.5326 22.9441 3.94366 23.1343 4.38265 23.1875C4.82164 23.2408 5.26625 23.1544 5.65334 22.9406C6.04044 22.7268 6.35035 22.3965 6.53906 21.9966C6.72778 22.3965 7.03769 22.7268 7.42478 22.9406C7.81188 23.1544 8.25648 23.2408 8.69547 23.1875C9.13447 23.1343 9.54553 22.9441 9.8703 22.644C10.1951 22.3439 10.417 21.9491 10.5047 21.5156H20.2922C20.389 21.9925 20.6477 22.4212 21.0245 22.7291C21.4012 23.037 21.8728 23.2052 22.3594 23.2052C22.8459 23.2052 23.3176 23.037 23.6943 22.7291C24.071 22.4212 24.3297 21.9925 24.4266 21.5156H25.7344C25.8463 21.5156 25.9536 21.4712 26.0327 21.3921C26.1118 21.3129 26.1562 21.2056 26.1562 21.0938V16.4531C26.1565 16.3788 26.1372 16.3057 26.1002 16.2412C26.0631 16.1768 26.0097 16.1233 25.9453 16.0861ZM4.64062 22.3594C4.39031 22.3594 4.14561 22.2851 3.93748 22.1461C3.72935 22.007 3.56713 21.8093 3.47134 21.5781C3.37555 21.3468 3.35048 21.0923 3.39932 20.8468C3.44815 20.6013 3.56869 20.3758 3.74569 20.1988C3.92269 20.0218 4.14821 19.9013 4.39371 19.8524C4.63922 19.8036 4.8937 19.8287 5.12496 19.9245C5.35622 20.0203 5.55388 20.1825 5.69295 20.3906C5.83202 20.5987 5.90625 20.8434 5.90625 21.0938C5.90525 21.4291 5.77158 21.7504 5.53445 21.9876C5.29732 22.2247 4.97598 22.3584 4.64062 22.3594ZM8.4375 22.3594C8.18718 22.3594 7.94249 22.2851 7.73436 22.1461C7.52623 22.007 7.36401 21.8093 7.26821 21.5781C7.17242 21.3468 7.14736 21.0923 7.19619 20.8468C7.24503 20.6013 7.36557 20.3758 7.54257 20.1988C7.71957 20.0218 7.94508 19.9013 8.19059 19.8524C8.4361 19.8036 8.69057 19.8287 8.92183 19.9245C9.1531 20.0203 9.35076 20.1825 9.48983 20.3906C9.6289 20.5987 9.70312 20.8434 9.70312 21.0938C9.70212 21.4291 9.56846 21.7504 9.33133 21.9876C9.09419 22.2247 8.77286 22.3584 8.4375 22.3594ZM17.7188 20.6719H10.5047C10.417 20.2384 10.1951 19.8436 9.8703 19.5435C9.54553 19.2434 9.13447 19.0532 8.69547 19C8.25648 18.9467 7.81188 19.0331 7.42478 19.2469C7.03769 19.4607 6.72778 19.791 6.53906 20.1909C6.35035 19.791 6.04044 19.4607 5.65334 19.2469C5.26625 19.0331 4.82164 18.9467 4.38265 19C3.94366 19.0532 3.5326 19.2434 3.20783 19.5435C2.88306 19.8436 2.66109 20.2384 2.57344 20.6719H1.6875V18.1406H17.7188V20.6719ZM17.7188 17.2969H1.6875V7.17188H17.7188V17.2969ZM21.5156 11.3906H22.7812C22.8931 11.3906 23.0004 11.4351 23.0796 11.5142C23.1587 11.5933 23.2031 11.7006 23.2031 11.8125V14.7656H21.5156V11.3906ZM22.3594 22.3594C22.1091 22.3594 21.8644 22.2851 21.6562 22.1461C21.4481 22.007 21.2859 21.8093 21.1901 21.5781C21.0943 21.3468 21.0692 21.0923 21.1181 20.8468C21.1669 20.6013 21.2874 20.3758 21.4644 20.1988C21.6414 20.0218 21.867 19.9013 22.1125 19.8524C22.358 19.8036 22.6124 19.8287 22.8437 19.9245C23.075 20.0203 23.2726 20.1825 23.4117 20.3906C23.5508 20.5987 23.625 20.8434 23.625 21.0938C23.624 21.4291 23.4903 21.7504 23.2532 21.9876C23.0161 22.2247 22.6947 22.3584 22.3594 22.3594ZM25.3125 18.5625H24.4688V17.7188H25.3125V18.5625ZM25.3125 16.875H24.0469C23.935 16.875 23.8277 16.9194 23.7486 16.9986C23.6694 17.0777 23.625 17.185 23.625 17.2969V18.9844C23.625 19.0963 23.6694 19.2036 23.7486 19.2827C23.8277 19.3618 23.935 19.4062 24.0469 19.4062H25.3125V20.6719H24.4266C24.3297 20.195 24.071 19.7663 23.6943 19.4584C23.3176 19.1505 22.8459 18.9823 22.3594 18.9823C21.8728 18.9823 21.4012 19.1505 21.0245 19.4584C20.6477 19.7663 20.389 20.195 20.2922 20.6719H18.5625V11.3906H20.6719V15.1875C20.6719 15.2994 20.7163 15.4067 20.7954 15.4858C20.8746 15.5649 20.9819 15.6094 21.0938 15.6094H23.4098C23.411 15.6094 23.412 15.6098 23.4128 15.6106C23.4136 15.6114 23.4141 15.6125 23.4141 15.6136L25.3125 16.6978V16.875Z'
        fill='black'
      />
      <path
        d='M21.0938 16.4531H22.7812V17.2969H21.0938V16.4531Z'
        fill='black'
      />
      <path
        d='M0.84375 23.2031H26.1562V24.0469H0.84375V23.2031Z'
        fill='black'
      />
      <path
        d='M8.4375 4.84062C8.4375 4.73017 8.52704 4.64062 8.6375 4.64062H13.3C13.4105 4.64062 13.5 4.73017 13.5 4.84062V5.28438C13.5 5.39483 13.4105 5.48438 13.3 5.48438H8.6375C8.52704 5.48438 8.4375 5.39483 8.4375 5.28438V4.84062Z'
        fill='#FF5B00'
      />
      <path
        d='M6.75 3.15312C6.75 3.04267 6.83954 2.95312 6.95 2.95312H11.6125C11.723 2.95312 11.8125 3.04267 11.8125 3.15312V3.59688C11.8125 3.70733 11.723 3.79688 11.6125 3.79688H6.95C6.83954 3.79688 6.75 3.70733 6.75 3.59688V3.15312Z'
        fill='#FF5B00'
      />
      <path
        d='M5.0625 3.15312C5.0625 3.04267 5.15204 2.95312 5.2625 2.95312H5.70625C5.81671 2.95312 5.90625 3.04267 5.90625 3.15312V3.59688C5.90625 3.70733 5.81671 3.79688 5.70625 3.79688H5.2625C5.15204 3.79688 5.0625 3.70733 5.0625 3.59688V3.15312Z'
        fill='#FF5B00'
      />
      <path
        d='M9.70312 10.125H9.28125C8.94589 10.126 8.62456 10.2597 8.38742 10.4968C8.15029 10.7339 8.01663 11.0553 8.01562 11.3906V13.0781C8.01663 13.4135 8.15029 13.7348 8.38742 13.972C8.62456 14.2091 8.94589 14.3427 9.28125 14.3438H9.70312C10.0385 14.3427 10.3598 14.2091 10.597 13.972C10.8341 13.7348 10.9677 13.4135 10.9688 13.0781V11.3906C10.9677 11.0553 10.8341 10.7339 10.597 10.4968C10.3598 10.2597 10.0385 10.126 9.70312 10.125ZM10.125 13.0781C10.125 13.19 10.0806 13.2973 10.0014 13.3764C9.92232 13.4556 9.81501 13.5 9.70312 13.5H9.28125C9.16936 13.5 9.06206 13.4556 8.98294 13.3764C8.90382 13.2973 8.85938 13.19 8.85938 13.0781V11.3906C8.85938 11.2787 8.90382 11.1714 8.98294 11.0923C9.06206 11.0132 9.16936 10.9688 9.28125 10.9688H9.70312C9.81501 10.9688 9.92232 11.0132 10.0014 11.0923C10.0806 11.1714 10.125 11.2787 10.125 11.3906V13.0781Z'
        fill='#FF5B00'
      />
      <path
        d='M13.5 10.125H12.2344C12.1225 10.125 12.0152 10.1694 11.9361 10.2486C11.8569 10.3277 11.8125 10.435 11.8125 10.5469V13.9219C11.8125 14.0338 11.8569 14.1411 11.9361 14.2202C12.0152 14.2993 12.1225 14.3438 12.2344 14.3438H13.5C13.8354 14.3427 14.1567 14.2091 14.3938 13.972C14.631 13.7348 14.7646 13.4135 14.7656 13.0781V11.3906C14.7646 11.0553 14.631 10.7339 14.3938 10.4968C14.1567 10.2597 13.8354 10.126 13.5 10.125ZM13.9219 13.0781C13.9219 13.19 13.8774 13.2973 13.7983 13.3764C13.7192 13.4556 13.6119 13.5 13.5 13.5H12.6562V10.9688H13.5C13.6119 10.9688 13.7192 11.0132 13.7983 11.0923C13.8774 11.1714 13.9219 11.2787 13.9219 11.3906V13.0781Z'
        fill='#FF5B00'
      />
      <path
        d='M6.32812 13.0781H7.17188C7.17087 13.4135 7.03721 13.7348 6.80008 13.972C6.56294 14.2091 6.24161 14.3427 5.90625 14.3438H5.48438C5.14902 14.3427 4.82768 14.2091 4.59055 13.972C4.35342 13.7348 4.21975 13.4135 4.21875 13.0781V11.3906C4.21975 11.0553 4.35342 10.7339 4.59055 10.4968C4.82768 10.2597 5.14902 10.126 5.48438 10.125H5.90625C6.24161 10.126 6.56294 10.2597 6.80008 10.4968C7.03721 10.7339 7.17087 11.0553 7.17188 11.3906H6.32812C6.32812 11.2787 6.28368 11.1714 6.20456 11.0923C6.12544 11.0132 6.01814 10.9688 5.90625 10.9688H5.48438C5.37249 10.9688 5.26518 11.0132 5.18606 11.0923C5.10695 11.1714 5.0625 11.2787 5.0625 11.3906V13.0781C5.0625 13.19 5.10695 13.2973 5.18606 13.3764C5.26518 13.4556 5.37249 13.5 5.48438 13.5H5.90625C6.01814 13.5 6.12544 13.4556 6.20456 13.3764C6.28368 13.2973 6.32812 13.19 6.32812 13.0781Z'
        fill='#FF5B00'
      />
      <path d='M4.21875 20.6719H5.0625V21.5156H4.21875V20.6719Z' fill='black' />
      <path
        d='M8.01562 20.6719H8.85938V21.5156H8.01562V20.6719Z'
        fill='black'
      />
      <path
        d='M21.9375 20.6719H22.7812V21.5156H21.9375V20.6719Z'
        fill='black'
      />
    </svg>
  )
}

export default CodIcon
