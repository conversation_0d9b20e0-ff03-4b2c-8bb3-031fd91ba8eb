import React from "react"

interface ECommerceLogisticsIconProps extends React.SVGProps<SVGSVGElement> {}

const ECommerceLogisticsIcon: React.FC<ECommerceLogisticsIconProps> = (
  props,
) => {
  return (
    <svg
      width='27'
      height='27'
      viewBox='0 0 27 27'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M25.3125 2.53125H1.6875C1.35184 2.53125 1.02992 2.66459 0.792568 2.90194C0.555217 3.13929 0.421875 3.46121 0.421875 3.79688V23.2031C0.421875 23.5388 0.555217 23.8607 0.792568 24.0981C1.02992 24.3354 1.35184 24.4688 1.6875 24.4688H25.3125C25.6482 24.4688 25.9701 24.3354 26.2074 24.0981C26.4448 23.8607 26.5781 23.5388 26.5781 23.2031V3.79688C26.5781 3.46121 26.4448 3.13929 26.2074 2.90194C25.9701 2.66459 25.6482 2.53125 25.3125 2.53125ZM25.7344 23.2031C25.7344 23.315 25.6899 23.4223 25.6108 23.5014C25.5317 23.5806 25.4244 23.625 25.3125 23.625H1.6875C1.57561 23.625 1.46831 23.5806 1.38919 23.5014C1.31007 23.4223 1.26563 23.315 1.26562 23.2031V6.75H25.7344V23.2031ZM25.7344 5.90625H1.26562V3.79688C1.26562 3.68499 1.31007 3.57768 1.38919 3.49856C1.46831 3.41945 1.57561 3.375 1.6875 3.375H25.3125C25.4244 3.375 25.5317 3.41945 25.6108 3.49856C25.6899 3.57768 25.7344 3.68499 25.7344 3.79688V5.90625Z'
        fill='black'
      />
      <path d='M2.10938 4.21875H8.85938V5.0625H2.10938V4.21875Z' fill='black' />
      <path d='M24.0469 4.21875H24.8906V5.0625H24.0469V4.21875Z' fill='black' />
      <path d='M22.3594 4.21875H23.2031V5.0625H22.3594V4.21875Z' fill='black' />
      <path d='M20.6719 4.21875H21.5156V5.0625H20.6719V4.21875Z' fill='black' />
      <path
        d='M10.9688 17.3686C10.9858 17.4677 11.0377 17.5574 11.1151 17.6216C11.1924 17.6858 11.2901 17.7203 11.3906 17.7188H17.2969C17.3921 17.7221 17.4857 17.693 17.5623 17.6363C17.6389 17.5796 17.694 17.4986 17.7188 17.4066L18.8452 13.2975C18.8793 13.1708 18.8835 13.0378 18.8572 12.9092C18.831 12.7806 18.7751 12.6599 18.6939 12.5566C18.6128 12.4534 18.5088 12.3705 18.39 12.3146C18.2713 12.2587 18.1411 12.2312 18.0098 12.2344H10.9266L10.5469 10.0533C10.5298 9.95427 10.4779 9.86457 10.4006 9.80038C10.3232 9.73618 10.2255 9.70171 10.125 9.70317H8.4375V10.5469H9.77062L10.9688 17.3686ZM18.0141 13.0782L16.9763 16.875H11.745L11.0742 13.0782H18.0141Z'
        fill='black'
      />
      <path
        d='M12.6563 18.1406C12.4059 18.1406 12.1612 18.2149 11.9531 18.3539C11.745 18.493 11.5828 18.6907 11.487 18.9219C11.3912 19.1532 11.3661 19.4077 11.4149 19.6532C11.4638 19.8987 11.5843 20.1242 11.7613 20.3012C11.9383 20.4782 12.1638 20.5987 12.4093 20.6476C12.6548 20.6964 12.9093 20.6713 13.1406 20.5755C13.3718 20.4797 13.5695 20.3175 13.7086 20.1094C13.8476 19.9013 13.9219 19.6566 13.9219 19.4062C13.9219 19.0706 13.7885 18.7487 13.5512 18.5113C13.3138 18.274 12.9919 18.1406 12.6563 18.1406ZM12.6563 19.8281C12.5728 19.8281 12.4912 19.8034 12.4219 19.757C12.3525 19.7107 12.2984 19.6448 12.2665 19.5677C12.2346 19.4906 12.2262 19.4058 12.2425 19.3239C12.2588 19.2421 12.2989 19.1669 12.3579 19.1079C12.4169 19.0489 12.4921 19.0088 12.5739 18.9925C12.6558 18.9762 12.7406 18.9846 12.8177 19.0165C12.8948 19.0484 12.9607 19.1025 13.007 19.1719C13.0534 19.2412 13.0781 19.3228 13.0781 19.4062C13.0781 19.5181 13.0337 19.6254 12.9546 19.7046C12.8754 19.7837 12.7681 19.8281 12.6563 19.8281Z'
        fill='#FF5B00'
      />
      <path
        d='M16.0313 18.1406C15.7809 18.1406 15.5362 18.2149 15.3281 18.3539C15.12 18.493 14.9578 18.6907 14.862 18.9219C14.7662 19.1532 14.7411 19.4077 14.7899 19.6532C14.8388 19.8987 14.9593 20.1242 15.1363 20.3012C15.3133 20.4782 15.5388 20.5987 15.7843 20.6476C16.0298 20.6964 16.2843 20.6713 16.5156 20.5755C16.7468 20.4797 16.9445 20.3175 17.0836 20.1094C17.2226 19.9013 17.2969 19.6566 17.2969 19.4062C17.2969 19.0706 17.1635 18.7487 16.9262 18.5113C16.6888 18.274 16.3669 18.1406 16.0313 18.1406ZM16.0313 19.8281C15.9478 19.8281 15.8662 19.8034 15.7969 19.757C15.7275 19.7107 15.6734 19.6448 15.6415 19.5677C15.6096 19.4906 15.6012 19.4058 15.6175 19.3239C15.6338 19.2421 15.6739 19.1669 15.7329 19.1079C15.7919 19.0489 15.8671 19.0088 15.9489 18.9925C16.0308 18.9762 16.1156 18.9846 16.1927 19.0165C16.2698 19.0484 16.3357 19.1025 16.382 19.1719C16.4284 19.2412 16.4531 19.3228 16.4531 19.4062C16.4531 19.5181 16.4087 19.6254 16.3296 19.7046C16.2504 19.7837 16.1431 19.8281 16.0313 19.8281Z'
        fill='#FF5B00'
      />
      <path d='M7.17188 13.5H9.28125V14.3438H7.17188V13.5Z' fill='#FF5B00' />
      <path
        d='M8.01562 15.6094H9.70312V16.4531H8.01562V15.6094Z'
        fill='#FF5B00'
      />
    </svg>
  )
}

export default ECommerceLogisticsIcon
