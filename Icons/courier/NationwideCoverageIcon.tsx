import React from "react"

interface NationwideCoverageIconProps extends React.SVGProps<SVGSVGElement> {}

const NationwideCoverageIcon: React.FC<NationwideCoverageIconProps> = (
  props,
) => {
  return (
    <svg
      width='27'
      height='27'
      viewBox='0 0 27 27'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <g clipPath='url(#clip0_2045_367)'>
        <path
          d='M25.8314 3.2785C25.062 2.41946 23.9324 1.92745 22.7322 1.92692C21.7677 1.92692 20.848 2.24596 20.1245 2.81761C17.9872 1.36741 15.496 0.602234 12.8978 0.602234C9.45316 0.602234 6.21422 1.9438 3.77789 4.38012C1.34156 6.81645 0 10.0554 0 13.5C0 16.9446 1.34156 20.1836 3.77789 22.6199C6.21422 25.0562 9.45264 26.3978 12.8978 26.3978C16.3429 26.3978 19.5813 25.0562 22.0177 22.6199C24.454 20.1836 25.795 16.9446 25.795 13.5C25.795 12.4195 25.66 11.3437 25.3932 10.2996C25.7449 9.82178 26.0671 9.35614 26.3287 8.9195C27.385 7.15501 27.1756 4.78249 25.8314 3.2785ZM12.8978 1.44598C13.5949 1.44598 14.2831 1.50505 14.9581 1.62106C13.2933 3.73518 11.9048 5.16903 8.94744 6.44784C7.63488 7.01421 6.65613 7.73245 5.87777 8.6996C5.18168 8.91897 4.55572 9.31395 4.07215 9.85448C3.68139 10.2906 3.38713 10.8005 3.19148 11.3469C2.50172 11.4049 1.80299 11.3453 1.07156 11.1639C2.16211 5.63151 7.05006 1.44598 12.8978 1.44598ZM10.0443 15.062C9.50801 15.9553 8.68008 17.009 7.87957 18.0273C7.71082 18.2424 7.54418 18.4539 7.38387 18.6601C7.32744 18.7328 7.26152 18.765 7.16924 18.765C7.07959 18.765 7.01209 18.7318 6.95619 18.6601C6.79377 18.4518 6.62555 18.2371 6.45416 18.0193C5.65523 17.0037 4.82941 15.9532 4.29627 15.0625C4.1966 14.8959 4.11117 14.7224 4.0384 14.5441C4.03365 14.5304 4.02785 14.5173 4.02152 14.5041C3.69088 13.6666 3.65555 12.7301 3.89971 11.8811C3.90445 11.8674 3.90867 11.8536 3.91236 11.8394C4.06951 11.3152 4.33266 10.8258 4.69969 10.4156C5.30877 9.73477 6.20842 9.34401 7.16818 9.34401C8.129 9.34401 9.03023 9.73477 9.63984 10.4156C10.7457 11.6517 10.9155 13.6055 10.0443 15.062ZM0.936563 11.9976C1.53088 12.14 2.10885 12.2117 2.67996 12.2117C2.77646 12.2117 2.87244 12.2091 2.96842 12.2049C2.83711 13.0048 2.90039 13.8412 3.16617 14.6169C2.56711 15.429 2.01129 16.3382 1.48131 17.3723C1.06787 16.1562 0.843223 14.8537 0.843223 13.4995C0.843223 12.9911 0.875391 12.4901 0.936563 11.9976ZM1.89896 18.4312C2.42842 17.319 2.98055 16.3519 3.57381 15.4965C4.1349 16.4326 4.97707 17.5041 5.79182 18.5398C5.96215 18.7566 6.12984 18.9696 6.29121 19.1769C6.50689 19.4542 6.81908 19.6072 7.16977 19.6072C7.5215 19.6072 7.83369 19.4542 8.04937 19.1769C8.20863 18.9717 8.37422 18.7613 8.54244 18.5477C8.89523 18.099 9.25277 17.6439 9.5908 17.1956C10.4514 17.3849 11.3959 17.7546 11.5367 18.4734C11.7239 19.431 10.5068 20.9356 8.50658 22.2191C7.7393 22.6294 7.24887 23.3155 6.90293 23.9546C4.7076 22.6916 2.94521 20.7563 1.89896 18.4312ZM7.64912 24.3506C8.0325 23.6498 8.4317 23.2121 8.9158 22.9574C8.92635 22.9516 8.93637 22.9458 8.94639 22.9395C10.0195 22.2523 10.9071 21.4908 11.5119 20.7378C12.2096 19.8687 12.5038 19.0303 12.3636 18.3131C12.1927 17.4387 11.4418 16.8175 10.1297 16.4616C10.3649 16.1293 10.5817 15.805 10.7673 15.496C11.8236 13.7305 11.6137 11.3579 10.2684 9.85501C9.51012 9.00757 8.40111 8.51766 7.21986 8.50395C7.78623 7.99454 8.45965 7.57688 9.28178 7.22251C12.5608 5.80501 14.0627 4.14968 15.8762 1.81671C17.1624 2.14313 18.3874 2.68261 19.5112 3.42194C18.4597 4.70602 18.1849 6.54962 18.7587 8.12532C17.0253 8.55247 14.965 9.19688 14.9829 10.9535C14.9908 11.7703 14.5341 11.9201 14.0511 12.0788C13.9097 12.1252 13.7769 12.169 13.6603 12.2323C12.7717 12.7169 12.5049 13.5264 12.9289 14.4534C13.2664 15.1917 14.078 16.036 15.1 16.7115C15.5994 17.0416 16.8386 17.7805 17.5627 17.5089C17.9608 17.3602 18.282 16.884 18.6221 16.3798C18.7661 16.1663 18.9886 15.8367 19.1399 15.6853C19.2064 15.8309 19.315 16.1689 19.4084 16.9309C19.6335 18.7592 20.0607 20.5817 20.7462 22.6431C18.6358 24.4572 15.8931 25.5556 12.8983 25.5556C11.0173 25.554 9.23695 25.1216 7.64912 24.3506ZM24.9518 13.5C24.9518 16.8191 23.6034 19.8287 21.426 22.0108C20.828 20.1524 20.4493 18.4903 20.2442 16.827C20.0786 15.4802 19.825 14.9101 19.3382 14.7931C18.7666 14.6565 18.3563 15.264 17.9223 15.9079C17.7325 16.1895 17.4145 16.6609 17.2668 16.7184C17.1202 16.769 16.4621 16.6071 15.5377 15.9901C14.6544 15.4006 13.944 14.6686 13.6846 14.079C13.4604 13.5701 13.5738 13.239 14.0627 12.9721C14.1117 12.9452 14.2146 12.9115 14.3137 12.8788C14.8089 12.7158 15.8393 12.3773 15.8256 10.944C15.814 9.84815 17.2853 9.35456 19.1257 8.90315C19.1289 8.90843 19.132 8.91423 19.1352 8.9195C19.6984 9.86134 20.5448 10.9382 21.3638 11.9797C21.5304 12.1917 21.6944 12.4 21.8521 12.603C22.0678 12.8804 22.3805 13.0333 22.7317 13.0333C23.0845 13.0333 23.3972 12.8799 23.6097 12.6035C23.7637 12.4058 23.924 12.2022 24.0864 11.996C24.3 11.725 24.5152 11.4513 24.7266 11.1781C24.8759 11.9417 24.9518 12.7195 24.9518 13.5ZM25.6052 8.48655C25.0662 9.38725 24.2314 10.4483 23.4241 11.4745C23.2606 11.6817 23.1003 11.8863 22.9437 12.0873C22.8894 12.1574 22.8224 12.1901 22.7322 12.1901C22.6405 12.1901 22.574 12.1579 22.5176 12.0851C22.3588 11.8811 22.1938 11.6712 22.0266 11.4587C21.2245 10.4377 20.395 9.38251 19.8587 8.48602C18.9865 7.0295 19.1558 5.07569 20.2611 3.84012C20.8712 3.15985 21.7714 2.76962 22.7317 2.76962C23.6925 2.76962 24.5937 3.15985 25.2033 3.84012C26.3076 5.07675 26.4769 7.03055 25.6052 8.48655Z'
          fill='black'
        />
        <path
          d='M6.94434 14.7601C7.89358 14.7601 8.66309 13.9906 8.66309 13.0414C8.66309 12.0921 7.89358 11.3226 6.94434 11.3226C5.9951 11.3226 5.22559 12.0921 5.22559 13.0414C5.22559 13.9906 5.9951 14.7601 6.94434 14.7601Z'
          stroke='#FF5B00'
          strokeWidth='0.859375'
        />
        <path
          d='M22.6221 7.79236C23.5713 7.79236 24.3408 7.02285 24.3408 6.07361C24.3408 5.12437 23.5713 4.35486 22.6221 4.35486C21.6728 4.35486 20.9033 5.12437 20.9033 6.07361C20.9033 7.02285 21.6728 7.79236 22.6221 7.79236Z'
          stroke='#FF5B00'
          strokeWidth='0.859375'
        />
      </g>
      <defs>
        <clipPath id='clip0_2045_367'>
          <rect width='27' height='27' fill='white' />
        </clipPath>
      </defs>
    </svg>
  )
}

export default NationwideCoverageIcon
