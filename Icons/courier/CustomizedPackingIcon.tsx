import React from "react"

interface CustomizedPackingIconProps extends React.SVGProps<SVGSVGElement> {}

const CustomizedPackingIcon: React.FC<CustomizedPackingIconProps> = (props) => {
  return (
    <svg
      width='27'
      height='27'
      viewBox='0 0 27 27'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M23.625 17.406V5.90622C23.625 5.82786 23.6032 5.75104 23.562 5.68437C23.5209 5.61769 23.462 5.56377 23.3919 5.52864L13.2669 0.466143C13.2103 0.437984 13.148 0.422893 13.0848 0.42198C13.0215 0.421067 12.9589 0.434357 12.9015 0.460869L1.93272 5.52337C1.85946 5.55699 1.79741 5.61095 1.75394 5.67882C1.71047 5.74669 1.68741 5.82562 1.6875 5.90622V19.4062C1.68741 19.4868 1.71047 19.5658 1.75394 19.6336C1.79741 19.7015 1.85946 19.7555 1.93272 19.7891L12.9015 24.8516C12.9589 24.8782 13.0215 24.8915 13.0848 24.8906C13.148 24.8897 13.2103 24.8745 13.2669 24.8463L16.0049 23.4768C16.3076 24.198 16.7734 24.8393 17.3657 25.3503C17.9579 25.8613 18.6605 26.228 19.4184 26.4218C20.1762 26.6156 20.9686 26.6311 21.7335 26.4671C22.4983 26.3032 23.2147 25.9642 23.8265 25.4768C24.4383 24.9894 24.9288 24.3668 25.2596 23.658C25.5903 22.9491 25.7523 22.1732 25.7327 21.3913C25.7132 20.6093 25.5127 19.8425 25.147 19.151C24.7813 18.4596 24.2604 17.8623 23.625 17.406ZM17.4751 3.51366L8.45543 8.3705L6.14461 7.30421L15.4148 2.48376L17.4751 3.51366ZM8.01563 9.09665V15.8134L5.90625 14.909V8.12317L8.01563 9.09665ZM13.0707 1.312L14.4824 2.01759L5.28979 6.79743C5.25501 6.81563 5.22289 6.83852 5.19434 6.86546L3.11607 5.90622L13.0707 1.312ZM12.6562 23.8095L2.53125 19.1362V6.5654L5.0625 7.73399V15.1875C5.06254 15.27 5.08679 15.3507 5.13225 15.4196C5.17771 15.4885 5.24239 15.5426 5.31826 15.5751L8.27139 16.8407C8.32381 16.8634 8.38036 16.8751 8.4375 16.875C8.54939 16.875 8.65669 16.8305 8.73581 16.7514C8.81493 16.6723 8.85938 16.565 8.85938 16.4531V9.48636L12.6562 11.2387V23.8095ZM13.0707 10.5004L9.41361 8.81294L18.398 3.97509L22.2597 5.90622L13.0707 10.5004ZM15.7412 22.6657L13.5 23.7863V11.2292L22.7812 6.5886V16.914C21.9205 16.5198 20.9666 16.3749 20.0276 16.4955C19.0887 16.6162 18.2024 16.9976 17.4693 17.5966C16.7362 18.1955 16.1857 18.988 15.8802 19.884C15.5748 20.78 15.5266 21.7437 15.7412 22.6657ZM20.6719 25.7343C19.8375 25.7343 19.0218 25.4869 18.3281 25.0234C17.6343 24.5598 17.0936 23.9009 16.7743 23.13C16.455 22.3592 16.3714 21.5109 16.5342 20.6926C16.697 19.8742 17.0988 19.1225 17.6888 18.5325C18.2788 17.9425 19.0305 17.5407 19.8488 17.3779C20.6672 17.2151 21.5154 17.2987 22.2863 17.618C23.0572 17.9373 23.7161 18.478 24.1796 19.1718C24.6432 19.8656 24.8906 20.6812 24.8906 21.5156C24.8893 22.6341 24.4444 23.7064 23.6535 24.4973C22.8627 25.2882 21.7904 25.733 20.6719 25.7343Z'
        fill='black'
      />
      <path
        d='M22.4829 20.3736L20.25 22.6066L19.2827 21.6392C19.2036 21.5601 19.0963 21.5156 18.9844 21.5156C18.8725 21.5156 18.7652 21.5601 18.6861 21.6392C18.6069 21.7183 18.5625 21.8256 18.5625 21.9375C18.5625 22.0494 18.6069 22.1567 18.6861 22.2358L19.9517 23.5015C19.9909 23.5406 20.0374 23.5717 20.0886 23.5929C20.1397 23.6141 20.1946 23.625 20.25 23.625C20.3054 23.625 20.3603 23.6141 20.4115 23.5929C20.4626 23.5717 20.5092 23.5406 20.5483 23.5015L23.0796 20.9702C23.1188 20.931 23.1498 20.8845 23.171 20.8333C23.1922 20.7821 23.2031 20.7273 23.2031 20.6719C23.2031 20.6165 23.1922 20.5616 23.171 20.5104C23.1498 20.4593 23.1188 20.4127 23.0796 20.3736C23.0404 20.3344 22.9939 20.3033 22.9427 20.2821C22.8915 20.2609 22.8367 20.25 22.7813 20.25C22.7259 20.25 22.671 20.2609 22.6198 20.2821C22.5686 20.3033 22.5221 20.3344 22.4829 20.3736Z'
        fill='#FF5B00'
      />
    </svg>
  )
}

export default CustomizedPackingIcon
