import React from "react"

const CarIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='40'
    height='41'
    viewBox='0 0 40 41'
    fill='none'
    {...props}
  >
    <path
      d='M4.16536 20.5L7.4987 22.1667M35.832 21.3333L32.4987 22.1667M13.332 29.6667L13.742 28.6433C14.3504 27.1217 14.6554 26.36 15.2904 25.93C15.9237 25.5 16.7437 25.5 18.3837 25.5H21.6137C23.2537 25.5 24.0737 25.5 24.707 25.93C25.3404 26.36 25.647 27.1217 26.257 28.6433L26.6654 29.6667M3.33203 28.8333V33.6367C3.33203 34.2683 3.73203 34.845 4.3687 35.1267C4.78036 35.31 5.1737 35.5 5.65036 35.5H8.5137C8.99036 35.5 9.3837 35.31 9.79703 35.1267C10.4304 34.845 10.832 34.2683 10.832 33.6367V30.5M29.1654 30.5V33.6367C29.1654 34.2683 29.5654 34.845 30.202 35.1267C30.6137 35.31 31.007 35.5 31.4837 35.5H34.347C34.8237 35.5 35.217 35.31 35.6304 35.1267C36.2637 34.845 36.6654 34.2683 36.6654 33.6367V28.8333M33.332 14.6667L34.9987 13.8333M6.66536 14.6667L4.9987 13.8333M7.4987 15.5L9.31203 10.0583C10.0454 7.85833 10.412 6.75833 11.2854 6.13C12.1587 5.50167 13.3187 5.5 15.637 5.5H24.3604C26.6787 5.5 27.8387 5.5 28.7104 6.13C29.5854 6.75833 29.952 7.85833 30.6854 10.0583L32.4987 15.5'
      stroke='black'
      strokeWidth='2.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M7.4987 15.5H32.4987C34.0937 17.19 36.6654 19.5417 36.6654 22.1667V27.95C36.6654 28.9 36.032 29.7 35.1937 29.8117L29.9987 30.5H9.9987L4.8037 29.8117C3.96536 29.7033 3.33203 28.9033 3.33203 27.95V22.1667C3.33203 19.5417 5.9037 17.19 7.4987 15.5Z'
      stroke='black'
      strokeWidth='2.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M7.5 22.5L5.5 21'
      stroke='#FF5B00'
      strokeWidth='3'
      strokeLinecap='round'
    />
    <path
      d='M32 22.5L34 21.5'
      stroke='#FF5B00'
      strokeWidth='3'
      strokeLinecap='round'
    />
  </svg>
)

export default CarIcon
