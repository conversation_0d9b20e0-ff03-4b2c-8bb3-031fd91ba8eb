import * as React from "react"

const DataCenterMovingIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width='28'
    height='27'
    viewBox='0 0 28 27'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <path
      d='M26.0485 3.98788H20.5333C19.9394 3.98788 19.4303 4.45455 19.4303 5.09091V19.5152C19.4303 20.1091 19.897 20.6182 20.5333 20.6182H22.9091V24.7333C22.9091 25.0727 22.6121 25.3697 22.2727 25.3697H13.8727V20.5758H17.4364C18.0303 20.5758 18.5394 20.1091 18.5394 19.4727V1.10303C18.5394 0.509091 18.0727 0 17.4364 0H9.71515C9.12121 0 8.61212 0.466667 8.61212 1.10303V19.5152C8.61212 20.1091 9.07879 20.6182 9.71515 20.6182H13.0667V25.3697H4.62424C4.28485 25.3697 3.98788 25.0727 3.98788 24.7333V20.6182H6.61818C7.21212 20.6182 7.72121 20.1515 7.72121 19.5152V5.09091C7.72121 4.49697 7.25455 3.98788 6.61818 3.98788H1.10303C0.509091 3.98788 0 4.45455 0 5.09091V19.5152C0 20.1091 0.466667 20.6182 1.10303 20.6182H3.13939V24.7333C3.13939 25.5394 3.81818 26.2182 4.62424 26.2182H22.2727C23.0788 26.2182 23.7576 25.5394 23.7576 24.7333V20.6182H26.0909C26.6848 20.6182 27.1939 20.1515 27.1939 19.5152V5.09091C27.1515 4.49697 26.6848 3.98788 26.0485 3.98788ZM9.46061 19.5152V1.10303C9.46061 0.975758 9.54545 0.848485 9.71515 0.848485H17.4364C17.5636 0.848485 17.6909 0.933333 17.6909 1.10303V19.5152C17.6909 19.6424 17.6061 19.7697 17.4364 19.7697H9.71515C9.58788 19.7273 9.46061 19.6424 9.46061 19.5152ZM0.848485 19.5152V5.09091C0.848485 4.96364 0.933333 4.83636 1.10303 4.83636H6.61818C6.74545 4.83636 6.87273 4.92121 6.87273 5.09091V19.5152C6.87273 19.6424 6.78788 19.7697 6.61818 19.7697H1.10303C0.933333 19.7273 0.848485 19.6424 0.848485 19.5152ZM26.303 19.5152C26.303 19.6424 26.2182 19.7697 26.0485 19.7697H20.5333C20.4061 19.7697 20.2788 19.6849 20.2788 19.5152V5.09091C20.2788 4.96364 20.3636 4.83636 20.5333 4.83636H26.0485C26.1758 4.83636 26.303 4.92121 26.303 5.09091V19.5152Z'
      fill='black'
    />
    <path
      d='M16.2492 17.3939H10.9037C10.6492 17.3939 10.4795 17.5636 10.4795 17.8182C10.4795 18.0727 10.6492 18.2424 10.9037 18.2424H16.2492C16.5037 18.2424 16.6734 18.0727 16.6734 17.8182C16.6734 17.606 16.4613 17.3939 16.2492 17.3939Z'
      fill='#FF5B00'
    />
    <path
      d='M16.2492 14.3818H10.9037C10.6492 14.3818 10.4795 14.5515 10.4795 14.806C10.4795 15.0606 10.6492 15.2303 10.9037 15.2303H16.2492C16.5037 15.2303 16.6734 15.0606 16.6734 14.806C16.6734 14.5515 16.4613 14.3818 16.2492 14.3818Z'
      fill='#FF5B00'
    />
    <path
      d='M16.2492 11.3273H10.9037C10.6492 11.3273 10.4795 11.497 10.4795 11.7515C10.4795 12.0061 10.6492 12.1758 10.9037 12.1758H16.2492C16.5037 12.1758 16.6734 12.0061 16.6734 11.7515C16.6734 11.5394 16.4613 11.3273 16.2492 11.3273Z'
      fill='#FF5B00'
    />
    <path
      d='M16.2492 8.31512H10.9037C10.6492 8.31512 10.4795 8.48482 10.4795 8.73937C10.4795 8.99391 10.6492 9.16361 10.9037 9.16361H16.2492C16.5037 9.16361 16.6734 8.99391 16.6734 8.73937C16.6734 8.48482 16.4613 8.31512 16.2492 8.31512Z'
      fill='#FF5B00'
    />
    <path
      d='M16.2492 5.26056H10.9037C10.6492 5.26056 10.4795 5.43026 10.4795 5.6848C10.4795 5.93935 10.6492 6.10904 10.9037 6.10904H16.2492C16.5037 6.10904 16.6734 5.93935 16.6734 5.6848C16.6734 5.47268 16.4613 5.26056 16.2492 5.26056Z'
      fill='#FF5B00'
    />
    <path
      d='M5.93967 17.4363H1.73967C1.48513 17.4363 1.31543 17.606 1.31543 17.8606C1.31543 18.1151 1.48513 18.2848 1.73967 18.2848H5.93967C6.19422 18.2848 6.36391 18.1151 6.36391 17.8606C6.36391 17.6485 6.15179 17.4363 5.93967 17.4363Z'
      fill='#FF5B00'
    />
    <path
      d='M5.93967 14.6788H1.73967C1.48513 14.6788 1.31543 14.8485 1.31543 15.103C1.31543 15.3576 1.48513 15.5273 1.73967 15.5273H5.93967C6.19422 15.5273 6.36391 15.3576 6.36391 15.103C6.36391 14.8485 6.15179 14.6788 5.93967 14.6788Z'
      fill='#FF5B00'
    />
    <path
      d='M5.93967 11.8788H1.73967C1.48513 11.8788 1.31543 12.0485 1.31543 12.303C1.31543 12.5576 1.48513 12.7273 1.73967 12.7273H5.93967C6.19422 12.7273 6.36391 12.5576 6.36391 12.303C6.36391 12.0485 6.15179 11.8788 5.93967 11.8788Z'
      fill='#FF5B00'
    />
    <path
      d='M5.93967 9.07874H1.73967C1.48513 9.07874 1.31543 9.24843 1.31543 9.50298C1.31543 9.75752 1.48513 9.92722 1.73967 9.92722H5.93967C6.19422 9.92722 6.36391 9.75752 6.36391 9.50298C6.36391 9.29086 6.15179 9.07874 5.93967 9.07874Z'
      fill='#FF5B00'
    />
    <path
      d='M5.9398 6.27875H4.41252C4.15798 6.27875 3.98828 6.44844 3.98828 6.70299C3.98828 6.95754 4.15798 7.12723 4.41252 7.12723H5.9398C6.19434 7.12723 6.36404 6.95754 6.36404 6.70299C6.36404 6.49087 6.15192 6.27875 5.9398 6.27875Z'
      fill='#FF5B00'
    />
    <path
      d='M25.4123 17.4363H21.2123C20.9578 17.4363 20.7881 17.606 20.7881 17.8606C20.7881 18.1151 20.9578 18.2848 21.2123 18.2848H25.4123C25.6669 18.2848 25.8366 18.1151 25.8366 17.8606C25.8366 17.6485 25.6245 17.4363 25.4123 17.4363Z'
      fill='#FF5B00'
    />
    <path
      d='M25.4123 14.6788H21.2123C20.9578 14.6788 20.7881 14.8485 20.7881 15.103C20.7881 15.3576 20.9578 15.5273 21.2123 15.5273H25.4123C25.6669 15.5273 25.8366 15.3576 25.8366 15.103C25.8366 14.8485 25.6245 14.6788 25.4123 14.6788Z'
      fill='#FF5B00'
    />
    <path
      d='M25.4123 11.8788H21.2123C20.9578 11.8788 20.7881 12.0485 20.7881 12.303C20.7881 12.5576 20.9578 12.7273 21.2123 12.7273H25.4123C25.6669 12.7273 25.8366 12.5576 25.8366 12.303C25.8366 12.0485 25.6245 11.8788 25.4123 11.8788Z'
      fill='#FF5B00'
    />
    <path
      d='M25.4123 9.07874H21.2123C20.9578 9.07874 20.7881 9.24843 20.7881 9.50298C20.7881 9.75752 20.9578 9.92722 21.2123 9.92722H25.4123C25.6669 9.92722 25.8366 9.75752 25.8366 9.50298C25.8366 9.29086 25.6245 9.07874 25.4123 9.07874Z'
      fill='#FF5B00'
    />
    <path
      d='M25.4125 6.27875H23.8852C23.6306 6.27875 23.4609 6.44844 23.4609 6.70299C23.4609 6.95754 23.6306 7.12723 23.8852 7.12723H25.4125C25.667 7.12723 25.8367 6.95754 25.8367 6.70299C25.8367 6.49087 25.6246 6.27875 25.4125 6.27875Z'
      fill='#FF5B00'
    />
    <path
      d='M16.2484 2.20605H14.7211C14.4666 2.20605 14.2969 2.37575 14.2969 2.6303C14.2969 2.88484 14.4666 3.05454 14.7211 3.05454H16.2484C16.5029 3.05454 16.6726 2.88484 16.6726 2.6303C16.6726 2.41818 16.4605 2.20605 16.2484 2.20605Z'
      fill='#FF5B00'
    />
  </svg>
)

export default DataCenterMovingIcon
