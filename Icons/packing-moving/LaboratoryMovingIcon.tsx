import * as React from "react"

const LaboratoryMovingIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width='27'
    height='27'
    viewBox='0 0 27 27'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <g clipPath='url(#clip0_2045_578)'>
      <path
        d='M26.4162 16.2413C26.1098 15.883 25.6998 15.6382 25.2492 15.5351L24.3793 13.672C23.7046 12.2268 22.2381 11.293 20.6431 11.293H6.47968C4.88473 11.293 3.41818 12.2268 2.74345 13.672L1.8862 15.5083C1.38227 15.59 0.919943 15.8471 0.583023 16.2413C0.16948 16.7251 -0.0109242 17.3631 0.0881109 17.9918L0.83926 22.7616C1.0251 23.9419 1.62769 25.0195 2.53594 25.796C3.44413 26.5724 4.60239 27 5.79724 27H21.2018C22.3967 27 23.555 26.5724 24.4632 25.796C25.3715 25.0196 25.974 23.942 26.1599 22.7616L26.911 17.9919C27.0101 17.3632 26.8297 16.7251 26.4162 16.2413ZM23.4235 14.1183L24.0594 15.4801H23.1791C22.5575 15.4801 21.9577 15.7056 21.4902 16.115L19.5422 17.8205C19.097 18.2103 18.5669 18.4814 17.9998 18.617C17.9947 18.494 17.9915 18.3709 17.9915 18.2481C17.9915 16.0466 18.808 13.9601 20.2952 12.3477H20.6431C21.8301 12.3477 22.9215 13.0427 23.4235 14.1183ZM18.9201 12.3477C17.6341 14.0379 16.9367 16.0939 16.9367 18.2481C16.9367 18.3869 16.9397 18.5261 16.9456 18.665H10.1772C10.1831 18.526 10.186 18.3868 10.186 18.2481C10.186 16.0939 9.4887 14.0379 8.20267 12.3477H18.9201ZM3.69915 14.1183C4.20123 13.0427 5.29268 12.3478 6.47968 12.3478H6.82757C8.31489 13.9602 9.13132 16.0467 9.13132 18.2482C9.13132 18.3798 9.12784 18.5119 9.12193 18.6439C8.50879 18.5195 7.93404 18.2383 7.45695 17.8205L5.50899 16.1151C5.0414 15.7056 4.4416 15.4801 3.82007 15.4801H3.06338L3.69915 14.1183ZM25.8692 17.8278L25.1181 22.5975C24.9713 23.5299 24.4953 24.381 23.7779 24.9943C23.0605 25.6076 22.1457 25.9453 21.2019 25.9453H5.79724C4.85345 25.9453 3.93862 25.6076 3.22122 24.9944C2.50382 24.3811 2.02784 23.5299 1.88103 22.5976L1.12988 17.8278C1.07894 17.5041 1.1718 17.1757 1.38469 16.9267C1.59721 16.6781 1.90655 16.5354 2.23361 16.5349L2.23477 16.5349L2.23625 16.5348H3.82002C4.18589 16.5348 4.53895 16.6676 4.81422 16.9086L6.76223 18.614C7.61489 19.3606 8.70855 19.7717 9.84181 19.7717H17.1573C18.2905 19.7717 19.3842 19.3606 20.2369 18.6141L22.1849 16.9086C22.4601 16.6676 22.8132 16.5348 23.1791 16.5348H24.764C25.0916 16.5348 25.4016 16.6776 25.6144 16.9267C25.8273 17.1757 25.9202 17.5041 25.8692 17.8278Z'
        fill='black'
      />
      <path
        d='M16.9219 22.0819C16.9024 21.5752 16.4892 21.1648 15.9824 21.1485C15.5223 21.1337 15.1311 21.4387 15.0139 21.8582C14.9803 21.9789 14.8723 22.0638 14.7469 22.0638H12.376C12.2507 22.0638 12.1427 21.9789 12.109 21.8582C11.9918 21.4388 11.6007 21.1338 11.1405 21.1485C10.6337 21.1648 10.2205 21.5751 10.2009 22.0819C10.189 22.3927 10.3231 22.6724 10.54 22.8585C10.3231 23.0445 10.189 23.3242 10.2009 23.635C10.2204 24.1418 10.6337 24.5522 11.1405 24.5684C11.6006 24.5832 11.9918 24.2782 12.109 23.8587C12.1427 23.738 12.2507 23.6531 12.376 23.6531H14.7469C14.8723 23.6531 14.9803 23.738 15.0139 23.8587C15.1311 24.2781 15.5223 24.5832 15.9824 24.5684C16.4892 24.5522 16.9024 24.1418 16.9219 23.635C16.934 23.3242 16.7997 23.0445 16.5828 22.8585C16.7997 22.6724 16.934 22.3927 16.9219 22.0819Z'
        fill='#FF5B00'
      />
      <path
        d='M7.02739 3.16406H7.96031L6.65445 4.46992C6.50368 4.62074 6.45854 4.84755 6.54012 5.04462C6.62176 5.24164 6.81403 5.37015 7.02734 5.37015H9.23343C9.52462 5.37015 9.76077 5.13401 9.76077 4.84281C9.76077 4.55161 9.52462 4.31546 9.23343 4.31546H8.3005L9.60636 3.0096C9.75713 2.85878 9.80227 2.63197 9.72069 2.4349C9.63906 2.23789 9.44679 2.10938 9.23348 2.10938H7.02739C6.73619 2.10938 6.50004 2.34552 6.50004 2.63672C6.50004 2.92792 6.73614 3.16406 7.02739 3.16406Z'
        fill='#FF5B00'
      />
      <path
        d='M17.8897 1.05469H18.8226L17.5168 2.36055C17.366 2.51137 17.3208 2.73818 17.4024 2.93525C17.4841 3.13226 17.6763 3.26078 17.8896 3.26078H20.0957C20.3869 3.26078 20.6231 3.02463 20.6231 2.73343C20.6231 2.44223 20.3869 2.20609 20.0957 2.20609H19.1628L20.4687 0.900229C20.6194 0.749408 20.6646 0.522598 20.583 0.325529C20.5014 0.128514 20.3091 0 20.0958 0H17.8897C17.5985 0 17.3623 0.236145 17.3623 0.527344C17.3623 0.818543 17.5985 1.05469 17.8897 1.05469Z'
        fill='#FF5B00'
      />
      <path
        d='M13.0684 6.72119H14.0013L12.6955 8.02705C12.5447 8.17787 12.4996 8.40468 12.5811 8.60175C12.6628 8.79877 12.855 8.92728 13.0684 8.92728H15.2744C15.5656 8.92728 15.8018 8.69114 15.8018 8.39994C15.8018 8.10874 15.5656 7.87259 15.2744 7.87259H14.3415L15.6474 6.56673C15.7981 6.41591 15.8433 6.1891 15.7617 5.99203C15.6801 5.79502 15.4878 5.6665 15.2745 5.6665H13.0684C12.7772 5.6665 12.5411 5.90265 12.5411 6.19385C12.5411 6.48505 12.7772 6.72119 13.0684 6.72119Z'
        fill='#FF5B00'
      />
    </g>
    <defs>
      <clipPath id='clip0_2045_578'>
        <rect width='27' height='27' fill='white' />
      </clipPath>
    </defs>
  </svg>
)

export default LaboratoryMovingIcon
