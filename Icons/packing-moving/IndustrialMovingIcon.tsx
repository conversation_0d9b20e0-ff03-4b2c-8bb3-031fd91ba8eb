import * as React from "react"

const IndustrialMovingIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width='28'
    height='28'
    viewBox='0 0 28 28'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <g clipPath='url(#clip0_2070_5)'>
      <path
        d='M6.06673 11.6667C4.77816 11.6667 3.7334 12.7114 3.7334 14C3.7334 15.2886 4.77816 16.3334 6.06673 16.3334C7.35531 16.3334 8.40006 15.2886 8.40006 14C8.39847 12.7119 7.35485 11.6683 6.06673 11.6667ZM6.06673 15.4C5.29359 15.4 4.66673 14.7732 4.66673 14C4.66673 13.2269 5.29359 12.6 6.06673 12.6C6.83988 12.6 7.46673 13.2269 7.46673 14C7.46673 14.7732 6.83988 15.4 6.06673 15.4Z'
        fill='#FF5B00'
      />
      <path
        d='M15.3997 6.53336C16.6883 6.53336 17.7331 5.48861 17.7331 4.20003C17.7331 2.91146 16.6883 1.8667 15.3997 1.8667C14.1112 1.8667 13.0664 2.91146 13.0664 4.20003C13.068 5.48815 14.1116 6.53177 15.3997 6.53336ZM15.3997 2.80003C16.1729 2.80003 16.7997 3.42689 16.7997 4.20003C16.7997 4.97318 16.1729 5.60003 15.3997 5.60003C14.6266 5.60003 13.9997 4.97318 13.9997 4.20003C13.9997 3.42689 14.6266 2.80003 15.3997 2.80003Z'
        fill='#FF5B00'
      />
      <path
        d='M26.6 14.1978V12.6C26.5963 11.4274 25.7213 10.4405 24.5579 10.2961L19.5747 4.65231C19.5911 4.50192 19.5995 4.35107 19.6 4.2C19.6 1.88034 17.7197 0 15.4 0C13.0803 0 11.2 1.88034 11.2 4.2C11.2 4.27428 11.2075 4.34652 11.2112 4.41943L5.81784 9.81253C4.12617 9.90892 2.65918 11.015 2.10068 12.6148C1.54219 14.2146 2.00247 15.9934 3.26667 17.1215V24.2667H0.466667C0.208952 24.2667 0 24.4756 0 24.7333V27.5333C0 27.791 0.208952 28 0.466667 28H18.2C18.4577 28 18.6667 27.791 18.6667 27.5333V24.7333C18.6667 24.4756 18.4577 24.2667 18.2 24.2667H8.86666V17.1215C9.93786 16.1786 10.4458 14.749 10.2097 13.3415L15.1621 8.38792C15.2414 8.39248 15.3198 8.4 15.4 8.4C15.6374 8.39863 15.8742 8.37698 16.108 8.33551L21.9333 12.9268V14.1987C21.0843 14.5694 20.5349 15.4071 20.5333 16.3333V21C20.5324 21.1622 20.6158 21.3131 20.7534 21.3988C20.8913 21.4844 21.0636 21.4924 21.2085 21.42L23.0752 20.4866C23.2342 20.4073 23.3342 20.2444 23.3333 20.0667V16.8H25.2V20.0667C25.1991 20.2444 25.2991 20.4073 25.4582 20.4866L27.3248 21.42C27.4698 21.4924 27.642 21.4844 27.7799 21.3988C27.9175 21.3131 28.0009 21.1622 28 21V16.3333C27.9989 15.4066 27.4492 14.5685 26.6 14.1978ZM15.4 0.933333C17.2042 0.933333 18.6667 2.39577 18.6667 4.2C18.6667 6.00423 17.2042 7.46667 15.4 7.46667C13.5958 7.46667 12.1333 6.00423 12.1333 4.2C12.1354 2.39668 13.5967 0.935384 15.4 0.933333ZM17.7333 27.0667H0.933333V25.2H17.7333V27.0667ZM7.93333 24.2667H4.2V17.7566C5.37441 18.3474 6.75892 18.3474 7.93333 17.7566V24.2667ZM6.06667 17.2667C4.26243 17.2667 2.8 15.8042 2.8 14C2.8 12.1958 4.26243 10.7333 6.06667 10.7333C7.8709 10.7333 9.33333 12.1958 9.33333 14C9.33128 15.8033 7.86999 17.2646 6.06667 17.2667ZM9.91256 12.32C9.38141 11.1134 8.31592 10.225 7.03349 9.91894L11.4203 5.53232C11.8392 6.77601 12.8133 7.75355 14.0556 8.17646L9.91256 12.32ZM19.2999 5.75085L23.4299 10.4287C22.7903 10.6775 22.2927 11.1954 22.0696 11.8444L17.1855 7.99577C18.1483 7.54118 18.9036 6.73909 19.2999 5.75085ZM22.8667 12.6C22.8667 11.8269 23.4935 11.2 24.2667 11.2C25.0398 11.2 25.6667 11.8269 25.6667 12.6V14H22.8667V12.6ZM27.0667 20.2449L26.1333 19.7782V16.3333C26.1333 16.0756 25.9244 15.8667 25.6667 15.8667H22.8667C22.6089 15.8667 22.4 16.0756 22.4 16.3333V19.7782L21.4667 20.2449V16.3333C21.4667 15.5602 22.0935 14.9333 22.8667 14.9333H25.6667C26.4398 14.9333 27.0667 15.5602 27.0667 16.3333V20.2449Z'
        fill='black'
      />
      <path
        d='M5.59948 19.6C5.34176 19.6 5.13281 19.8089 5.13281 20.0666V22.8666C5.13281 23.1244 5.34176 23.3333 5.59948 23.3333C5.85719 23.3333 6.06615 23.1244 6.06615 22.8666V20.0666C6.06615 19.8089 5.85719 19.6 5.59948 19.6Z'
        fill='#FF5B00'
      />
      <path
        d='M10.8704 7.60339L9.00376 9.47006C8.88253 9.58718 8.83377 9.76082 8.87661 9.92397C8.91922 10.0871 9.04659 10.2145 9.20975 10.2571C9.3729 10.2999 9.54653 10.2512 9.66365 10.13L11.5303 8.26329C11.7071 8.08009 11.7046 7.7891 11.5246 7.60909C11.3446 7.42908 11.0536 7.42657 10.8704 7.60339Z'
        fill='#FF5B00'
      />
    </g>
    <defs>
      <clipPath id='clip0_2070_5'>
        <rect width='28' height='28' fill='white' />
      </clipPath>
    </defs>
  </svg>
)

export default IndustrialMovingIcon
