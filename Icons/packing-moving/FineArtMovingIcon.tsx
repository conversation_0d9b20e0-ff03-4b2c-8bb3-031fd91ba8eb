import * as React from "react"

const FineArtMovingIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width='27'
    height='27'
    viewBox='0 0 27 27'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <g clipPath='url(#clip0_2045_585)'>
      <path
        d='M7.8047 8.75397C6.64165 8.75397 5.69531 9.70031 5.69531 10.8634C5.69531 12.0264 6.64165 12.9728 7.8047 12.9728C8.96796 12.9728 9.9141 12.0264 9.9141 10.8634C9.9141 9.70031 8.96796 8.75397 7.8047 8.75397ZM7.8047 11.9181C7.22318 11.9181 6.75001 11.4449 6.75001 10.8634C6.75001 10.2818 7.22318 9.80866 7.8047 9.80866C8.38643 9.80866 8.8594 10.2818 8.8594 10.8634C8.8594 11.4449 8.38643 11.9181 7.8047 11.9181Z'
        fill='#FF5B00'
      />
      <path
        d='M15.7149 8.22664C15.7149 7.06359 14.7687 6.11725 13.6055 6.11725C12.4424 6.11725 11.4961 7.06359 11.4961 8.22664C11.4961 9.38969 12.4424 10.336 13.6055 10.336C14.7687 10.336 15.7149 9.38969 15.7149 8.22664ZM12.5508 8.22664C12.5508 7.64512 13.024 7.17194 13.6055 7.17194C14.1872 7.17194 14.6602 7.64512 14.6602 8.22664C14.6602 8.80816 14.1872 9.28134 13.6055 9.28134C13.024 9.28134 12.5508 8.80816 12.5508 8.22664Z'
        fill='#FF5B00'
      />
      <path
        d='M4.11328 17.1915C4.11328 18.3546 5.05962 19.3009 6.22267 19.3009C7.38593 19.3009 8.33206 18.3546 8.33206 17.1915C8.33206 16.0285 7.38593 15.0822 6.22267 15.0822C5.05962 15.0822 4.11328 16.0285 4.11328 17.1915ZM6.22267 16.1368C6.8044 16.1368 7.27737 16.61 7.27737 17.1915C7.27737 17.7731 6.8044 18.2462 6.22267 18.2462C5.64115 18.2462 5.16798 17.7731 5.16798 17.1915C5.16798 16.61 5.64115 16.1368 6.22267 16.1368Z'
        fill='#FF5B00'
      />
      <path
        d='M8.85938 21.4103C8.85938 22.5733 9.80572 23.5197 10.9688 23.5197C12.132 23.5197 13.0782 22.5733 13.0782 21.4103C13.0782 20.2472 12.132 19.3009 10.9688 19.3009C9.80572 19.3009 8.85938 20.2472 8.85938 21.4103ZM12.0235 21.4103C12.0235 21.9918 11.5505 22.465 10.9688 22.465C10.3872 22.465 9.91407 21.9918 9.91407 21.4103C9.91407 20.8288 10.3872 20.3556 10.9688 20.3556C11.5505 20.3556 12.0235 20.8288 12.0235 21.4103Z'
        fill='#FF5B00'
      />
      <path
        d='M23.1132 5.01516C23.1132 3.76044 21.2877 0.821097 20.9216 0.244722C20.8248 0.0922859 20.6569 0 20.4764 0C20.2958 0 20.1279 0.0922859 20.0313 0.244722C19.7114 0.748381 18.2779 3.05553 17.9205 4.46701C14.606 3.10538 11.1512 3.14246 8.11755 4.58834C5.10116 6.02598 2.66857 8.82257 1.80668 12.3434C1.73726 12.6265 1.9105 12.9118 2.19333 12.981C2.23556 12.9913 2.27779 12.9962 2.3192 12.9962C2.5565 12.9962 2.77218 12.8349 2.8311 12.5941C3.5803 9.53346 5.72636 6.89631 8.57136 5.54024C11.4481 4.16934 14.7535 4.19633 17.9077 5.60946C18.0731 6.32529 18.5331 6.93998 19.1659 7.30294C18.8555 8.43283 18.6136 10.1477 18.4762 12.2151C18.4204 13.0543 18.3827 13.9453 18.3642 14.8677C16.3617 16.0168 15.0429 18.2032 15.2081 20.8146C15.2707 21.8013 15.3653 23.2925 13.8117 24.6467C12.702 25.6143 11.0686 25.8802 9.54943 25.3409C5.77848 24.0026 3.05048 20.573 2.59915 16.6034C2.56639 16.314 2.30457 16.1064 2.01577 16.1389C1.72634 16.1719 1.51849 16.4331 1.55125 16.7225C2.05758 21.1771 5.08819 24.8766 9.19676 26.3348C11.075 27.0017 13.1088 26.6587 14.5048 25.4417C16.4507 23.7453 16.3271 21.7953 16.2605 20.7476C16.1376 18.8016 16.9857 17.1553 18.3516 16.1346V16.1366C18.3516 18.6343 18.543 23.529 19.348 25.8112C19.4625 26.1363 19.7672 27 20.461 27C21.155 27 21.4597 26.1363 21.5742 25.8112C22.3743 23.5432 22.5704 18.6846 22.5704 16.1366C22.5704 15.7747 22.5673 15.4185 22.5613 15.071C22.7241 15.0688 22.8891 15.0686 23.0561 15.0712C24.0548 15.0859 24.962 14.4782 25.3157 13.5576C26.2849 11.0329 24.4835 8.51811 22.3586 6.8615C22.8353 6.37576 23.1132 5.71616 23.1132 5.01516ZM20.4764 1.53837C21.2372 2.82626 22.0585 4.43487 22.0585 5.01516C22.0585 5.93081 21.2978 6.61739 20.4433 6.59823C19.5933 6.57908 18.8944 5.88549 18.8944 5.01516C18.8944 4.43487 19.7155 2.82626 20.4764 1.53837ZM20.4764 7.6519C20.5696 7.6519 20.6623 7.64654 20.7539 7.63686C21.0122 8.60257 21.2261 10.0744 21.3593 11.8134C20.7661 11.8896 20.1563 11.8882 19.5629 11.8095C19.6961 10.0707 19.9104 8.59948 20.1691 7.63377C20.2707 7.64572 20.3732 7.6519 20.4764 7.6519ZM20.461 25.7511C19.609 23.9286 19.4063 18.4397 19.4063 16.1368C19.4063 15.8423 19.4133 15.1919 19.4133 15.1919C19.4255 14.3888 19.4527 13.6089 19.4936 12.8644C19.8187 12.9048 20.1475 12.9252 20.4764 12.9252C20.7953 12.9252 21.114 12.906 21.429 12.8679C21.4597 13.4297 21.4823 14.0066 21.4965 14.5906C21.5093 15.0854 21.5157 15.6056 21.5157 16.1368C21.5157 18.5116 21.3013 23.9542 20.461 25.7511ZM24.331 13.1796C24.1353 13.6888 23.629 14.0262 23.0726 14.0169C22.8915 14.0141 22.7119 14.0141 22.535 14.0167C22.5146 13.4053 22.4849 12.8034 22.4464 12.2213C22.4462 12.2178 22.4456 12.2143 22.4451 12.2108C22.3279 10.446 22.1343 8.93834 21.8873 7.83297C24.0216 9.56045 24.9527 11.5602 24.331 13.1796Z'
        fill='black'
      />
      <path
        d='M2.53126 14.5548C2.53126 14.8461 2.29519 15.0822 2.00391 15.0822C1.71284 15.0822 1.47656 14.8461 1.47656 14.5548C1.47656 14.2635 1.71284 14.0275 2.00391 14.0275C2.29519 14.0275 2.53126 14.2635 2.53126 14.5548Z'
        fill='black'
      />
    </g>
    <defs>
      <clipPath id='clip0_2045_585'>
        <rect width='27' height='27' fill='white' />
      </clipPath>
    </defs>
  </svg>
)

export default FineArtMovingIcon
