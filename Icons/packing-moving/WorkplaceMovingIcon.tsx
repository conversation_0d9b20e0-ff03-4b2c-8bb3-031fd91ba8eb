import * as React from "react"

const WorkplaceMovingIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width='27'
    height='27'
    viewBox='0 0 27 27'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <g clipPath='url(#clip0_2045_603)'>
      <path
        d='M26.4727 25.3593H24.8437V16.8508C24.8437 16.5596 24.6075 16.3234 24.3163 16.3234C24.0251 16.3234 23.789 16.5596 23.789 16.8508V25.3593H20.0023V20.9746C20.0023 20.6834 19.7661 20.4472 19.4749 20.4472H16.0362C15.745 20.4472 15.5089 20.6834 15.5089 20.9746V25.3593H11.8108V11.8871V3.65113H23.789V12.1947C23.789 12.4859 24.0251 12.722 24.3163 12.722C24.6075 12.722 24.8437 12.4859 24.8437 12.1947V3.12379C24.8437 2.83259 24.6075 2.59644 24.3163 2.59644H23.0189V1.11334C23.0189 0.822143 22.7828 0.585999 22.4916 0.585999H13.2413C12.9501 0.585999 12.7139 0.822143 12.7139 1.11334V2.59644H11.2835C10.9923 2.59644 10.7562 2.83259 10.7562 3.12379V11.3597H6.62238V9.70425C6.62238 9.41305 6.38624 9.1769 6.09504 9.1769H2.46122C2.17002 9.1769 1.93388 9.41305 1.93388 9.70425V11.8871V25.3593H0.527344C0.236145 25.3593 0 25.5955 0 25.8867C0 26.1779 0.236145 26.414 0.527344 26.414H26.4727C26.7639 26.414 27 26.1779 27 25.8867C27 25.5955 26.7639 25.3593 26.4727 25.3593ZM13.7686 1.64069H21.9643V2.59644H13.7686V1.64069ZM2.98856 10.2316H5.5677V11.3597H2.98856V10.2316ZM7.48085 25.3591H6.19745V23.4761H7.48085V25.3591ZM10.7562 25.3593H8.53559V22.9488C8.53559 22.6576 8.29944 22.4215 8.00824 22.4215H5.67016C5.37896 22.4215 5.14281 22.6576 5.14281 22.9488V25.3593H2.98856V12.4144H6.09504H10.7562V25.3593ZM18.9476 25.3591H16.5636V21.5019H18.9476V25.3591Z'
        fill='black'
      />
      <path
        d='M24.7547 14.2889C24.493 13.8967 23.8655 14.0379 23.7951 14.5044C23.7235 14.9797 24.3044 15.2936 24.6639 14.9785C24.8616 14.8052 24.8979 14.5074 24.7547 14.2889Z'
        fill='black'
      />
      <path
        d='M16.8994 6.06757C16.8994 5.77637 16.6633 5.54022 16.3721 5.54022H14.1367C13.8455 5.54022 13.6094 5.77637 13.6094 6.06757V8.30292C13.6094 8.59412 13.8455 8.83027 14.1367 8.83027H16.3721C16.6633 8.83027 16.8994 8.59412 16.8994 8.30292V6.06757ZM15.8447 7.77553H14.6641V6.59486H15.8447V7.77553Z'
        fill='#FF5B00'
      />
      <path
        d='M21.8408 6.06757C21.8408 5.77637 21.6047 5.54022 21.3135 5.54022H19.0781C18.7869 5.54022 18.5508 5.77637 18.5508 6.06757V8.30292C18.5508 8.59412 18.7869 8.83027 19.0781 8.83027H21.3135C21.6047 8.83027 21.8408 8.59412 21.8408 8.30292V6.06757ZM20.7861 7.77553H19.6055V6.59486H20.7861V7.77553Z'
        fill='#FF5B00'
      />
      <path
        d='M16.8994 10.6786C16.8994 10.3875 16.6633 10.1513 16.3721 10.1513H14.1367C13.8455 10.1513 13.6094 10.3875 13.6094 10.6786V12.914C13.6094 13.2052 13.8455 13.4414 14.1367 13.4414H16.3721C16.6633 13.4414 16.8994 13.2052 16.8994 12.914V10.6786ZM15.8447 12.3867H14.6641V11.206H15.8447V12.3867Z'
        fill='#FF5B00'
      />
      <path
        d='M21.8408 10.6786C21.8408 10.3875 21.6047 10.1513 21.3135 10.1513H19.0781C18.7869 10.1513 18.5508 10.3875 18.5508 10.6786V12.914C18.5508 13.2052 18.7869 13.4414 19.0781 13.4414H21.3135C21.6047 13.4414 21.8408 13.2052 21.8408 12.914V10.6786ZM20.7861 12.3867H19.6055V11.206H20.7861V12.3867Z'
        fill='#FF5B00'
      />
      <path
        d='M16.3721 14.7623H14.1367C13.8455 14.7623 13.6094 14.9985 13.6094 15.2897V17.525C13.6094 17.8162 13.8455 18.0524 14.1367 18.0524H16.3721C16.6633 18.0524 16.8994 17.8162 16.8994 17.525V15.2897C16.8994 14.9985 16.6633 14.7623 16.3721 14.7623ZM15.8447 16.9977H14.6641V15.817H15.8447V16.9977Z'
        fill='#FF5B00'
      />
      <path
        d='M21.3135 14.7623H19.0781C18.7869 14.7623 18.5508 14.9985 18.5508 15.2897V17.525C18.5508 17.8162 18.7869 18.0524 19.0781 18.0524H21.3135C21.6047 18.0524 21.8408 17.8162 21.8408 17.525V15.2897C21.8408 14.9985 21.6047 14.7623 21.3135 14.7623ZM20.7861 16.9977H19.6055V15.817H20.7861V16.9977Z'
        fill='#FF5B00'
      />
      <path
        d='M4.67383 13.6295C4.38263 13.6295 4.14648 13.8657 4.14648 14.1569V20.7636C4.14648 21.0548 4.38263 21.2909 4.67383 21.2909C4.96503 21.2909 5.20117 21.0548 5.20117 20.7636V14.1569C5.20117 13.8657 4.96503 13.6295 4.67383 13.6295Z'
        fill='#FF5B00'
      />
      <path
        d='M6.83398 21.2909C7.12518 21.2909 7.36133 21.0548 7.36133 20.7636V14.1569C7.36133 13.8657 7.12518 13.6295 6.83398 13.6295C6.54278 13.6295 6.30664 13.8657 6.30664 14.1569V20.7636C6.30664 21.0548 6.54273 21.2909 6.83398 21.2909Z'
        fill='#FF5B00'
      />
      <path
        d='M8.99414 21.2909C9.28534 21.2909 9.52148 21.0548 9.52148 20.7636V14.1569C9.52148 13.8657 9.28534 13.6295 8.99414 13.6295C8.70294 13.6295 8.4668 13.8657 8.4668 14.1569V20.7636C8.4668 21.0548 8.70289 21.2909 8.99414 21.2909Z'
        fill='#FF5B00'
      />
    </g>
    <defs>
      <clipPath id='clip0_2045_603'>
        <rect width='27' height='27' fill='white' />
      </clipPath>
    </defs>
  </svg>
)

export default WorkplaceMovingIcon
