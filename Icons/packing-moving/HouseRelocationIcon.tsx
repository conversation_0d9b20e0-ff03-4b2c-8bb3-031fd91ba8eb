import React from "react"

const HouseRelocationIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width='43'
    height='27'
    viewBox='0 0 43 27'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <path
      d='M41.5238 11.4247L37.2044 10.5488L32.8104 3.08343C32.7557 2.99067 32.6778 2.91378 32.5843 2.86035C32.4909 2.80692 32.3851 2.77879 32.2774 2.77876H30.1684V0.618524C30.1685 0.537285 30.1525 0.456833 30.1214 0.381769C30.0904 0.306706 30.0448 0.238502 29.9873 0.181058C29.9299 0.123613 29.8617 0.0780547 29.7866 0.0469875C29.7116 0.0159202 29.6311 -4.63609e-05 29.5499 1.0111e-07H27.3939C27.2298 1.0111e-07 27.0725 0.0651653 26.9565 0.181161C26.8405 0.297157 26.7754 0.454481 26.7754 0.618524V2.77876H25.1577V0.618524C25.1577 0.537285 25.1417 0.456833 25.1107 0.381769C25.0796 0.306706 25.034 0.238502 24.9766 0.181058C24.9192 0.123613 24.851 0.0780547 24.7759 0.0469875C24.7008 0.0159202 24.6204 -4.63609e-05 24.5391 1.0111e-07H0.618524C0.537285 -4.63609e-05 0.456834 0.0159202 0.38177 0.0469875C0.306706 0.0780547 0.238502 0.123613 0.181058 0.181058C0.123613 0.238502 0.0780551 0.306706 0.0469878 0.381769C0.0159205 0.456833 -4.63609e-05 0.537285 1.01108e-07 0.618524V23.7711C1.01108e-07 23.9351 0.0651657 24.0925 0.181161 24.2085C0.297157 24.3245 0.454481 24.3896 0.618524 24.3896H4.33179C4.80964 25.1858 5.48551 25.8447 6.29358 26.3021C7.10165 26.7596 8.0144 27 8.94297 27C9.87154 27 10.7843 26.7596 11.5924 26.3021C12.4004 25.8447 13.0763 25.1858 13.5542 24.3896H28.4652C28.943 25.1858 29.6189 25.8447 30.427 26.3021C31.235 26.7596 32.1478 27 33.0763 27C34.0049 27 34.9177 26.7596 35.7257 26.3021C36.5338 25.8447 37.2097 25.1858 37.6875 24.3896H41.4008C41.5648 24.3896 41.7222 24.3245 41.8382 24.2085C41.9542 24.0925 42.0193 23.9351 42.0193 23.7711V12.0312C42.0193 11.8884 41.97 11.7501 41.8797 11.6396C41.7894 11.529 41.6637 11.4531 41.5238 11.4247ZM40.7816 15.9636H39.0812V14.8181H40.7823L40.7816 15.9636ZM35.7273 10.4817H28.0124V6.64966H33.4738L35.7273 10.4817ZM28.0124 1.23563H28.9314V2.77735H28.0124V1.23563ZM31.9236 4.01439L32.7457 5.4112H27.3939C27.2298 5.4112 27.0725 5.47636 26.9565 5.59236C26.8405 5.70836 26.7754 5.86568 26.7754 6.02972V11.1002C26.7754 11.2642 26.8405 11.4216 26.9565 11.5376C27.0725 11.6536 27.2298 11.7187 27.3939 11.7187H36.7499L40.7823 12.5366V13.581H38.4626C38.2986 13.581 38.1413 13.6462 38.0253 13.7622C37.9093 13.8782 37.8441 14.0355 37.8441 14.1995V16.5821C37.8441 16.6633 37.86 16.7438 37.8911 16.8188C37.9222 16.8939 37.9677 16.9621 38.0252 17.0196C38.0826 17.077 38.1508 17.1226 38.2259 17.1536C38.3009 17.1847 38.3814 17.2007 38.4626 17.2006H40.7823V18.8431H37.6992C37.2227 18.0416 36.546 17.3779 35.7355 16.9169C34.9251 16.4559 34.0087 16.2135 33.0763 16.2135C32.144 16.2135 31.2276 16.4559 30.4172 16.9169C29.6067 17.3779 28.93 18.0416 28.4535 18.8431H25.1577V4.01581L31.9236 4.01439ZM23.9206 1.23563V18.8431H13.5648C13.0882 18.0416 12.4116 17.3779 11.6011 16.9169C10.7907 16.4559 9.87429 16.2135 8.94191 16.2135C8.00953 16.2135 7.09316 16.4559 6.28271 16.9169C5.47227 17.3779 4.79559 18.0416 4.31906 18.8431H1.23705V1.23563H23.9206ZM1.23705 23.1526V20.0801H3.77299C3.47343 21.0826 3.47515 22.151 3.77794 23.1526H1.23705ZM8.94209 25.7631C8.12 25.7631 7.31637 25.5193 6.63283 25.0626C5.9493 24.6058 5.41655 23.9567 5.10197 23.1972C4.78738 22.4376 4.70509 21.6019 4.8655 20.7956C5.02591 19.9893 5.42181 19.2487 6.00314 18.6674C6.58447 18.0861 7.32511 17.6903 8.13141 17.53C8.93771 17.3696 9.77346 17.452 10.5329 17.7666C11.2924 18.0813 11.9416 18.6141 12.3982 19.2977C12.8549 19.9812 13.0986 20.7849 13.0986 21.607C13.0977 22.7091 12.6597 23.7658 11.8805 24.5452C11.1014 25.3247 10.0442 25.7618 8.94209 25.7631ZM14.108 23.1526C14.4108 22.151 14.4125 21.0826 14.1129 20.0801H27.9064C27.6068 21.0826 27.6085 22.151 27.9113 23.1526H14.108ZM33.0765 25.7645C32.2545 25.7646 31.4509 25.5209 30.7673 25.0643C30.0838 24.6076 29.551 23.9586 29.2363 23.1991C28.9216 22.4397 28.8392 21.604 28.9994 20.7978C29.1597 19.9915 29.5554 19.2508 30.1366 18.6695C30.7178 18.0881 31.4583 17.6921 32.2645 17.5316C33.0707 17.371 33.9064 17.4532 34.666 17.7676C35.4255 18.082 36.0748 18.6146 36.5316 19.298C36.9885 19.9814 37.2324 20.7849 37.2327 21.607C37.2318 22.7091 36.7937 23.7659 36.0145 24.5454C35.2353 25.3248 34.1787 25.7633 33.0765 25.7645ZM40.7816 23.154H38.2407C38.5435 22.1525 38.5452 21.084 38.2456 20.0815H40.7816V23.154Z'
      fill='black'
    />
    <path
      d='M8.94313 19.2109C8.46935 19.211 8.00623 19.3516 7.61232 19.6148C7.21842 19.8781 6.91142 20.2522 6.73014 20.69C6.54887 21.1277 6.50145 21.6093 6.5939 22.074C6.68635 22.5387 6.9145 22.9655 7.24952 23.3005C7.58453 23.6355 8.01136 23.8637 8.47603 23.9562C8.94071 24.0486 9.42236 24.0012 9.86009 23.8199C10.2978 23.6386 10.672 23.3316 10.9352 22.9377C11.1985 22.5438 11.339 22.0807 11.3391 21.6069C11.3385 20.9717 11.0858 20.3626 10.6366 19.9134C10.1874 19.4642 9.57838 19.2116 8.94313 19.2109ZM8.94313 22.7652C8.71398 22.7651 8.49 22.6971 8.29952 22.5697C8.10903 22.4423 7.96058 22.2613 7.87296 22.0496C7.78533 21.8379 7.76246 21.6049 7.80723 21.3802C7.852 21.1555 7.96241 20.9491 8.12449 20.7871C8.28657 20.6251 8.49304 20.5148 8.7178 20.4702C8.94256 20.4255 9.1755 20.4486 9.38718 20.5363C9.59886 20.6241 9.77976 20.7726 9.90701 20.9632C10.0343 21.1538 10.1021 21.3778 10.1021 21.6069C10.1017 21.9141 9.97945 22.2086 9.76217 22.4258C9.54488 22.6429 9.25032 22.765 8.94313 22.7652Z'
      fill='black'
    />
    <path
      d='M33.0773 19.2109C32.6035 19.2109 32.1403 19.3514 31.7464 19.6147C31.3524 19.8779 31.0454 20.252 30.864 20.6897C30.6827 21.1274 30.6352 21.6091 30.7276 22.0738C30.8201 22.5385 31.0482 22.9654 31.3832 23.3004C31.7182 23.6355 32.145 23.8636 32.6097 23.9561C33.0744 24.0486 33.556 24.0012 33.9938 23.8199C34.4315 23.6387 34.8057 23.3317 35.069 22.9378C35.3323 22.5439 35.4728 22.0807 35.4729 21.6069C35.4722 20.9718 35.2195 20.3628 34.7704 19.9137C34.3213 19.4645 33.7124 19.2118 33.0773 19.2109ZM33.0773 22.7652C32.8481 22.7652 32.6241 22.6972 32.4336 22.5699C32.243 22.4425 32.0945 22.2616 32.0068 22.0499C31.9192 21.8381 31.8962 21.6052 31.941 21.3804C31.9857 21.1557 32.0961 20.9492 32.2582 20.7872C32.4202 20.6252 32.6267 20.5149 32.8515 20.4702C33.0762 20.4255 33.3092 20.4485 33.5209 20.5363C33.7326 20.624 33.9135 20.7726 34.0408 20.9632C34.168 21.1537 34.2359 21.3778 34.2359 21.6069C34.2354 21.914 34.1132 22.2084 33.896 22.4255C33.6788 22.6426 33.3844 22.7648 33.0773 22.7652Z'
      fill='black'
    />
    <path
      d='M27.3939 14.1186H28.8098C28.9738 14.1186 29.1312 14.0534 29.2472 13.9374C29.3632 13.8214 29.4283 13.6641 29.4283 13.5001C29.4283 13.336 29.3632 13.1787 29.2472 13.0627C29.1312 12.9467 28.9738 12.8815 28.8098 12.8815H27.3939C27.2299 12.8815 27.0725 12.9467 26.9566 13.0627C26.8406 13.1787 26.7754 13.336 26.7754 13.5001C26.7754 13.6641 26.8406 13.8214 26.9566 13.9374C27.0725 14.0534 27.2299 14.1186 27.3939 14.1186Z'
      fill='black'
    />
    <path
      d='M14.8869 10.1071C14.7229 10.1071 14.5656 10.1723 14.4496 10.2883C14.3337 10.4043 14.2685 10.5616 14.2684 10.7256V13.1997H10.8884V10.7256C10.8884 10.5615 10.8232 10.4042 10.7073 10.2882C10.5913 10.1722 10.4339 10.1071 10.2699 10.1071C10.1058 10.1071 9.94852 10.1722 9.83253 10.2882C9.71653 10.4042 9.65137 10.5615 9.65137 10.7256V13.8182C9.65132 13.8994 9.66729 13.9799 9.69836 14.055C9.72942 14.13 9.77498 14.1982 9.83243 14.2557C9.88987 14.3131 9.95807 14.3587 10.0331 14.3897C10.1082 14.4208 10.1887 14.4368 10.2699 14.4367H14.8869C14.9681 14.4368 15.0486 14.4208 15.1237 14.3897C15.1987 14.3587 15.2669 14.3131 15.3244 14.2557C15.3818 14.1982 15.4274 14.13 15.4584 14.055C15.4895 13.9799 15.5055 13.8994 15.5054 13.8182V10.7256C15.5054 10.5615 15.4403 10.4042 15.3243 10.2882C15.2083 10.1722 15.0509 10.1071 14.8869 10.1071Z'
      fill='#FF5B00'
    />
    <path
      d='M9.17954 10.1424L12.5796 6.74226L15.9801 10.1424C16.0961 10.2584 16.2534 10.3235 16.4175 10.3235C16.5816 10.3235 16.7389 10.2584 16.8549 10.1424C16.9709 10.0264 17.0361 9.86904 17.0361 9.70499C17.0361 9.54094 16.9709 9.38361 16.8549 9.2676L15.5069 7.91993V5.96751C15.5069 5.80347 15.4417 5.64615 15.3257 5.53015C15.2097 5.41416 15.0524 5.34899 14.8883 5.34899C14.7243 5.34899 14.567 5.41416 14.451 5.53015C14.335 5.64615 14.2698 5.80347 14.2698 5.96751V6.6843L13.0172 5.43134C12.9012 5.31542 12.7438 5.25031 12.5798 5.25031C12.4158 5.25031 12.2585 5.31542 12.1424 5.43134L8.30477 9.26902C8.19629 9.38647 8.13751 9.54136 8.14075 9.7012C8.14399 9.86105 8.209 10.0134 8.32214 10.1264C8.43528 10.2393 8.58777 10.3041 8.74762 10.3071C8.90747 10.3101 9.06226 10.251 9.17954 10.1424Z'
      fill='#FF5B00'
    />
  </svg>
)

export default HouseRelocationIcon
