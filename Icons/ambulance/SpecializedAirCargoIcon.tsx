import * as React from "react"

const SpecializedAirCargoIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width='27'
    height='27'
    viewBox='0 0 27 27'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <path
      d='M25.9539 3.55222C25.473 3.10926 24.7642 2.873 23.7939 2.83082C21.9334 2.75488 19.3052 3.45941 16.7106 4.26519L11.1756 3.32019C11.0828 3.30332 10.99 3.32019 10.9098 3.36238L9.63578 4.05425C9.50921 4.12175 9.42906 4.25254 9.42484 4.39597C9.42062 4.53941 9.48812 4.67441 9.61046 4.75457L11.4161 5.9316C11.3823 5.94004 11.3444 5.95269 11.3106 5.96113C9.94796 6.34504 8.61484 6.69519 7.34078 7.00738L2.83515 2.93629C2.75921 2.86879 2.66218 2.83082 2.56093 2.83082H0.831246C0.679371 2.83082 0.540152 2.91519 0.468433 3.04597C0.396715 3.18097 0.405152 3.34129 0.489527 3.46785L3.62406 8.13379L2.49343 10.1335C2.43437 10.2389 2.42593 10.3697 2.46812 10.4836C2.51031 10.5975 2.60734 10.6861 2.72125 10.7241L3.97421 11.1291C4.0164 11.1418 4.05859 11.1502 4.10078 11.1502C4.20625 11.1502 4.30328 11.1122 4.38343 11.0363L5.49718 9.97738C6.805 10.2432 8.16765 10.3824 9.56406 10.3824C10.0956 10.3824 10.6652 10.3528 11.2684 10.298L9.72859 12.8124C9.65265 12.9389 9.64843 13.095 9.72015 13.2258C9.79187 13.3566 9.92687 13.4325 10.0745 13.4325H12.1291C12.243 13.4325 12.3527 13.3861 12.4286 13.3018L15.947 9.47957C20.7058 8.33629 25.8527 6.29863 26.2872 5.80926C26.8652 5.15535 26.4011 4.17238 26.3378 4.04582C26.2366 3.86019 26.1016 3.69566 25.9497 3.55644L25.9539 3.55222ZM22.9037 4.25254C23.2792 4.09222 23.6167 3.89394 23.9078 3.66191C23.9669 3.66191 24.0217 3.67035 24.0808 3.67879C24.182 3.68722 24.2791 3.69988 24.3719 3.71676C24.4478 3.72941 24.5153 3.74629 24.587 3.76316C24.6672 3.78426 24.7473 3.80535 24.8191 3.83066C24.8444 3.8391 24.8655 3.85175 24.8908 3.86019C24.4394 4.1091 23.8487 4.36222 23.1316 4.52254L22.9122 4.25254H22.9037ZM10.6314 4.45082L11.1798 4.15129L15.0147 4.80941C14.8586 4.86004 14.6983 4.91066 14.5464 4.96129C13.8123 5.19754 13.112 5.42535 12.4412 5.63207L10.6314 4.45082ZM2.40484 3.64504L6.37468 7.23941C5.33265 7.47566 4.63656 7.60644 4.29484 7.66551L1.59906 3.64504H2.40484ZM3.99953 10.2727L3.45531 10.0955L4.28218 8.6316L5.26937 9.06191L4.00375 10.2685L3.99953 10.2727ZM11.9519 12.6141H10.8044L13.4411 8.30676L16.5967 7.56426L11.9477 12.6141H11.9519ZM25.7008 5.24394C25.3548 5.52238 21.4356 7.18879 16.9595 8.37847L18.1028 7.13816C18.2252 7.00738 18.2462 6.8091 18.1534 6.65722C18.0648 6.50113 17.8834 6.42519 17.7062 6.46738L13.0867 7.55582C12.9812 7.58113 12.8884 7.64863 12.8336 7.74144L11.8042 9.42472C10.9984 9.52176 10.2433 9.57238 9.56406 9.57238C8.425 9.57238 7.30703 9.47535 6.22703 9.28972L6.27343 9.24332C6.37046 9.15051 6.41687 9.01129 6.39156 8.87629C6.36625 8.74129 6.27765 8.62738 6.15531 8.57254L5.48031 8.27722C5.96125 8.17597 6.57296 8.04519 7.31968 7.86379C8.66546 7.53894 10.083 7.16769 11.53 6.75847C12.5214 6.47582 13.6225 6.12144 14.7911 5.74176C17.2886 4.93175 20.0519 4.03738 22.2077 3.74629C22.1402 3.82644 22.0853 3.92347 22.0558 4.03316C21.9967 4.26097 22.0473 4.50566 22.1992 4.69129L22.5198 5.07941C22.6633 5.25238 22.8742 5.34941 23.0894 5.34941C23.1442 5.34941 23.1948 5.34519 23.2497 5.33254C24.2537 5.11316 25.0427 4.73347 25.5911 4.39176C25.5995 4.40441 25.608 4.41285 25.6122 4.4255C25.6712 4.53941 25.8611 5.04988 25.6923 5.24394H25.7008Z'
      fill='#FF5B00'
    />
    <path
      d='M26.0004 13.5841V17.9159C26.0004 18.208 25.7541 18.5 25.4093 18.5H19.9914C19.6959 18.5 19.4004 18.2566 19.4004 17.9159V13.5841C19.4004 13.292 19.6467 13 19.9914 13H25.4093C25.7541 13 26.0004 13.2434 26.0004 13.5841Z'
      stroke='#010101'
      strokeWidth='0.899353'
      strokeMiterlimit='10'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M21.8135 13V15.531L22.7493 14.9469L23.6359 15.531V13H21.8135Z'
      stroke='black'
      strokeWidth='0.899353'
      strokeMiterlimit='10'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M19.3998 19.0841V23.4159C19.3998 23.708 19.1535 24 18.8088 24H13.3908C13.0953 24 12.7998 23.7566 12.7998 23.4159V19.0841C12.7998 18.792 13.0461 18.5 13.3908 18.5H18.8088C19.1535 18.5 19.3998 18.7434 19.3998 19.0841Z'
      stroke='#010101'
      strokeWidth='0.899353'
      strokeMiterlimit='10'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M15.2139 18.5V21.031L16.1497 20.4469L17.0363 21.031V18.5H15.2139Z'
      stroke='black'
      strokeWidth='0.899353'
      strokeMiterlimit='10'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M26.0004 19.0841V23.4159C26.0004 23.708 25.7541 24 25.4093 24H19.9914C19.6959 24 19.4004 23.7566 19.4004 23.4159V19.0841C19.4004 18.792 19.6467 18.5 19.9914 18.5H25.4093C25.7541 18.5 26.0004 18.7434 26.0004 19.0841Z'
      stroke='#010101'
      strokeWidth='0.899353'
      strokeMiterlimit='10'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M21.8135 18.5V21.031L22.7493 20.4469L23.6359 21.031V18.5H21.8135Z'
      stroke='black'
      strokeWidth='0.899353'
      strokeMiterlimit='10'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  </svg>
)

export default SpecializedAirCargoIcon
