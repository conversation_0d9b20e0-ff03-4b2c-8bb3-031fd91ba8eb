import * as React from "react"

const TemperatureControlledCoffinsIcon = (
  props: React.SVGProps<SVGSVGElement>,
) => (
  <svg
    width='27'
    height='27'
    viewBox='0 0 27 27'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <path
      d='M23.8296 19.412C23.8545 19.3336 23.8541 19.2468 23.8283 19.1687L18.8469 4.09467C18.8166 4.00301 18.7538 3.9256 18.6704 3.87697L12.1052 0.053711C11.9936 -0.0112071 11.8574 -0.0176937 11.7402 0.036519L5.17306 3.07333C5.08441 3.11452 5.012 3.18935 4.97424 3.27958L3.18396 7.55537C3.16371 7.60373 3.15327 7.65557 3.15327 7.70804L3.15137 14.5675C3.15121 14.6701 3.19271 14.7728 3.26501 14.8458L11.278 23.1978C11.6125 23.5463 12.2112 23.0279 11.8488 22.6501L4.01197 14.4816L5.46917 11.0013L16.4206 22.4162L14.9634 25.8965L13.2436 24.1039C12.901 23.7468 12.3146 24.2782 12.6728 24.6516L14.81 26.8792C14.9232 27.0008 15.1122 27.0331 15.2614 26.9643L21.8285 23.9275C21.9182 23.886 21.9891 23.8124 22.0273 23.7213L23.8176 19.4455C23.8216 19.4343 23.8256 19.4232 23.8296 19.412ZM11.8852 0.841009L18.1428 4.48518L22.9664 19.0819L16.9757 21.8522L5.73275 10.1334L5.73454 3.68528L11.8852 0.841009ZM4.94302 5.40237L4.94171 10.213L3.94299 12.5983L3.94431 7.78773L4.94302 5.40237ZM21.357 23.2741L15.8533 25.8192L17.1911 22.6241L22.6947 20.0791L21.357 23.2741Z'
      fill='black'
    />
    <path
      d='M16.9517 20.7946C17.0279 20.8739 17.1316 20.916 17.2369 20.916C17.293 20.916 17.3496 20.904 17.4028 20.8795L21.7598 18.8647C21.942 18.7804 22.0322 18.5732 21.9697 18.3825L17.6213 5.11196C17.5912 5.01988 17.5282 4.94204 17.4445 4.89331L12.0253 1.73743C11.9137 1.67251 11.7775 1.66602 11.6603 1.72023L6.66555 4.02997C6.52565 4.09468 6.4361 4.23475 6.43605 4.38889L6.43457 9.69889C6.43457 9.8011 6.47412 9.89945 6.545 9.97312L16.9517 20.7946ZM7.22698 4.64187L11.8053 2.52472L16.9165 5.5012L21.1083 18.2944L17.3265 20.0432L7.22566 9.53968L7.22698 4.64187Z'
      fill='black'
    />
    <path
      d='M17.5454 14.2845L16.3992 12.4188C16.1401 11.997 15.4543 12.3919 15.7252 12.8328L16.8714 14.6986C17.0978 15.0672 16.5028 15.4413 16.274 15.069L12.6312 9.13941C12.5262 8.9686 12.3102 8.90337 12.1282 8.98743L9.9252 10.0061C9.51728 10.1948 9.26225 9.53752 9.6323 9.36639L11.6724 8.42299C11.8844 8.32496 11.9657 8.056 11.8434 7.85698L10.4571 5.6004C10.2306 5.23182 10.8256 4.85777 11.0545 5.23003L12.5348 7.6396C12.6398 7.81041 12.8558 7.87559 13.0378 7.79158L15.2408 6.77293C15.6489 6.58429 15.9038 7.24149 15.5338 7.41262L13.4936 8.35607C13.2816 8.45411 13.2004 8.72306 13.3226 8.92209L14.6798 11.1312C14.9389 11.5529 15.6247 11.158 15.3538 10.7171L14.2296 8.88723L15.8658 8.13062C17.1604 7.53196 16.2583 5.43059 14.9088 6.05493L13.0248 6.92613L11.7284 4.81595C10.9679 3.57834 9.01972 4.77196 9.78302 6.01453L10.9364 7.89194L9.30032 8.64849C8.00571 9.24715 8.90771 11.3484 10.2573 10.7242L12.1413 9.85299L15.6001 15.4831C16.3568 16.7146 18.3118 15.532 17.5454 14.2845Z'
      fill='#FF5B00'
    />
  </svg>
)

export default TemperatureControlledCoffinsIcon
