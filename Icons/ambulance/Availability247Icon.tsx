import * as React from "react"

const Availability247Icon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width='27'
    height='27'
    viewBox='0 0 27 27'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <path
      d='M25.979 13.2436C25.9146 13.2436 25.8509 13.2562 25.7915 13.2808C25.7321 13.3054 25.6781 13.3415 25.6326 13.387C25.5871 13.4325 25.551 13.4865 25.5264 13.5459C25.5018 13.6053 25.4892 13.669 25.4892 13.7334C25.4893 16.3993 24.5832 18.9862 22.9196 21.0695C21.256 23.1527 18.9336 24.6086 16.3336 25.1982C13.7337 25.7878 11.0105 25.4761 8.61095 24.3143C6.21142 23.1525 4.27808 21.2095 3.12817 18.8043C1.97826 16.399 1.68008 13.6743 2.28257 11.0773C2.88506 8.48028 4.35243 6.16518 6.44389 4.5119C8.53535 2.85862 11.1267 1.96535 13.7926 1.97869C16.4586 1.99203 19.0408 2.91118 21.1157 4.5853H19.0502C18.9204 4.5853 18.7958 4.63691 18.7039 4.72876C18.6121 4.82061 18.5605 4.94519 18.5605 5.07509C18.5605 5.20499 18.6121 5.32956 18.7039 5.42142C18.7958 5.51327 18.9204 5.56487 19.0502 5.56487H22.3926C22.5224 5.56437 22.6467 5.51261 22.7384 5.42086C22.8302 5.32912 22.8819 5.20483 22.8824 5.07509V1.7327C22.8824 1.6028 22.8308 1.47822 22.739 1.38637C22.6471 1.29452 22.5225 1.24292 22.3926 1.24292C22.2627 1.24292 22.1382 1.29452 22.0463 1.38637C21.9545 1.47822 21.9029 1.6028 21.9029 1.7327V3.9659C19.3874 1.8621 16.1573 0.809846 12.8853 1.02831C9.61335 1.24677 6.5518 2.71911 4.33835 5.13865C2.12491 7.55818 0.930228 10.7384 1.00315 14.0168C1.07608 17.2953 2.41098 20.4192 4.72982 22.7379C6.51076 24.5188 8.7798 25.7317 11.25 26.223C13.7203 26.7144 16.2807 26.4622 18.6076 25.4984C20.9345 24.5345 22.9233 22.9023 24.3226 20.8082C25.7219 18.714 26.4687 16.252 26.4688 13.7334C26.4688 13.669 26.4561 13.6053 26.4315 13.5459C26.4069 13.4865 26.3708 13.4325 26.3253 13.387C26.2798 13.3415 26.2258 13.3054 26.1664 13.2808C26.107 13.2562 26.0433 13.2436 25.979 13.2436Z'
      fill='black'
    />
    <path
      d='M16.4408 10.2182L13.945 17.0752C13.9228 17.1357 13.9128 17.1999 13.9155 17.2643C13.9182 17.3286 13.9335 17.3918 13.9607 17.4502C13.9878 17.5086 14.0262 17.5611 14.0737 17.6046C14.1212 17.6481 14.1767 17.6819 14.2373 17.7039C14.2978 17.7259 14.3621 17.7358 14.4264 17.7329C14.4908 17.7301 14.5539 17.7146 14.6122 17.6873C14.6706 17.66 14.7229 17.6214 14.7664 17.5738C14.8098 17.5263 14.8434 17.4706 14.8652 17.41L17.361 10.553C17.4054 10.431 17.3995 10.2963 17.3446 10.1786C17.2897 10.061 17.1903 9.9699 17.0683 9.9255C16.9463 9.8811 16.8116 9.887 16.6939 9.9419C16.5762 9.99679 16.4852 10.0962 16.4408 10.2182Z'
      fill='#FF5B00'
    />
    <path
      d='M9.60009 12.8475C9.60072 12.6099 9.55424 12.3745 9.46331 12.155C9.37238 11.9354 9.23882 11.7361 9.07036 11.5685C8.38687 10.8845 7.19589 10.8845 6.51239 11.5685C6.34385 11.7361 6.21023 11.9354 6.11926 12.1549C6.02829 12.3745 5.98178 12.6099 5.98243 12.8475C5.98243 12.9774 6.03403 13.102 6.12588 13.1938C6.21773 13.2857 6.34231 13.3373 6.47221 13.3373C6.60211 13.3373 6.72669 13.2857 6.81854 13.1938C6.91039 13.102 6.962 12.9774 6.962 12.8475C6.96174 12.7386 6.98308 12.6306 7.02479 12.53C7.0665 12.4293 7.12774 12.3379 7.20498 12.2611C7.36285 12.1101 7.5729 12.0258 7.79138 12.0258C8.00986 12.0258 8.21991 12.1101 8.37778 12.2611C8.5332 12.4166 8.62052 12.6274 8.62052 12.8473C8.62052 13.0671 8.5332 13.278 8.37778 13.4334L6.12592 15.6853C6.05742 15.7538 6.01076 15.841 5.99186 15.936C5.97295 16.0311 5.98265 16.1295 6.01972 16.219C6.05678 16.3085 6.11956 16.385 6.2001 16.4388C6.28065 16.4926 6.37534 16.5214 6.47221 16.5214H9.11031C9.24021 16.5214 9.36479 16.4698 9.45664 16.3779C9.54849 16.2861 9.60009 16.1615 9.60009 16.0316C9.60009 15.9017 9.54849 15.7771 9.45664 15.6853C9.36479 15.5934 9.24021 15.5418 9.11031 15.5418H7.65458L9.07036 14.126C9.23882 13.9585 9.37237 13.7593 9.4633 13.5398C9.55423 13.3204 9.60072 13.0851 9.60009 12.8475Z'
      fill='#FF5B00'
    />
    <path
      d='M12.3928 16.5214C12.4571 16.5214 12.5208 16.5087 12.5802 16.4841C12.6397 16.4595 12.6937 16.4234 12.7391 16.3779C12.7846 16.3325 12.8207 16.2785 12.8453 16.219C12.8699 16.1596 12.8826 16.0959 12.8826 16.0316V15.3012H13.1353C13.2652 15.3012 13.3898 15.2496 13.4817 15.1577C13.5735 15.0659 13.6251 14.9413 13.6251 14.8114C13.6251 14.6815 13.5735 14.5569 13.4817 14.4651C13.3898 14.3732 13.2652 14.3216 13.1353 14.3216H12.8826V11.5283C12.8825 11.4229 12.8485 11.3203 12.7855 11.2358C12.7225 11.1512 12.6339 11.0893 12.5329 11.0591C12.4319 11.0289 12.3238 11.0321 12.2248 11.0682C12.1258 11.1043 12.041 11.1714 11.9831 11.2595L9.83027 14.5426C9.7818 14.6165 9.75422 14.7021 9.75045 14.7904C9.74667 14.8787 9.76685 14.9663 9.80884 15.0441C9.85083 15.1218 9.91306 15.1868 9.98896 15.232C10.0648 15.2773 10.1516 15.3012 10.2399 15.3012H11.903V16.0316C11.903 16.0959 11.9156 16.1596 11.9402 16.219C11.9649 16.2785 12.0009 16.3325 12.0464 16.3779C12.0919 16.4234 12.1459 16.4595 12.2053 16.4841C12.2648 16.5087 12.3285 16.5214 12.3928 16.5214ZM11.1468 14.3216L11.903 13.1684V14.3216H11.1468Z'
      fill='#FF5B00'
    />
    <path
      d='M19.0579 16.0316C19.0579 16.1615 19.1095 16.2861 19.2014 16.378C19.2932 16.4698 19.4178 16.5214 19.5477 16.5214C19.6776 16.5214 19.8022 16.4698 19.8941 16.378C19.9859 16.2861 20.0375 16.1615 20.0375 16.0316V15.0233C20.036 14.4383 20.1505 13.8588 20.3744 13.3184C20.5982 12.7779 20.9271 12.2872 21.3418 11.8746C21.4103 11.8062 21.457 11.7189 21.4759 11.6239C21.4948 11.5289 21.4851 11.4304 21.448 11.3409C21.411 11.2514 21.3482 11.1749 21.2677 11.1211C21.1871 11.0673 21.0924 11.0386 20.9956 11.0386H18.1001C17.9702 11.0386 17.8457 11.0902 17.7538 11.182C17.662 11.2739 17.6104 11.3985 17.6104 11.5284C17.6104 11.6583 17.662 11.7828 17.7538 11.8747C17.8457 11.9665 17.9702 12.0181 18.1001 12.0181H19.9629C19.37 12.908 19.0551 13.9541 19.0579 15.0233V16.0316Z'
      fill='#FF5B00'
    />
  </svg>
)

export default Availability247Icon
