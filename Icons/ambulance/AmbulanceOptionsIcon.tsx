import * as React from "react"

const AmbulanceOptionsIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width='28'
    height='28'
    viewBox='0 0 28 28'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <path
      d='M19.9054 4.71902V5.56356C19.8927 6.13869 20.7619 6.13869 20.7492 5.56356V4.71902C20.7492 4.13863 19.9054 4.1621 19.9054 4.71902ZM17.8537 5.49437L18.274 6.22689C18.5524 6.72901 19.3024 6.29497 19.0056 5.80336L18.5854 5.07083C18.3189 4.60626 17.5636 4.98872 17.8537 5.49437ZM22.0733 5.07083L21.6498 5.80336C21.3532 6.29489 22.103 6.7289 22.3815 6.22689L22.8017 5.49437C23.0813 5.00688 22.3482 4.59545 22.0733 5.07083ZM2.60938 5.98379C1.91551 5.98379 1.34375 6.55558 1.34375 7.24943V19.0619V20.7494C1.34378 20.9824 1.53264 21.1713 1.76563 21.1713H3.90796C4.11521 22.598 5.34699 23.7034 6.82895 23.7034C8.31091 23.7034 9.53985 22.598 9.74664 21.1713H16.9531C17.5236 21.1793 17.5236 20.3195 16.9531 20.3276H9.74747C9.54177 18.9001 8.31179 17.7955 6.82895 17.7955C6.02786 17.7955 5.30014 18.1189 4.76654 18.64H2.1875V7.24943C2.1875 7.00843 2.36835 6.82755 2.60938 6.82755H16.1094C16.3504 6.82755 16.5312 7.00843 16.5312 7.24943V17.3744C16.5233 17.9449 17.383 17.9449 17.375 17.3744V9.35962H22.4375C22.6791 9.35962 22.8052 9.66303 22.8767 9.90096L24.144 14.1214C24.1642 14.1883 24.2007 14.2492 24.2502 14.2985L25.8133 15.8616V18.64H23.2343C22.7014 18.1189 21.9746 17.7955 21.1735 17.7955C20.3724 17.7955 19.6439 18.1189 19.1103 18.64H10.625C10.0544 18.6321 10.0544 19.4919 10.625 19.4838H18.508C18.3242 19.8679 18.2179 20.2951 18.2179 20.7478C18.2179 22.3738 19.5476 23.7034 21.1735 23.7034C22.6555 23.7034 23.8844 22.598 24.0912 21.1713H26.2344C26.4674 21.1713 26.6562 20.9824 26.6563 20.7494V19.0636L26.6571 15.6877C26.6571 15.5755 26.6123 15.4678 26.5327 15.3886L24.9193 13.7753L23.6867 9.65873C23.481 8.97183 23.074 8.51586 22.4375 8.51586H21.5938V8.09154C21.5937 7.39767 21.0228 6.82755 20.3289 6.82755C19.6351 6.82755 19.0617 7.39768 19.0617 8.09154V8.51586H17.375V7.24943C17.375 6.55558 16.8032 5.98379 16.1094 5.98379H2.60938ZM20.3289 7.67131C20.57 7.67131 20.7492 7.85052 20.7492 8.09154V8.51586H19.9054V8.09154C19.9054 7.85052 20.0879 7.67131 20.3289 7.67131ZM18.6414 10.2001C18.4072 10.1992 18.217 10.3893 18.2179 10.6236V13.9994C18.2188 14.2324 18.4085 14.4205 18.6414 14.4197H22.7836C23.0654 14.4196 23.2679 14.1486 23.1881 13.8783L22.1763 10.5025C22.1233 10.3229 21.9582 10.1997 21.7709 10.2001H18.6414ZM19.0617 11.0438H21.4561L22.2167 13.5759H19.0617V11.0438ZM18.6414 15.2642C18.0663 15.2515 18.0663 16.1207 18.6414 16.108H19.4852C20.0354 16.0952 20.0354 15.2764 19.4852 15.2642H18.6414ZM6.82895 18.64C7.99892 18.64 8.9375 19.5778 8.9375 20.7478C8.9375 21.9178 7.99892 22.8596 6.82895 22.8596C5.65898 22.8596 4.7171 21.9178 4.7171 20.7478C4.7171 19.5778 5.65898 18.64 6.82895 18.64ZM21.1735 18.64C22.3435 18.64 23.2812 19.5778 23.2812 20.7478C23.2812 21.9178 22.3435 22.8596 21.1735 22.8596C20.0035 22.8596 19.0617 21.9178 19.0617 20.7478C19.0617 19.5778 20.0035 18.64 21.1735 18.64ZM2.1875 19.4838H4.16339C4.03776 19.7464 3.95016 20.0296 3.90714 20.3276H2.1875V19.4838ZM6.82895 19.4838C6.13497 19.4838 5.5617 20.0538 5.56168 20.7478C5.56168 21.4418 6.13497 22.0159 6.82895 22.0159C7.52294 22.0159 8.09295 21.4418 8.09293 20.7478C8.09293 20.0538 7.52293 19.4838 6.82895 19.4838ZM21.1735 19.4838C20.4795 19.4838 19.9055 20.0538 19.9054 20.7478C19.9054 21.4418 20.4795 22.0159 21.1735 22.0159C21.8675 22.0159 22.4375 21.4418 22.4375 20.7478C22.4375 20.0538 21.8675 19.4838 21.1735 19.4838ZM23.8358 19.4838H25.8125V20.3276H24.092C24.0491 20.0296 23.9612 19.7464 23.8358 19.4838ZM6.82895 20.3276C7.06694 20.3276 7.24918 20.5098 7.24918 20.7478C7.24919 20.9858 7.06696 21.1721 6.82895 21.1721C6.59099 21.1721 6.40543 20.9858 6.40543 20.7478C6.40544 20.5098 6.59098 20.3276 6.82895 20.3276ZM21.1735 20.3276C21.4115 20.3276 21.5937 20.5098 21.5937 20.7478C21.5937 20.9858 21.4115 21.1721 21.1735 21.1721C20.9355 21.1721 20.7492 20.9858 20.7492 20.7478C20.7492 20.5098 20.9356 20.3276 21.1735 20.3276Z'
      fill='black'
    />
    <path
      d='M10.8571 15.5H9.14286C9.08602 15.5 9.03152 15.4774 8.99133 15.4372C8.95115 15.3971 8.92857 15.3425 8.92857 15.2857V13.5714H7.21429C7.15745 13.5714 7.10295 13.5489 7.06276 13.5087C7.02258 13.4685 7 13.414 7 13.3571V11.6429C7 11.586 7.02258 11.5315 7.06276 11.4913C7.10295 11.4511 7.15745 11.4286 7.21429 11.4286H8.92857V9.71429C8.92857 9.65745 8.95115 9.60295 8.99133 9.56276C9.03152 9.52258 9.08602 9.5 9.14286 9.5H10.8571C10.914 9.5 10.9685 9.52258 11.0087 9.56276C11.0489 9.60295 11.0714 9.65745 11.0714 9.71429V11.4286H12.7857C12.8425 11.4286 12.8971 11.4511 12.9372 11.4913C12.9774 11.5315 13 11.586 13 11.6429V13.3571C13 13.414 12.9774 13.4685 12.9372 13.5087C12.8971 13.5489 12.8425 13.5714 12.7857 13.5714H11.0714V15.2857C11.0714 15.3425 11.0489 15.3971 11.0087 15.4372C10.9685 15.4774 10.914 15.5 10.8571 15.5ZM9.35714 15.0714H10.6429V13.3571C10.6429 13.3003 10.6654 13.2458 10.7056 13.2056C10.7458 13.1654 10.8003 13.1429 10.8571 13.1429H12.5714V11.8571H10.8571C10.8003 11.8571 10.7458 11.8346 10.7056 11.7944C10.6654 11.7542 10.6429 11.6997 10.6429 11.6429V9.92857H9.35714V11.6429C9.35714 11.6997 9.33457 11.7542 9.29438 11.7944C9.25419 11.8346 9.19969 11.8571 9.14286 11.8571H7.42857V13.1429H9.14286C9.19969 13.1429 9.25419 13.1654 9.29438 13.2056C9.33457 13.2458 9.35714 13.3003 9.35714 13.3571V15.0714Z'
      fill='#FF5B00'
    />
  </svg>
)

export default AmbulanceOptionsIcon
