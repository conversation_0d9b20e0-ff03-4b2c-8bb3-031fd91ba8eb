import React from "react"

const GrowthIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='42'
    height='42'
    viewBox='0 0 42 42'
    fill='none'
    {...props}
  >
    <path
      d='M28.0374 6.5C28.0374 6.10218 28.1955 5.72064 28.4768 5.43934C28.7581 5.15804 29.1396 5 29.5374 5H34.5374C34.9352 5 35.3168 5.15804 35.5981 5.43934C35.8794 5.72064 36.0374 6.10218 36.0374 6.5V11.5C36.0374 11.8978 35.8794 12.2794 35.5981 12.5607C35.3168 12.842 34.9352 13 34.5374 13C34.1396 13 33.7581 12.842 33.4768 12.5607C33.1955 12.2794 33.0374 11.8978 33.0374 11.5V10.12L24.0974 19.06C23.8162 19.3409 23.4349 19.4987 23.0374 19.4987C22.6399 19.4987 22.2587 19.3409 21.9774 19.06L17.0374 14.12L8.59742 22.56C8.46009 22.7074 8.29449 22.8256 8.11049 22.9076C7.92649 22.9895 7.72787 23.0336 7.52646 23.0372C7.32506 23.0407 7.125 23.0037 6.93822 22.9282C6.75145 22.8528 6.58178 22.7405 6.43934 22.5981C6.2969 22.4556 6.18461 22.286 6.10917 22.0992C6.03373 21.9124 5.99668 21.7124 6.00023 21.511C6.00379 21.3096 6.04787 21.1109 6.12985 20.9269C6.21184 20.7429 6.33004 20.5773 6.47742 20.44L15.9774 10.94C16.2587 10.6591 16.6399 10.5013 17.0374 10.5013C17.4349 10.5013 17.8162 10.6591 18.0974 10.94L23.0374 15.88L30.9174 8H29.5374C29.1396 8 28.7581 7.84196 28.4768 7.56066C28.1955 7.27936 28.0374 6.89782 28.0374 6.5ZM7.53742 29C7.93524 29 8.31677 29.158 8.59808 29.4393C8.87938 29.7206 9.03742 30.1022 9.03742 30.5V35.5C9.03742 35.8978 8.87938 36.2794 8.59808 36.5607C8.31677 36.842 7.93524 37 7.53742 37C7.13959 37 6.75806 36.842 6.47676 36.5607C6.19545 36.2794 6.03742 35.8978 6.03742 35.5V30.5C6.03742 30.1022 6.19545 29.7206 6.47676 29.4393C6.75806 29.158 7.13959 29 7.53742 29ZM17.0374 24.5C17.0374 24.1022 16.8794 23.7206 16.5981 23.4393C16.3168 23.158 15.9352 23 15.5374 23C15.1396 23 14.7581 23.158 14.4768 23.4393C14.1955 23.7206 14.0374 24.1022 14.0374 24.5V35.5C14.0374 35.8978 14.1955 36.2794 14.4768 36.5607C14.7581 36.842 15.1396 37 15.5374 37C15.9352 37 16.3168 36.842 16.5981 36.5607C16.8794 36.2794 17.0374 35.8978 17.0374 35.5V24.5ZM23.5374 27C23.9352 27 24.3168 27.158 24.5981 27.4393C24.8794 27.7206 25.0374 28.1022 25.0374 28.5V35.5C25.0374 35.8978 24.8794 36.2794 24.5981 36.5607C24.3168 36.842 23.9352 37 23.5374 37C23.1396 37 22.7581 36.842 22.4768 36.5607C22.1955 36.2794 22.0374 35.8978 22.0374 35.5V28.5C22.0374 28.1022 22.1955 27.7206 22.4768 27.4393C22.7581 27.158 23.1396 27 23.5374 27ZM33.0374 20.5C33.0374 20.1022 32.8794 19.7206 32.5981 19.4393C32.3168 19.158 31.9352 19 31.5374 19C31.1396 19 30.7581 19.158 30.4768 19.4393C30.1955 19.7206 30.0374 20.1022 30.0374 20.5V35.5C30.0374 35.8978 30.1955 36.2794 30.4768 36.5607C30.7581 36.842 31.1396 37 31.5374 37C31.9352 37 32.3168 36.842 32.5981 36.5607C32.8794 36.2794 33.0374 35.8978 33.0374 35.5V20.5Z'
      fill='black'
    />
    <path
      d='M7.53906 30V36'
      stroke='#FF5B00'
      strokeWidth='3'
      strokeLinecap='round'
    />
    <path
      d='M15.5391 24V36'
      stroke='#FF5B00'
      strokeWidth='3'
      strokeLinecap='round'
    />
    <path
      d='M23.5391 28V36M31.5391 20V36'
      stroke='#FF5B00'
      strokeWidth='3'
      strokeLinecap='round'
    />
  </svg>
)

export default GrowthIcon
