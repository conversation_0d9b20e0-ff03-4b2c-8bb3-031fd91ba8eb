import React from "react"

const GlobeIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width='40'
    height='40'
    viewBox='0 0 40 40'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <path
      d='M20.8333 31.667V36.667M17.5 36.667H24.1667'
      stroke='black'
      strokeWidth='2.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M22.4993 26.6668C16.056 26.6668 10.8327 21.4435 10.8327 15.0002C10.8327 8.55684 16.056 3.3335 22.4993 3.3335C28.9427 3.3335 34.166 8.55684 34.166 15.0002C34.166 21.4435 28.9427 26.6668 22.4993 26.6668Z'
      stroke='#FF5B00'
      strokeWidth='2.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M14.166 6.66683C15.256 6.73016 15.8193 7.26516 16.7877 8.2885C18.5377 10.1385 20.2877 10.2918 21.4543 9.67516C23.2027 8.75016 21.7327 7.2535 23.786 6.44183C25.1243 5.9085 25.311 4.46683 24.566 3.3335M33.3327 16.6668C30.8327 16.6668 30.3893 18.7452 28.3327 18.3335C24.166 17.5002 22.986 18.4318 22.986 20.4185C22.986 22.4052 22.986 22.4052 22.1193 23.8952C21.556 24.8652 21.3577 25.8335 22.481 26.6668'
      stroke='#FF5B00'
      strokeWidth='2.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M10.833 3.3335C9.25032 4.86518 7.99202 6.69967 7.13303 8.72774C6.27404 10.7558 5.83192 12.936 5.83301 15.1385C5.83301 24.2668 13.2947 31.6668 22.4997 31.6668C26.8567 31.6773 31.0446 29.9812 34.1663 26.9418'
      stroke='black'
      strokeWidth='2.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  </svg>
)

export default GlobeIcon
