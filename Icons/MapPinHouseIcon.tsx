import React from "react"

const MapPinHouseIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='24'
    height='25'
    viewBox='0 0 24 25'
    fill='none'
    {...props}
  >
    <path
      d='M18 10.75C18 8.62827 17.1571 6.59344 15.6569 5.09315C14.1566 3.59285 12.1217 2.75 10 2.75C7.87827 2.75 5.84344 3.59285 4.34315 5.09315C2.84285 6.59344 2 8.62827 2 10.75C2 15.743 7.539 20.943 9.399 22.549C9.57237 22.679 9.78329 22.7492 10 22.749M13 10.75C13 12.4069 11.6569 13.75 10 13.75C8.34315 13.75 7 12.4069 7 10.75C7 9.09315 8.34315 7.75 10 7.75C11.6569 7.75 13 9.09315 13 10.75Z'
      stroke='black'
      strokeWidth='2'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      fillRule='evenodd'
      clipRule='evenodd'
      d='M18 13.75L13 17.0833V23.75H16V19.75H20V23.75H23V17.0833L18 13.75Z'
      fill='#FF5B00'
    />
  </svg>
)

export default MapPinHouseIcon
