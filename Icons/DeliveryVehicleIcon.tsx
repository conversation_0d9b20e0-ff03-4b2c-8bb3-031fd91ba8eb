import React from "react"

const DeliveryVehicleIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='14'
    height='15'
    viewBox='0 0 14 15'
    fill='none'
    {...props}
  >
    <path
      d='M7.73822 9.06603H1.47398C1.2405 9.06603 1.04688 8.8724 1.04688 8.63892V5.00566C1.04688 4.82343 1.16076 4.66396 1.32591 4.60132C1.49676 4.53868 1.68469 4.58995 1.79859 4.72662C2.14597 5.14234 2.68697 5.37582 3.22228 5.35873C3.67786 5.34734 4.09926 5.17651 4.41816 4.87469C4.56623 4.74941 4.68582 4.60133 4.77694 4.43618C4.95347 4.13436 5.0389 3.80406 5.03321 3.46807C5.02182 2.94415 4.79404 2.46579 4.4068 2.11841C4.27012 1.99882 4.22455 1.81088 4.28719 1.64574C4.34984 1.48059 4.5093 1.3667 4.68584 1.3667H8.87717C9.11066 1.3667 9.30428 1.56032 9.30428 1.79381V7.48857C9.30428 8.36556 8.60382 9.06603 7.73822 9.06603ZM1.90109 8.21181H7.73822C8.13116 8.21181 8.45006 7.89291 8.45006 7.49997V2.23231H5.56852C5.76783 2.60247 5.87603 3.0182 5.88742 3.451C5.89881 3.94645 5.77352 4.43618 5.52295 4.86329C5.38627 5.10817 5.19835 5.34166 4.99334 5.5125C4.53776 5.9453 3.91133 6.20156 3.24504 6.21865C2.76668 6.23573 2.30542 6.11614 1.90679 5.89974V8.21181H1.90109Z'
      fill='#171717'
    />
    <path
      d='M11.1551 12.483H10.5856C10.3521 12.483 10.1585 12.2894 10.1585 12.0559C10.1585 11.663 9.83959 11.3441 9.44665 11.3441C9.05371 11.3441 8.7348 11.663 8.7348 12.0559C8.7348 12.2894 8.54118 12.483 8.30769 12.483H6.02979C5.79631 12.483 5.60268 12.2894 5.60268 12.0559C5.60268 11.663 5.28378 11.3441 4.89084 11.3441C4.4979 11.3441 4.17899 11.663 4.17899 12.0559C4.17899 12.2894 3.98537 12.483 3.75189 12.483H3.18241C2.00359 12.483 1.04688 11.5263 1.04688 10.3475V8.63906C1.04688 8.40558 1.2405 8.21195 1.47398 8.21195H7.73822C8.13116 8.21195 8.45006 7.89305 8.45006 7.50011V3.51378C8.45006 3.28029 8.64369 3.08667 8.87717 3.08667H9.92502C10.4888 3.08667 11.007 3.3885 11.286 3.87825L12.2599 5.58098C12.3339 5.71196 12.3339 5.87711 12.2599 6.00808C12.1858 6.13906 12.0434 6.21879 11.8897 6.21879H11.1551C11.0753 6.21879 11.0127 6.28143 11.0127 6.36116V8.06958C11.0127 8.14931 11.0753 8.21195 11.1551 8.21195H12.8635C13.097 8.21195 13.2906 8.40558 13.2906 8.63906V10.3475C13.2906 11.5263 12.3339 12.483 11.1551 12.483ZM10.9558 11.6288H11.1551C11.8612 11.6288 12.4364 11.0536 12.4364 10.3475V9.06617H11.1551C10.6084 9.06617 10.1585 8.61628 10.1585 8.06958V6.36116C10.1585 5.81446 10.6027 5.36457 11.1551 5.36457L10.5457 4.29966C10.4204 4.07756 10.1813 3.94088 9.92502 3.94088H9.30428V7.50011C9.30428 8.36571 8.60382 9.06617 7.73822 9.06617H1.90109V10.3475C1.90109 11.0536 2.47626 11.6288 3.18241 11.6288H3.38171C3.56964 10.9739 4.1733 10.4899 4.89084 10.4899C5.60838 10.4899 6.21204 10.9739 6.39996 11.6288H7.94322C8.13115 10.9739 8.73481 10.4899 9.45235 10.4899C10.1699 10.4899 10.7678 10.9739 10.9558 11.6288Z'
      fill='#171717'
    />
    <path
      d='M4.89125 13.6219C4.02565 13.6219 3.3252 12.9214 3.3252 12.0558C3.3252 11.1902 4.02565 10.4897 4.89125 10.4897C5.75686 10.4897 6.45731 11.1902 6.45731 12.0558C6.45731 12.9214 5.75686 13.6219 4.89125 13.6219ZM4.89125 11.344C4.49832 11.344 4.17941 11.6629 4.17941 12.0558C4.17941 12.4487 4.49832 12.7677 4.89125 12.7677C5.28419 12.7677 5.6031 12.4487 5.6031 12.0558C5.6031 11.6629 5.28419 11.344 4.89125 11.344Z'
      fill='#171717'
    />
    <path
      d='M9.44692 13.6219C8.58131 13.6219 7.88086 12.9214 7.88086 12.0558C7.88086 11.1902 8.58131 10.4897 9.44692 10.4897C10.3125 10.4897 11.013 11.1902 11.013 12.0558C11.013 12.9214 10.3125 13.6219 9.44692 13.6219ZM9.44692 11.344C9.05398 11.344 8.73507 11.6629 8.73507 12.0558C8.73507 12.4487 9.05398 12.7677 9.44692 12.7677C9.83986 12.7677 10.1588 12.4487 10.1588 12.0558C10.1588 11.6629 9.83986 11.344 9.44692 11.344Z'
      fill='#171717'
    />
    <path
      d='M12.8632 9.0661H11.1548C10.6081 9.0661 10.1582 8.61621 10.1582 8.06951V6.36109C10.1582 5.81439 10.6081 5.3645 11.1548 5.3645H11.8894C12.0432 5.3645 12.1855 5.44423 12.2596 5.58091L13.2334 7.28933C13.2676 7.35198 13.2903 7.42601 13.2903 7.50004V8.63899C13.2903 8.87247 13.0967 9.0661 12.8632 9.0661ZM11.1548 6.21872C11.0751 6.21872 11.0124 6.28136 11.0124 6.36109V8.06951C11.0124 8.14924 11.0751 8.21188 11.1548 8.21188H12.4361V7.61394L11.6388 6.21872H11.1548Z'
      fill='#171717'
    />
    <path
      d='M3.17609 6.21862C2.39022 6.21862 1.6499 5.87692 1.14876 5.27897C1.06903 5.19355 0.983622 5.07396 0.90959 4.96006C0.641937 4.55573 0.493873 4.07738 0.482483 3.57624C0.459704 2.7448 0.812786 1.96462 1.4506 1.435C1.93465 1.03637 2.51549 0.819983 3.13053 0.808594C3.80251 0.814289 4.47452 1.03639 4.97566 1.48627C5.54513 1.98741 5.87542 2.68787 5.89251 3.45097C5.90389 3.94641 5.7786 4.43615 5.52803 4.86326C5.39136 5.10813 5.20344 5.34163 4.99842 5.51247C4.54284 5.94527 3.91642 6.20153 3.25013 6.21862C3.22165 6.21862 3.19887 6.21862 3.17609 6.21862ZM3.17609 1.66281C3.1647 1.66281 3.15332 1.66281 3.14193 1.66281C2.72621 1.6685 2.32756 1.82227 1.99157 2.09562C1.55877 2.45438 1.31961 2.98969 1.331 3.55347C1.34239 3.89516 1.4392 4.21977 1.62144 4.48742C1.67269 4.56715 1.72393 4.64115 1.78657 4.70949C2.15104 5.14229 2.69205 5.37009 3.22166 5.3587C3.67724 5.34731 4.09864 5.17648 4.41755 4.87466C4.56561 4.74938 4.6852 4.6013 4.77632 4.43615C4.95286 4.13433 5.03829 3.80403 5.03259 3.46804C5.0212 2.94412 4.79342 2.46576 4.40618 2.11838C4.0645 1.82795 3.63167 1.66281 3.17609 1.66281Z'
      fill='#171717'
    />
    <path
      d='M2.86929 4.51016C2.76109 4.51016 2.65857 4.47028 2.57315 4.39055L1.99797 3.84388C1.82713 3.67873 1.82146 3.41108 1.98661 3.24024C2.15175 3.06939 2.41941 3.06369 2.59025 3.22884L2.86929 3.49647L3.76337 2.63089C3.93421 2.46574 4.20186 2.47142 4.36701 2.64226C4.53216 2.8131 4.52645 3.08079 4.35561 3.24594L3.16539 4.39625C3.07997 4.47029 2.97179 4.51016 2.86929 4.51016Z'
      fill='#171717'
    />
  </svg>
)

export default DeliveryVehicleIcon
