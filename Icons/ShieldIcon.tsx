import React from "react"

const ShieldIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width='40'
    height='40'
    viewBox='0 0 40 40'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <mask
      id='mask0_750_809'
      // style="mask-type:luminance"
      maskUnits='userSpaceOnUse'
      x='0'
      y='0'
      width='40'
      height='40'
    >
      <path d='M40 0H0V40H40V0Z' fill='white' />
    </mask>
    <g mask='url(#mask0_750_809)'>
      <path
        d='M23.3604 3.6286L23.3605 3.62864L32.6771 8.27862C33.2008 8.54045 33.5247 9.08013 33.5247 9.65018V23.0335C33.5247 25.7652 32.3378 27.9851 30.1512 30.1552C27.9604 32.3294 24.7765 34.4433 20.7995 36.9612L20.7995 36.9612L20.797 36.9628C20.5698 37.1143 20.2945 37.1918 19.9997 37.1918C19.7058 37.1918 19.4291 37.1148 19.1831 36.9612C14.77 34.1718 11.5851 31.7038 9.50454 29.4222C7.4243 27.141 6.45801 25.0573 6.45801 23.0335V9.65018C6.45801 9.06417 6.78129 8.54077 7.30559 8.27862L16.6219 3.62877C18.737 2.5794 21.2624 2.5796 23.3604 3.6286ZM9.61035 10.5717L9.54134 10.6063V10.6835V23.0335C9.54134 24.3934 10.443 25.9743 12.1746 27.7581C13.911 29.5469 16.5031 31.5611 19.9316 33.7883L19.999 33.8322L20.0669 33.7889C23.6996 31.4726 26.2936 29.6579 27.9792 27.9911C29.6681 26.3212 30.458 24.7869 30.458 23.0335V10.6835V10.6062L30.3889 10.5717L21.9895 6.37197C21.9894 6.37191 21.9893 6.37185 21.9892 6.37179C20.7371 5.73734 19.2455 5.73735 17.9934 6.37183C17.9933 6.37187 17.9933 6.37192 17.9932 6.37197L9.61035 10.5717Z'
        fill='#010101'
        stroke='#939393'
        strokeWidth='0.25'
      />
      <path
        d='M15 18L19 21.5L25 15.5'
        stroke='#FF5B00'
        strokeWidth='2.5'
        strokeLinecap='round'
      />
    </g>
  </svg>
)

export default ShieldIcon
