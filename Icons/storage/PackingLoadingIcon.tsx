import React from "react"

interface PackingLoadingIconProps extends React.SVGProps<SVGSVGElement> {}

const PackingLoadingIcon: React.FC<PackingLoadingIconProps> = (props) => {
  return (
    <svg
      width='27'
      height='27'
      viewBox='0 0 27 27'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <g clipPath='url(#clip0_2045_55)'>
        <path
          d='M5.48438 20.3719C5.48438 20.5376 5.35006 20.6719 5.18437 20.6719H4.94063C4.77494 20.6719 4.64062 20.5376 4.64062 20.3719V10.4251C4.64062 10.2594 4.77494 10.1251 4.94063 10.1251H22.0594C22.2251 10.1251 22.3594 10.2594 22.3594 10.4251V17.4188C22.3594 17.5845 22.2251 17.7188 22.0594 17.7188H21.8156C21.6499 17.7188 21.5156 17.5845 21.5156 17.4188V11.2688C21.5156 11.1031 21.3813 10.9688 21.2156 10.9688H5.78438C5.61869 10.9688 5.48438 11.1031 5.48438 11.2688V20.3719Z'
          fill='black'
        />
        <path
          d='M22.3594 14.0438C22.3594 14.2095 22.2251 14.3438 22.0594 14.3438H4.94062C4.77494 14.3438 4.64062 14.2095 4.64062 14.0438V10.4251C4.64062 10.2594 4.77494 10.1251 4.94063 10.1251H22.0594C22.2251 10.1251 22.3594 10.2594 22.3594 10.4251V14.0438ZM5.48438 13.2001C5.48438 13.3657 5.61869 13.5001 5.78438 13.5001H21.2156C21.3813 13.5001 21.5156 13.3657 21.5156 13.2001V11.2688C21.5156 11.1031 21.3813 10.9688 21.2156 10.9688H5.78438C5.61869 10.9688 5.48438 11.1031 5.48438 11.2688V13.2001Z'
          fill='black'
        />
        <path
          d='M4.64062 12.1126C4.64062 11.9469 4.77494 11.8126 4.94063 11.8126H22.0594C22.2251 11.8126 22.3594 11.9469 22.3594 12.1126V12.3563C22.3594 12.522 22.2251 12.6563 22.0594 12.6563H4.94062C4.77494 12.6563 4.64062 12.522 4.64062 12.3563V12.1126Z'
          fill='black'
        />
        <path
          d='M20.6719 8.98131C20.6719 9.147 20.5376 9.28131 20.3719 9.28131H6.62812C6.46244 9.28131 6.32812 9.147 6.32812 8.98131V6.20631C6.32812 6.04063 6.46244 5.90631 6.62813 5.90631H20.3719C20.5376 5.90631 20.6719 6.04063 20.6719 6.20631V8.98131ZM7.17188 8.13756C7.17188 8.30325 7.30619 8.43756 7.47188 8.43756H19.5281C19.6938 8.43756 19.8281 8.30325 19.8281 8.13756V7.05006C19.8281 6.88438 19.6938 6.75006 19.5281 6.75006H7.47187C7.30619 6.75006 7.17188 6.88438 7.17188 7.05006V8.13756Z'
          fill='black'
        />
        <path
          d='M0.421875 26.0344C0.421875 25.8688 0.55619 25.7344 0.721875 25.7344H26.2781C26.4438 25.7344 26.5781 25.8688 26.5781 26.0344V26.2782C26.5781 26.4439 26.4438 26.5782 26.2781 26.5782H0.721874C0.556188 26.5782 0.421875 26.4439 0.421875 26.2782V26.0344Z'
          fill='black'
        />
        <path
          d='M9.70312 26.2782C9.70312 26.4439 9.56881 26.5782 9.40312 26.5782H3.25313C3.08744 26.5782 2.95312 26.4439 2.95312 26.2782V20.1282C2.95312 19.9625 3.08744 19.8282 3.25313 19.8282H9.40312C9.56881 19.8282 9.70312 19.9625 9.70312 20.1282V26.2782ZM3.79688 25.4344C3.79688 25.6001 3.93119 25.7344 4.09688 25.7344H8.55937C8.72506 25.7344 8.85938 25.6001 8.85938 25.4344V20.9719C8.85938 20.8063 8.72506 20.6719 8.55937 20.6719H4.09688C3.93119 20.6719 3.79688 20.8063 3.79688 20.9719V25.4344Z'
          fill='black'
        />
        <path
          d='M7.59375 24.6797C7.59375 24.5633 7.68819 24.4688 7.80469 24.4688V24.4688C7.92119 24.4688 8.01562 24.5633 8.01562 24.6797V24.6797C8.01562 24.7962 7.92119 24.8907 7.80469 24.8907V24.8907C7.68819 24.8907 7.59375 24.7962 7.59375 24.6797V24.6797Z'
          fill='black'
        />
        <path
          d='M6.75 24.6797C6.75 24.5633 6.84444 24.4688 6.96094 24.4688V24.4688C7.07744 24.4688 7.17188 24.5633 7.17188 24.6797V24.6797C7.17188 24.7962 7.07744 24.8907 6.96094 24.8907V24.8907C6.84444 24.8907 6.75 24.7962 6.75 24.6797V24.6797Z'
          fill='black'
        />
        <path
          d='M23.625 26.5782C23.2912 26.5782 22.965 26.4792 22.6875 26.2938C22.41 26.1084 22.1937 25.8448 22.066 25.5365C21.9382 25.2281 21.9048 24.8888 21.9699 24.5615C22.035 24.2341 22.1958 23.9334 22.4318 23.6974C22.6678 23.4614 22.9684 23.3007 23.2958 23.2356C23.6231 23.1705 23.9624 23.2039 24.2708 23.3316C24.5791 23.4594 24.8427 23.6757 25.0281 23.9532C25.2135 24.2307 25.3125 24.5569 25.3125 24.8907C25.3125 25.3382 25.1347 25.7675 24.8182 26.0839C24.5018 26.4004 24.0726 26.5782 23.625 26.5782ZM23.625 24.0469C23.4581 24.0469 23.295 24.0964 23.1562 24.1891C23.0175 24.2818 22.9093 24.4136 22.8455 24.5678C22.7816 24.722 22.7649 24.8916 22.7975 25.0553C22.83 25.219 22.9104 25.3693 23.0284 25.4873C23.1464 25.6053 23.2967 25.6857 23.4604 25.7182C23.6241 25.7508 23.7937 25.7341 23.9479 25.6702C24.1021 25.6063 24.2338 25.4982 24.3266 25.3594C24.4193 25.2207 24.4688 25.0576 24.4688 24.8907C24.4688 24.6669 24.3799 24.4523 24.2216 24.2941C24.0634 24.1358 23.8488 24.0469 23.625 24.0469Z'
          fill='black'
        />
        <path
          d='M15.6094 26.5782C15.2756 26.5782 14.9494 26.4792 14.6719 26.2938C14.3943 26.1084 14.1781 25.8448 14.0503 25.5365C13.9226 25.2281 13.8892 24.8888 13.9543 24.5615C14.0194 24.2341 14.1801 23.9334 14.4161 23.6974C14.6521 23.4614 14.9528 23.3007 15.2802 23.2356C15.6075 23.1705 15.9468 23.2039 16.2552 23.3316C16.5635 23.4594 16.8271 23.6757 17.0125 23.9532C17.1979 24.2307 17.2969 24.5569 17.2969 24.8907C17.2969 25.3382 17.1191 25.7675 16.8026 26.0839C16.4862 26.4004 16.0569 26.5782 15.6094 26.5782ZM15.6094 24.0469C15.4425 24.0469 15.2794 24.0964 15.1406 24.1891C15.0019 24.2818 14.8937 24.4136 14.8299 24.5678C14.766 24.722 14.7493 24.8916 14.7818 25.0553C14.8144 25.219 14.8948 25.3693 15.0128 25.4873C15.1308 25.6053 15.2811 25.6857 15.4448 25.7182C15.6084 25.7508 15.7781 25.7341 15.9323 25.6702C16.0864 25.6063 16.2182 25.4982 16.3109 25.3594C16.4036 25.2207 16.4531 25.0576 16.4531 24.8907C16.4531 24.6669 16.3642 24.4523 16.206 24.2941C16.0478 24.1358 15.8332 24.0469 15.6094 24.0469Z'
          fill='black'
        />
        <path
          d='M20.6719 22.0594C20.6719 22.2251 20.5376 22.3594 20.3719 22.3594H14.2219C14.0562 22.3594 13.9219 22.2251 13.9219 22.0594V15.9094C13.9219 15.7438 14.0562 15.6094 14.2219 15.6094H20.3719C20.5376 15.6094 20.6719 15.7438 20.6719 15.9094V22.0594ZM14.7656 21.2157C14.7656 21.3814 14.8999 21.5157 15.0656 21.5157H19.5281C19.6938 21.5157 19.8281 21.3814 19.8281 21.2157V16.7532C19.8281 16.5875 19.6938 16.4532 19.5281 16.4532H15.0656C14.8999 16.4532 14.7656 16.5875 14.7656 16.7532V21.2157Z'
          fill='black'
        />
        <path
          d='M18.5625 20.461C18.5625 20.3445 18.6569 20.2501 18.7734 20.2501V20.2501C18.8899 20.2501 18.9844 20.3445 18.9844 20.461V20.461C18.9844 20.5775 18.8899 20.6719 18.7734 20.6719V20.6719C18.6569 20.6719 18.5625 20.5775 18.5625 20.461V20.461Z'
          fill='black'
        />
        <path
          d='M17.7188 20.461C17.7188 20.3445 17.8132 20.2501 17.9297 20.2501V20.2501C18.0462 20.2501 18.1406 20.3445 18.1406 20.461V20.461C18.1406 20.5775 18.0462 20.6719 17.9297 20.6719V20.6719C17.8132 20.6719 17.7188 20.5775 17.7188 20.461V20.461Z'
          fill='black'
        />
        <path
          d='M7.59375 22.4813C7.59375 22.647 7.45944 22.7813 7.29375 22.7813H5.3625C5.19681 22.7813 5.0625 22.647 5.0625 22.4813V20.1282C5.0625 19.9625 5.19681 19.8282 5.3625 19.8282H7.29375C7.45944 19.8282 7.59375 19.9625 7.59375 20.1282V22.4813ZM5.90625 21.6376C5.90625 21.8032 6.04056 21.9376 6.20625 21.9376H6.45C6.61569 21.9376 6.75 21.8032 6.75 21.6376V20.9719C6.75 20.8063 6.61569 20.6719 6.45 20.6719H6.20625C6.04056 20.6719 5.90625 20.8063 5.90625 20.9719V21.6376Z'
          fill='#FF5B00'
        />
        <path
          d='M18.5625 18.2626C18.5625 18.4282 18.4282 18.5626 18.2625 18.5626H16.3312C16.1656 18.5626 16.0312 18.4282 16.0312 18.2626V15.9094C16.0312 15.7438 16.1656 15.6094 16.3313 15.6094H18.2625C18.4282 15.6094 18.5625 15.7438 18.5625 15.9094V18.2626ZM16.875 17.4188C16.875 17.5845 17.0093 17.7188 17.175 17.7188H17.4187C17.5844 17.7188 17.7188 17.5845 17.7188 17.4188V16.7532C17.7188 16.5875 17.5844 16.4532 17.4187 16.4532H17.175C17.0093 16.4532 16.875 16.5875 16.875 16.7532V17.4188Z'
          fill='#FF5B00'
        />
        <path
          d='M25.5783 21.9376H22.7812C22.6694 21.9376 22.5621 21.8931 22.4829 21.814C22.4038 21.7349 22.3594 21.6276 22.3594 21.5157V19.8282C22.3594 19.7163 22.4038 19.609 22.4829 19.5299C22.5621 19.4508 22.6694 19.4063 22.7812 19.4063H24.9455C25.0314 19.4062 25.1152 19.4324 25.1859 19.4812C25.2565 19.5301 25.3105 19.5993 25.3408 19.6797L25.9736 21.3672C25.9976 21.4311 26.0058 21.4998 25.9974 21.5675C25.989 21.6352 25.9643 21.6999 25.9254 21.756C25.8866 21.8121 25.8347 21.8579 25.7742 21.8895C25.7138 21.9211 25.6465 21.9376 25.5783 21.9376ZM23.2031 20.7938C23.2031 20.9595 23.3374 21.0938 23.5031 21.0938H24.5366C24.7461 21.0938 24.8911 20.8846 24.8175 20.6885L24.7261 20.4447C24.6822 20.3276 24.5703 20.2501 24.4452 20.2501H23.5031C23.3374 20.2501 23.2031 20.3844 23.2031 20.5501V20.7938Z'
          fill='black'
        />
        <path
          d='M26.1562 25.0126C26.1562 25.1782 26.0219 25.3126 25.8562 25.3126H25.1906C25.0249 25.3126 24.8906 25.1782 24.8906 25.0126V24.6797C24.8906 24.5633 24.9851 24.4688 25.1016 24.4688V24.4688C25.2181 24.4688 25.3125 24.3744 25.3125 24.2579V22.0687C25.3125 22.0328 25.306 21.9971 25.2934 21.9634L24.2494 19.1791C24.2055 19.062 24.0935 18.9844 23.9685 18.9844H22.2375C22.0718 18.9844 21.9375 19.1188 21.9375 19.2844V22.0594C21.9375 22.2251 21.8032 22.3594 21.6375 22.3594H13.8C13.6343 22.3594 13.5 22.4938 13.5 22.6594V24.1688C13.5 24.3345 13.6343 24.4688 13.8 24.4688H14.0437C14.2094 24.4688 14.3438 24.6031 14.3438 24.7688V25.0126C14.3438 25.1782 14.2094 25.3126 14.0437 25.3126H12.9562C12.7906 25.3126 12.6562 25.1782 12.6562 25.0126V21.8157C12.6562 21.65 12.7906 21.5157 12.9563 21.5157H20.7937C20.9594 21.5157 21.0938 21.3814 21.0938 21.2157V18.4407C21.0938 18.275 21.2281 18.1407 21.3938 18.1407H24.5532C24.6783 18.1407 24.7902 18.2183 24.8341 18.3353L26.1371 21.8098C26.1498 21.8435 26.1562 21.8792 26.1562 21.9152V25.0126Z'
          fill='black'
        />
        <path
          d='M16.875 24.7688C16.875 24.6031 17.0093 24.4688 17.175 24.4688H22.0594C22.2251 24.4688 22.3594 24.6031 22.3594 24.7688V25.0126C22.3594 25.1782 22.2251 25.3126 22.0594 25.3126H17.175C17.0093 25.3126 16.875 25.1782 16.875 25.0126V24.7688Z'
          fill='black'
        />
        <path
          d='M15.6094 25.3126C15.8424 25.3126 16.0312 25.1237 16.0312 24.8907C16.0312 24.6577 15.8424 24.4688 15.6094 24.4688C15.3764 24.4688 15.1875 24.6577 15.1875 24.8907C15.1875 25.1237 15.3764 25.3126 15.6094 25.3126Z'
          fill='black'
        />
        <path
          d='M23.625 25.3126C23.858 25.3126 24.0469 25.1237 24.0469 24.8907C24.0469 24.6577 23.858 24.4688 23.625 24.4688C23.392 24.4688 23.2031 24.6577 23.2031 24.8907C23.2031 25.1237 23.392 25.3126 23.625 25.3126Z'
          fill='black'
        />
        <path
          d='M24.3469 17.7188C24.1812 17.7188 24.0469 17.5845 24.0469 17.4188V6.77435C24.0469 6.66072 23.9827 6.55684 23.881 6.50602L13.6283 1.37965C13.5452 1.33812 13.4476 1.3374 13.364 1.37767L2.70111 6.51138C2.59727 6.56137 2.53125 6.66643 2.53125 6.78168V26.1563C2.53125 26.2682 2.4868 26.3755 2.40769 26.4547C2.32857 26.5338 2.22126 26.5782 2.10938 26.5782C1.99749 26.5782 1.89018 26.5338 1.81106 26.4547C1.73195 26.3755 1.6875 26.2682 1.6875 26.1563V6.32822C1.68757 6.24888 1.71001 6.17118 1.75225 6.10402C1.79449 6.03686 1.85481 5.98297 1.92628 5.94853L13.3169 0.464159C13.375 0.436238 13.4387 0.421987 13.5031 0.422499C13.5675 0.423011 13.631 0.438272 13.6886 0.467112L24.6573 5.95149C24.7273 5.98647 24.7862 6.04023 24.8274 6.10676C24.8686 6.17329 24.8905 6.24997 24.8906 6.32822V17.4188C24.8906 17.5845 24.7563 17.7188 24.5906 17.7188H24.3469Z'
          fill='black'
        />
      </g>
      <defs>
        <clipPath id='clip0_2045_55'>
          <rect width='27' height='27' fill='white' />
        </clipPath>
      </defs>
    </svg>
  )
}

export default PackingLoadingIcon
