import * as React from "react"

const LongTermStorageIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width='27'
    height='27'
    viewBox='0 0 27 27'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <g clipPath='url(#clip0_1263_7859)'>
      <path d='M6.89 11.75H8.89V20.5H6.89V11.75Z' fill='#043873' />
      <path d='M8.89 5.13H10.89V20.5H8.89V5.13Z' fill='#043873' />
      <path d='M18.09 8.44H20.09V20.5H18.09V8.44Z' fill='#043873' />
      <path d='M16.1 1.83H18.1V20.5H16.1V1.83Z' fill='#043873' />
      <path d='M12.13 15.07H14.13V20.5H12.13V15.07Z' fill='#043873' />
      <path d='M10.13 11.76H12.13V20.5H10.13V11.76Z' fill='#043873' />
      <path d='M4.9 15.06H6.9V20.5H4.9V15.06Z' fill='#043873' />
      <path d='M2.9 18.37H4.9V20.5H2.9V18.37Z' fill='#043873' />
      <path d='M20.08 15.06H22.08V20.5H20.08V15.06Z' fill='#043873' />
      <path d='M22.08 18.37H24.08V20.5H22.08V18.37Z' fill='#043873' />
      <path d='M14.12 8.44H16.12V20.5H14.12V8.44Z' fill='#043873' />
      <path d='M0.9 21.82H26.09V23.82H0.9V21.82Z' fill='#043873' />
      <path d='M2.9 20.5H4.9V21.82H2.9V20.5Z' fill='#043873' />
      <path d='M4.9 20.5H6.9V21.82H4.9V20.5Z' fill='#043873' />
      <path d='M6.89 20.5H8.89V21.82H6.89V20.5Z' fill='#043873' />
      <path d='M8.89 20.5H10.89V21.82H8.89V20.5Z' fill='#043873' />
      <path d='M10.13 20.5H12.13V21.82H10.13V20.5Z' fill='#043873' />
      <path d='M12.13 20.5H14.13V21.82H12.13V20.5Z' fill='#043873' />
      <path d='M14.12 20.5H16.12V21.82H14.12V20.5Z' fill='#043873' />
      <path d='M16.1 20.5H18.1V21.82H16.1V20.5Z' fill='#043873' />
      <path d='M18.09 20.5H20.09V21.82H18.09V20.5Z' fill='#043873' />
      <path d='M20.08 20.5H22.08V21.82H20.08V20.5Z' fill='#043873' />
      <path d='M22.08 20.5H24.08V21.82H22.08V20.5Z' fill='#043873' />
    </g>
    <defs>
      <clipPath id='clip0_1263_7859'>
        <rect width='27' height='27' fill='white' />
      </clipPath>
    </defs>
  </svg>
)

export default LongTermStorageIcon
