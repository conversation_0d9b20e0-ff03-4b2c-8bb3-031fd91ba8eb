import * as React from "react"

const DistributionSupportIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width='27'
    height='27'
    viewBox='0 0 27 27'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <path
      d='M2.17086 6.94003C2.17645 7.17903 2.32347 7.39097 2.54538 7.47982C2.76734 7.56873 3.01994 7.5169 3.18895 7.34788L3.73591 6.80092L6.25383 9.31883C6.39278 9.45779 6.5753 9.52724 6.75786 9.52724C6.94043 9.52724 7.12294 9.45779 7.2619 9.31883L8.52811 8.05263C8.66279 7.91799 8.73693 7.73901 8.73693 7.54854C8.73693 7.35806 8.66279 7.17908 8.52816 7.04445L6.01025 4.52659L6.55721 3.97962C6.72627 3.81061 6.77806 3.55796 6.68915 3.33605C6.60024 3.11409 6.38835 2.96707 6.14931 2.96148L3.05522 2.8895C2.80162 2.88401 2.55218 2.98331 2.37246 3.16308C2.19269 3.34286 2.09302 3.59171 2.09888 3.84584L2.17086 6.94003ZM2.93177 3.72244C2.94801 3.7062 2.98334 3.67788 3.03681 3.68036L5.67644 3.74179L5.34331 4.07486C5.09425 4.32398 5.09425 4.72919 5.34331 4.97831L7.91354 7.54854L6.75786 8.70416L4.18764 6.13388C4.06313 6.00938 3.89955 5.9471 3.73597 5.9471C3.57238 5.9471 3.4088 6.00938 3.2843 6.13388L2.95117 6.46701L2.88968 3.82743C2.88842 3.77391 2.91552 3.73868 2.93177 3.72244Z'
      fill='#FF5B00'
    />
    <path
      d='M19.7393 9.31888C19.8783 9.45778 20.0608 9.52723 20.2433 9.52723C20.4259 9.52723 20.6084 9.45773 20.7474 9.31883L23.2653 6.80091L23.8122 7.34788C23.9813 7.51689 24.234 7.56857 24.4558 7.47982C24.6777 7.39085 24.8248 7.17902 24.8304 6.94003L24.9023 3.84588C24.9083 3.59176 24.8086 3.34285 24.6288 3.16313C24.449 2.98341 24.2025 2.88517 23.946 2.88954L20.8519 2.96153C20.6129 2.96706 20.4009 3.11414 20.3121 3.3361C20.2232 3.55806 20.275 3.81071 20.444 3.97967L20.991 4.52658L18.4731 7.04449C18.1952 7.32241 18.1952 7.7746 18.4731 8.05262L19.7393 9.31888ZM21.658 4.9783C21.907 4.72919 21.907 4.32398 21.658 4.07486L21.3248 3.74179L23.9645 3.68035C24.0172 3.67808 24.0532 3.70619 24.0695 3.72243C24.0857 3.73873 24.1128 3.77395 24.1115 3.82743L24.0501 6.46705L23.717 6.13393C23.4679 5.88486 23.0628 5.88497 22.8136 6.13393L20.2434 8.70421L19.0877 7.54858L21.658 4.9783Z'
      fill='#FF5B00'
    />
    <path
      d='M26.6045 18.4736H25.0226V16.9995C25.0226 16.781 24.8456 16.604 24.6271 16.604C24.4086 16.604 24.2316 16.781 24.2316 16.9995V18.4736H18.0157V13.7113C18.0157 13.5366 17.901 13.3825 17.7336 13.3324L16.0383 12.825V11.5291H18.9731V13.5023C18.9731 13.7208 19.1501 13.8978 19.3686 13.8978H20.9013C21.1197 13.8978 21.2968 13.7208 21.2968 13.5023V11.5291H24.2316V15.4701C24.2316 15.6886 24.4086 15.8656 24.6271 15.8656C24.8455 15.8656 25.0226 15.6886 25.0226 15.4701V11.1336C25.0226 10.9151 24.8455 10.7381 24.6271 10.7381H15.6428C15.4243 10.7381 15.2473 10.9151 15.2473 11.1336V12.5882L9.12125 10.7547C9.04732 10.7326 8.96843 10.7326 8.89444 10.7547L0.282129 13.3324C0.114697 13.3825 0 13.5366 0 13.7114V26.6046C0 26.823 0.177082 27.0001 0.395508 27.0001H26.6045C26.823 27.0001 27 26.823 27 26.6046V18.8691C27 18.6507 26.823 18.4736 26.6045 18.4736ZM20.5058 11.5292V13.1069H19.7642V11.5292H20.5058ZM22.4832 19.2646V20.8423H21.7415V19.2646H22.4832ZM9.00787 20.4884C9.2263 20.4884 9.40338 20.3114 9.40338 20.0929V19.513H10.8721V22.4655H7.14372V19.513H8.61237V20.0929C8.61237 20.3113 8.78945 20.4884 9.00787 20.4884ZM6.74821 24.2319C6.96663 24.2319 7.14372 24.0548 7.14372 23.8364V23.2566H8.61237V26.209H4.88405V23.2566H6.3527V23.8364C6.3527 24.0548 6.52973 24.2319 6.74821 24.2319ZM9.40338 26.209V23.2566H10.8721V23.8364C10.8721 24.0548 11.0492 24.2319 11.2676 24.2319C11.486 24.2319 11.6631 24.0548 11.6631 23.8364V23.2566H13.1318V26.209H9.40338ZM13.9227 26.209V22.861C13.9227 22.6426 13.7457 22.4655 13.5272 22.4655H11.6631V19.1175C11.6631 18.8991 11.486 18.722 11.2675 18.722H6.74815C6.52973 18.722 6.35265 18.8991 6.35265 19.1175V22.4655H4.48849C4.27006 22.4655 4.09298 22.6426 4.09298 22.861V26.209H3.0462V19.8822C3.0462 19.6637 2.86912 19.4867 2.65069 19.4867C2.43227 19.4867 2.25519 19.6637 2.25519 19.8822V26.209H0.791068V14.0058L9.00787 11.5466L17.2247 14.0058V26.209H15.7605V16.2497C15.7605 15.8229 15.4132 15.4756 14.9863 15.4756H3.02938C2.60249 15.4756 2.25519 15.8229 2.25519 16.2497V18.3529C2.25519 18.5714 2.43227 18.7484 2.65069 18.7484C2.86912 18.7484 3.0462 18.5714 3.0462 18.3529V16.2667H14.9695V26.2091H13.9227V26.209ZM26.209 26.209H18.0157V19.2646H20.9505V21.2378C20.9505 21.4563 21.1275 21.6333 21.346 21.6333H22.8786C23.0971 21.6333 23.2742 21.4563 23.2742 21.2378V19.2646H26.209V26.209Z'
      fill='black'
    />
    <path
      d='M11.1191 3.53648H11.8926V4.60214C11.8926 4.82062 12.0696 4.99765 12.2881 4.99765C12.5065 4.99765 12.6836 4.82062 12.6836 4.60214V3.38429C12.6836 3.03202 12.397 2.74552 12.0448 2.74552H11.5737L13.3967 0.835578C13.4337 0.796818 13.4778 0.791017 13.5007 0.791017C13.5237 0.791017 13.5678 0.796818 13.6047 0.835578L15.4278 2.74552H14.9567C14.6044 2.74552 14.3179 3.03207 14.3179 3.38429V7.01917H12.6835V6.13144C12.6835 5.91296 12.5064 5.73593 12.288 5.73593C12.0696 5.73593 11.8925 5.91296 11.8925 6.13144V7.09738C11.8925 7.49041 12.2123 7.81019 12.6054 7.81019H14.3961C14.7892 7.81019 15.1089 7.49046 15.1089 7.09738V3.53648H15.8825C16.1215 3.53648 16.3368 3.39447 16.4308 3.17467C16.5249 2.95487 16.479 2.70111 16.3139 2.52814L14.1769 0.289354C14.0014 0.105469 13.7549 0 13.5007 0C13.2465 5.27345e-05 13 0.105522 12.8245 0.289354L10.6876 2.52814C10.5225 2.70106 10.4766 2.95487 10.5707 3.17467C10.6647 3.39447 10.88 3.53648 11.1191 3.53648Z'
      fill='#FF5B00'
    />
  </svg>
)

export default DistributionSupportIcon
