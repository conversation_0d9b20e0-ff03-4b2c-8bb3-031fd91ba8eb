import * as React from "react"

const PersonalStorageIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width='27'
    height='24'
    viewBox='0 0 27 24'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <path
      d='M7.02047 18.36C7.3187 18.36 7.56047 18.1183 7.56047 17.82C7.56047 17.5218 7.3187 17.28 7.02047 17.28C6.72223 17.28 6.48047 17.5218 6.48047 17.82C6.48047 18.1183 6.72223 18.36 7.02047 18.36Z'
      fill='#FF5B00'
    />
    <path
      d='M4.86031 18.36C5.15855 18.36 5.40031 18.1183 5.40031 17.82C5.40031 17.5218 5.15855 17.28 4.86031 17.28C4.56208 17.28 4.32031 17.5218 4.32031 17.82C4.32031 18.1183 4.56208 18.36 4.86031 18.36Z'
      fill='#FF5B00'
    />
    <path
      d='M2.70016 18.36C2.99839 18.36 3.24016 18.1183 3.24016 17.82C3.24016 17.5218 2.99839 17.28 2.70016 17.28C2.40192 17.28 2.16016 17.5218 2.16016 17.82C2.16016 18.1183 2.40192 18.36 2.70016 18.36Z'
      fill='#FF5B00'
    />
    <path
      d='M22.9503 16.4253V15.39C22.9503 14.05 21.8603 12.96 20.5203 12.96C19.1803 12.96 18.0903 14.05 18.0903 15.39V16.4253C17.6084 16.7063 17.2803 17.2231 17.2803 17.82V19.44C17.2803 20.3333 18.007 21.06 18.9003 21.06H22.1403C23.0336 21.06 23.7603 20.3333 23.7603 19.44V17.82C23.7603 17.2231 23.4321 16.7063 22.9503 16.4253ZM20.5203 14.04C21.2649 14.04 21.8703 14.6454 21.8703 15.39V16.2H19.1703V15.39C19.1703 14.6454 19.7757 14.04 20.5203 14.04ZM22.6803 19.44C22.6803 19.738 22.4382 19.98 22.1403 19.98H18.9003C18.6023 19.98 18.3603 19.738 18.3603 19.44V17.82C18.3603 17.5221 18.6023 17.28 18.9003 17.28H22.1403C22.4382 17.28 22.6803 17.5221 22.6803 17.82V19.44Z'
      fill='#FF5B00'
    />
    <path
      d='M23.76 11.6767V1.62C23.76 0.726678 23.0333 0 22.14 0H1.62C0.726678 0 0 0.726678 0 1.62V18.9C0 19.7933 0.726678 20.52 1.62 20.52H14.9167C16.0392 22.4536 18.1276 23.76 20.52 23.76C24.0933 23.76 27 20.8533 27 17.28C27 14.8876 25.6936 12.7992 23.76 11.6767ZM22.68 1.62V2.16H15.66V1.08H22.14C22.438 1.08 22.68 1.32205 22.68 1.62ZM9.18 5.67013H14.58V7.02013L14.04 7.42513L13.2841 6.85824C13.0921 6.71427 12.8279 6.71427 12.6359 6.85824L11.88 7.42513L11.1241 6.85824C10.9321 6.71427 10.6679 6.71427 10.4759 6.85824L9.72 7.42513L9.18 7.02013V5.67013ZM9.18 4.59013V1.08H14.58V4.59013H9.18ZM1.62 1.08H8.1V2.16H1.08V1.62C1.08 1.32205 1.32205 1.08 1.62 1.08ZM1.62 19.44C1.32205 19.44 1.08 19.198 1.08 18.9V3.24H8.1V7.29013C8.1 7.45993 8.17989 7.62025 8.31595 7.72202L9.39595 8.53202C9.5879 8.67599 9.8521 8.67599 10.0441 8.53202L10.8 7.96513L11.5559 8.53202C11.7479 8.67599 12.0121 8.67599 12.2041 8.53202L12.96 7.96513L13.7159 8.53202C13.9079 8.67599 14.1721 8.67599 14.3641 8.53202L15.4441 7.72202C15.5801 7.62025 15.66 7.45994 15.66 7.29013V3.24H22.68V11.178C22.0032 10.9377 21.2782 10.8 20.52 10.8C16.9467 10.8 14.04 13.7067 14.04 17.28C14.04 18.0382 14.1777 18.7632 14.418 19.44H1.62ZM20.52 22.68C17.5426 22.68 15.12 20.2574 15.12 17.28C15.12 14.3026 17.5426 11.88 20.52 11.88C23.4974 11.88 25.92 14.3026 25.92 17.28C25.92 20.2574 23.4974 22.68 20.52 22.68Z'
      fill='black'
    />
  </svg>
)

export default PersonalStorageIcon
