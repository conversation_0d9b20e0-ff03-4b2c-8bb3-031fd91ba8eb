import React from "react"

interface RecordDigitalizationIconProps extends React.SVGProps<SVGSVGElement> {}

const RecordDigitalizationIcon: React.FC<RecordDigitalizationIconProps> = (
  props,
) => {
  return (
    <svg
      width='27'
      height='27'
      viewBox='0 0 27 27'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M16.6752 18.9366H6.13827C5.9362 18.9366 5.77246 18.7728 5.77246 18.5708C5.77246 18.3687 5.9362 18.205 6.13827 18.205H16.6752C16.8773 18.205 17.041 18.3687 17.041 18.5708C17.041 18.7728 16.8756 18.9366 16.6752 18.9366Z'
        fill='black'
      />
      <path
        d='M2.41073 12.5768C2.20866 12.5768 2.04492 12.4131 2.04492 12.211V5.38957C2.04492 4.67364 2.62847 4.09009 3.34441 4.09009H21.8142C22.5301 4.09009 23.1136 4.67364 23.1136 5.38957V9.5528C23.1136 9.75486 22.9499 9.9186 22.7478 9.9186C22.5458 9.9186 22.382 9.75486 22.382 9.5528V5.38957C22.382 5.07602 22.1277 4.8217 21.8142 4.8217H3.34441C3.03086 4.8217 2.77653 5.07602 2.77653 5.38957V12.211C2.77653 12.4131 2.61279 12.5768 2.41073 12.5768Z'
        fill='black'
      />
      <path
        d='M16.6752 15.8202H6.13827C5.9362 15.8202 5.77246 15.6565 5.77246 15.4544C5.77246 15.2524 5.9362 15.0886 6.13827 15.0886H16.6752C16.8773 15.0886 17.041 15.2524 17.041 15.4544C17.041 15.6565 16.8756 15.8202 16.6752 15.8202Z'
        fill='black'
      />
      <path
        d='M12.5781 17.4629C12.8273 17.4629 13.0293 17.2609 13.0293 17.0117C13.0293 16.7625 12.8273 16.5605 12.5781 16.5605C12.3289 16.5605 12.127 16.7625 12.127 17.0117C12.127 17.2609 12.3289 17.4629 12.5781 17.4629Z'
        fill='#FF5B00'
      />
      <path
        d='M11.0807 20.9328C10.8786 20.9328 10.7148 20.7691 10.7148 20.567V18.5708C10.7148 18.3687 10.8786 18.205 11.0807 18.205C11.2827 18.205 11.4465 18.3687 11.4465 18.5708V20.567C11.4465 20.7691 11.2827 20.9328 11.0807 20.9328Z'
        fill='black'
      />
      <path
        d='M14.0787 20.9328C13.8766 20.9328 13.7129 20.7691 13.7129 20.567V18.5708C13.7129 18.3687 13.8766 18.205 14.0787 18.205C14.2808 18.205 14.4445 18.3687 14.4445 18.5708V20.567C14.4445 20.7691 14.2808 20.9328 14.0787 20.9328Z'
        fill='black'
      />
      <path
        d='M17.0236 22.9082H7.31405C7.11198 22.9082 6.94824 22.7444 6.94824 22.5424V22.3089C6.94824 21.2063 7.84534 20.3092 8.94798 20.3092H16.2119C16.4052 20.3092 16.5968 20.3371 16.7797 20.3911C16.9731 20.4486 17.0846 20.6524 17.0271 20.8457C16.9696 21.0391 16.7658 21.1506 16.5724 21.0931C16.4557 21.0582 16.3355 21.0408 16.2119 21.0408H8.94798C8.29302 21.0408 7.75302 21.539 7.68682 22.1766H17.0236C17.2257 22.1766 17.3894 22.3403 17.3894 22.5424C17.3894 22.7444 17.2257 22.9082 17.0236 22.9082Z'
        fill='black'
      />
      <path
        d='M5.86125 22.9082H0.816602C0.461248 22.9082 0.173828 22.619 0.173828 22.2654V12.4879C0.173828 12.1326 0.462989 11.8452 0.816602 11.8452H5.85951C6.21486 11.8452 6.50228 12.1343 6.50228 12.4879V22.2637C6.50402 22.6208 6.21486 22.9082 5.86125 22.9082ZM0.905441 22.1766H5.77241V12.5768H0.905441V22.1766Z'
        fill='#FF5B00'
      />
      <path
        d='M6.13822 14.0835H0.539635C0.33757 14.0835 0.173828 13.9198 0.173828 13.7177C0.173828 13.5157 0.33757 13.3519 0.539635 13.3519H6.13822C6.34028 13.3519 6.50402 13.5157 6.50402 13.7177C6.50402 13.9198 6.34028 14.0835 6.13822 14.0835Z'
        fill='#FF5B00'
      />
      <path
        d='M6.13822 21.4014H0.539635C0.33757 21.4014 0.173828 21.2377 0.173828 21.0356C0.173828 20.8335 0.33757 20.6698 0.539635 20.6698H6.13822C6.34028 20.6698 6.50402 20.8335 6.50402 21.0356C6.50402 21.2377 6.34028 21.4014 6.13822 21.4014Z'
        fill='#FF5B00'
      />
      <path
        d='M26.1114 22.9082H17.0238C16.6301 22.9082 16.3096 22.5877 16.3096 22.194V9.90291C16.3096 9.50924 16.6301 9.18872 17.0238 9.18872H26.1114C26.5051 9.18872 26.8256 9.50924 26.8256 9.90291V22.194C26.8256 22.5877 26.5051 22.9082 26.1114 22.9082ZM17.0412 22.1766H26.094V9.91859H17.0394V22.1766H17.0412Z'
        fill='black'
      />
      <path
        d='M25.2406 20.9729H17.8931C17.6911 20.9729 17.5273 20.8091 17.5273 20.6071V10.7721C17.5273 10.5701 17.6911 10.4063 17.8931 10.4063H25.2406C25.4427 10.4063 25.6064 10.5701 25.6064 10.7721V20.6071C25.6064 20.8091 25.4427 20.9729 25.2406 20.9729ZM18.2607 20.2413H24.8748V11.1379H18.259V20.2413H18.2607Z'
        fill='black'
      />
      <path
        d='M21.5673 21.9222C21.7588 21.9222 21.914 21.767 21.914 21.5756C21.914 21.3841 21.7588 21.2289 21.5673 21.2289C21.3759 21.2289 21.2207 21.3841 21.2207 21.5756C21.2207 21.767 21.3759 21.9222 21.5673 21.9222Z'
        fill='#FF5B00'
      />
    </svg>
  )
}

export default RecordDigitalizationIcon
