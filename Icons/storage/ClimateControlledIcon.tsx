import * as React from "react"

const ClimateControlledIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width='27'
    height='27'
    viewBox='0 0 27 27'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <g clipPath='url(#clip0_2045_88)'>
      <path
        d='M26.356 8.67761L13.6998 0.533364C13.578 0.455106 13.4219 0.455106 13.3002 0.533364L0.643992 8.67761C0.538418 8.74558 0.474609 8.86249 0.474609 8.988V10.9634C0.474609 11.1672 0.639879 11.3325 0.84375 11.3325H1.66356V26.1563C1.66356 26.3601 1.82883 26.5254 2.0327 26.5254H24.9673C25.1712 26.5254 25.3364 26.3601 25.3364 26.1563V11.3325H26.1562C26.3601 11.3325 26.5254 11.1672 26.5254 10.9634V8.988C26.5254 8.86249 26.4616 8.74558 26.356 8.67761ZM22.5109 25.7871H4.48912V13.4882H22.5109V25.7871ZM24.5982 25.7871H23.2492V13.1191C23.2492 12.9152 23.0839 12.7499 22.88 12.7499H4.11998C3.91611 12.7499 3.75084 12.9152 3.75084 13.1191V25.7871H2.40184V11.3325H24.5982V25.7871ZM25.7871 10.5942H1.21289V9.18939L13.5 1.28272L25.7871 9.18945V10.5942ZM6.72653 7.28695V8.73129C6.72653 8.93516 6.8918 9.10043 7.09567 9.10043H19.9043C20.1082 9.10043 20.2735 8.93516 20.2735 8.73129V7.28695C20.2735 7.08308 20.1082 6.91781 19.9043 6.91781H7.09567C6.8918 6.91781 6.72653 7.08303 6.72653 7.28695ZM7.46481 7.65609H19.5352V8.36215H7.46481V7.65609ZM19.1881 23.3568C19.946 23.3568 20.5626 22.7402 20.5626 21.9824C20.5626 21.6803 20.4648 21.3913 20.2847 21.1536V17.0151C20.2847 16.4104 19.7927 15.9185 19.1881 15.9185C18.5835 15.9185 18.0916 16.4104 18.0916 17.0151V21.1536C17.9114 21.3913 17.8137 21.6803 17.8137 21.9824C17.8137 22.7402 18.4303 23.3568 19.1881 23.3568ZM19.1881 16.6567C19.3857 16.6567 19.5464 16.8175 19.5464 17.0151V21.2885C19.5464 21.3834 19.583 21.4746 19.6485 21.5433C19.7618 21.6621 19.8242 21.8181 19.8242 21.9823C19.8242 22.3331 19.5389 22.6185 19.1881 22.6185C18.8373 22.6185 18.552 22.3331 18.552 21.9823C18.552 21.8181 18.6144 21.6621 18.7277 21.5433C18.7932 21.4747 18.8298 21.3834 18.8298 21.2885V20.5024H18.9579C19.1618 20.5024 19.327 20.3372 19.327 20.1333C19.327 19.9294 19.1618 19.7642 18.9579 19.7642H18.8298V19.4795H18.9579C19.1618 19.4795 19.327 19.3142 19.327 19.1103C19.327 18.9064 19.1618 18.7412 18.9579 18.7412H18.8298V18.4565H18.9579C19.1618 18.4565 19.327 18.2912 19.327 18.0873C19.327 17.8834 19.1618 17.7182 18.9579 17.7182H18.8298V17.0151C18.8299 16.8175 18.9906 16.6567 19.1881 16.6567Z'
        fill='black'
      />
      <g clipPath='url(#clip1_2045_88)'>
        <path
          d='M15.2846 21.3305L14.3705 20.8027L15.1627 20.3356C15.3088 20.2494 15.3574 20.0612 15.2712 19.915C15.1851 19.769 14.9968 19.7204 14.8507 19.8065L13.7594 20.4499L12.1141 19.5L13.7592 18.5502L14.8507 19.1936C14.8997 19.2224 14.9534 19.2361 15.0064 19.2361C15.1115 19.2361 15.2139 19.182 15.2712 19.0849C15.3574 18.9388 15.3087 18.7505 15.1626 18.6643L14.3703 18.1974L15.2846 17.6695C15.4315 17.5847 15.4818 17.3968 15.397 17.2499C15.3122 17.103 15.1244 17.0527 14.9775 17.1375L14.0634 17.6653L14.0551 16.7456C14.0536 16.576 13.9144 16.4389 13.7452 16.4412C13.5756 16.4427 13.4393 16.5815 13.4409 16.7511L13.4523 18.0181L11.807 18.968V17.0684L12.9101 16.445C13.0578 16.3615 13.1098 16.1742 13.0264 16.0264C12.9429 15.8788 12.7555 15.8267 12.6079 15.9102L11.807 16.3628V15.3071C11.807 15.1375 11.6695 15 11.4999 15C11.3303 15 11.1928 15.1375 11.1928 15.3071V16.3628L10.392 15.9102C10.2442 15.8267 10.057 15.8788 9.97347 16.0265C9.89002 16.1742 9.94206 16.3615 10.0897 16.445L11.1928 17.0684V18.968L9.54751 18.0181L9.55894 16.7511C9.56049 16.5815 9.42421 16.4427 9.2546 16.4412C9.25365 16.4412 9.25275 16.4412 9.25177 16.4412C9.08347 16.4412 8.94624 16.5769 8.94473 16.7456L8.93642 17.6653L8.02229 17.1375C7.87541 17.0527 7.68757 17.1031 7.60276 17.2499C7.51795 17.3968 7.56828 17.5847 7.71517 17.6695L8.62946 18.1974L7.83704 18.6644C7.69093 18.7505 7.64228 18.9388 7.7284 19.085C7.78568 19.1821 7.8881 19.2362 7.9933 19.2362C8.04629 19.2362 8.09997 19.2225 8.14895 19.1936L9.24063 18.5503L10.8857 19.5001L9.24035 20.45L8.14895 19.8064C8.00292 19.7203 7.81455 19.7689 7.7284 19.915C7.64224 20.0611 7.69084 20.2494 7.83695 20.3355L8.62925 20.8027L7.71513 21.3304C7.56824 21.4152 7.51791 21.6031 7.60272 21.75C7.6596 21.8485 7.76283 21.9036 7.86898 21.9036C7.92106 21.9036 7.97389 21.8903 8.02225 21.8624L8.93638 21.3346L8.94469 22.2544C8.9462 22.4231 9.08343 22.5588 9.25173 22.5588C9.25267 22.5588 9.25361 22.5588 9.25456 22.5587C9.42417 22.5572 9.56045 22.4185 9.5589 22.2489L9.54747 20.9818L11.1928 20.0319V21.9316L10.0897 22.555C9.94206 22.6385 9.89002 22.8258 9.97347 22.9735C10.0299 23.0734 10.134 23.1296 10.2411 23.1296C10.2923 23.1296 10.3442 23.1168 10.392 23.0898L11.1928 22.6372V23.6929C11.1928 23.8625 11.3303 24 11.4999 24C11.6695 24 11.807 23.8625 11.807 23.6929V22.6372L12.6079 23.0898C12.6556 23.1168 12.7075 23.1296 12.7587 23.1296C12.8658 23.1296 12.9699 23.0734 13.0264 22.9735C13.1098 22.8258 13.0578 22.6385 12.9101 22.555L11.807 21.9316V20.0319L13.4523 20.9818L13.4406 22.2488C13.439 22.4184 13.5752 22.5572 13.7448 22.5588C13.7458 22.5588 13.7468 22.5588 13.7477 22.5588C13.916 22.5588 14.0532 22.4231 14.0548 22.2545L14.0633 21.3346L14.9775 21.8624C15.0259 21.8903 15.0787 21.9036 15.1308 21.9036C15.237 21.9036 15.3402 21.8485 15.3971 21.75C15.4819 21.6031 15.4316 21.4153 15.2846 21.3305Z'
          fill='#FF5B00'
        />
      </g>
    </g>
    <defs>
      <clipPath id='clip0_2045_88'>
        <rect width='27' height='27' fill='white' />
      </clipPath>
      <clipPath id='clip1_2045_88'>
        <rect width='9' height='9' fill='white' transform='translate(7 15)' />
      </clipPath>
    </defs>
  </svg>
)

export default ClimateControlledIcon
