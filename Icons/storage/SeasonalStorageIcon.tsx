import * as React from "react"

const SeasonalStorageIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width='27'
    height='27'
    viewBox='0 0 27 27'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <g clipPath='url(#clip0_2045_162)'>
      <path
        d='M23.3301 14.0907H3.71289'
        stroke='#010101'
        strokeWidth='0.817594'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M23.3301 15.736H3.71289'
        stroke='#010101'
        strokeWidth='0.817594'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M23.3301 5.9906H3.71289'
        stroke='#010101'
        strokeWidth='0.817594'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M23.3301 7.63599H3.71289'
        stroke='#010101'
        strokeWidth='0.817594'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M23.3301 22.5282H3.71289'
        stroke='#010101'
        strokeWidth='0.817594'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M23.3301 24.1735H3.71289'
        stroke='#010101'
        strokeWidth='0.817594'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M1.81406 4.6828C1.98281 4.80936 2.06719 5.0203 2.06719 5.27343V24.9328C2.06719 25.3547 1.6875 25.7344 1.22344 25.7344C0.759375 25.7344 0.421875 25.3547 0.421875 24.9328V5.27343C0.421875 4.80936 0.801563 4.47186 1.22344 4.47186C1.47656 4.47186 1.6875 4.55624 1.81406 4.6828Z'
        stroke='#010101'
        strokeWidth='0.817594'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M26.5777 5.27336V24.9327C26.5777 25.3968 26.198 25.7343 25.7762 25.7343C25.3543 25.7343 24.9746 25.3546 24.9746 24.9327V5.27336C24.9746 4.8093 25.3543 4.4718 25.7762 4.4718C25.9871 4.4718 26.198 4.55618 26.3668 4.72493C26.4934 4.85149 26.5777 5.06243 26.5777 5.27336Z'
        stroke='#010101'
        strokeWidth='0.817594'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M8.7332 1.77187V5.48438C8.7332 5.7375 8.52227 5.99062 8.22695 5.99062H3.58633C3.3332 5.99062 3.08008 5.77969 3.08008 5.48438V1.77187C3.12227 1.47656 3.3332 1.26562 3.62852 1.26562H8.26914C8.52227 1.26562 8.7332 1.47656 8.7332 1.77187Z'
        stroke='#010101'
        strokeWidth='0.817594'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M5.14648 1.26562V3.45937L5.94805 2.95312L6.70742 3.45937V1.26562H5.14648Z'
        stroke='#FF5B00'
        strokeWidth='0.817594'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M16.242 1.77187V5.48438C16.242 5.7375 16.0311 5.99062 15.7357 5.99062H11.0951C10.842 5.99062 10.5889 5.77969 10.5889 5.48438V1.77187C10.5889 1.51875 10.7998 1.26562 11.0951 1.26562H15.7357C16.0311 1.26562 16.242 1.47656 16.242 1.77187Z'
        stroke='#010101'
        strokeWidth='0.817594'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M12.6562 1.26562V3.45937L13.4578 2.95312L14.2172 3.45937V1.26562H12.6562Z'
        stroke='#FF5B00'
        strokeWidth='0.817594'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M23.7518 1.77187V5.48438C23.7518 5.7375 23.5408 5.99062 23.2455 5.99062H18.6049C18.3518 5.99062 18.0986 5.77969 18.0986 5.48438V1.77187C18.1408 1.47656 18.3518 1.26562 18.6471 1.26562H23.2877C23.5408 1.26562 23.7518 1.47656 23.7518 1.77187Z'
        stroke='#010101'
        strokeWidth='0.817594'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M20.166 1.26562V3.45937L20.9676 2.95312L21.727 3.45937V1.26562H20.166Z'
        stroke='#FF5B00'
        strokeWidth='0.817594'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M12.4871 9.74545V13.5001C12.4871 13.7533 12.2762 14.0064 11.9809 14.0064H7.34023C7.08711 14.0064 6.83398 13.7954 6.83398 13.5001V9.74545C6.83398 9.49232 7.04492 9.2392 7.34023 9.2392H11.9809C12.2762 9.2392 12.4871 9.45013 12.4871 9.74545Z'
        stroke='#010101'
        strokeWidth='0.817594'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M8.90137 9.2392V11.4329L9.70293 10.9267L10.4623 11.4329V9.2392H8.90137Z'
        stroke='#FF5B00'
        strokeWidth='0.817594'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M19.9969 9.74539V13.5001C19.9969 13.7532 19.7859 14.0063 19.4906 14.0063H14.85C14.5969 14.0063 14.3438 13.7954 14.3438 13.5001V9.74539C14.3438 9.49226 14.5547 9.23914 14.85 9.23914H19.4906C19.7859 9.23914 19.9969 9.45007 19.9969 9.74539Z'
        stroke='#010101'
        strokeWidth='0.817594'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M16.4111 9.2392V11.4329L17.2127 10.9267L17.9721 11.4329V9.2392H16.4111Z'
        stroke='#FF5B00'
        strokeWidth='0.817594'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M8.7332 18.3516V22.1063C8.7332 22.3595 8.52227 22.6126 8.22695 22.6126H3.58633C3.3332 22.6126 3.08008 22.4016 3.08008 22.1063V18.3516C3.08008 18.0985 3.29102 17.8454 3.58633 17.8454H8.22695C8.52227 17.8454 8.7332 18.0563 8.7332 18.3516Z'
        stroke='#010101'
        strokeWidth='0.817594'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M5.14648 17.8453V20.0812L5.94805 19.5328L6.70742 20.0812V17.8453H5.14648Z'
        stroke='#FF5B00'
        strokeWidth='0.817594'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M16.242 18.3513V22.106C16.242 22.3592 16.0311 22.6123 15.7357 22.6123H11.0951C10.842 22.6123 10.5889 22.4013 10.5889 22.106V18.3513C10.5889 18.0982 10.7998 17.8451 11.0951 17.8451H15.7357C16.0311 17.8451 16.242 18.056 16.242 18.3513Z'
        stroke='#010101'
        strokeWidth='0.817594'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M12.6562 17.8451V20.081L13.4578 19.5326L14.2172 20.081V17.8451H12.6562Z'
        stroke='#FF5B00'
        strokeWidth='0.817594'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M23.7518 18.3513V22.106C23.7518 22.3592 23.5408 22.6123 23.2455 22.6123H18.6049C18.3518 22.6123 18.0986 22.4013 18.0986 22.106V18.3513C18.0986 18.0982 18.3096 17.8451 18.6049 17.8451H23.2455C23.5408 17.8451 23.7518 18.056 23.7518 18.3513Z'
        stroke='#010101'
        strokeWidth='0.817594'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M20.166 17.8451V20.081L20.9676 19.5326L21.727 20.081V17.8451H20.166Z'
        stroke='#FF5B00'
        strokeWidth='0.817594'
        strokeMiterlimit='10'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </g>
    <defs>
      <clipPath id='clip0_2045_162'>
        <rect width='27' height='27' fill='white' />
      </clipPath>
    </defs>
  </svg>
)

export default SeasonalStorageIcon
