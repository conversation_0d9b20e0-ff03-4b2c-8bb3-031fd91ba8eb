import React from "react"

const IndiaIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='66'
    height='67'
    viewBox='0 0 66 67'
    fill='none'
    {...props}
  >
    <path
      d='M19.3453 11.8519C20.5648 10.8801 20.6185 10.0464 22.654 9.75936C24.7554 9.61318 26.8466 11.1966 27.3184 13.3006C28.043 16.5324 29.4894 15.4029 30.0566 17.0905C30.4259 18.1893 29.8896 19.3767 29.4408 20.3563C31.2142 21.3243 33.8952 22.8804 35.8213 23.2632C36.3393 23.3663 36.8262 23.238 37.3447 23.2265C37.8722 23.2149 44.6729 22.592 43.0383 24.5808C42.7127 24.9769 41.863 24.8266 41.4145 24.8269L39.1086 24.8299C38.037 24.8302 36.8981 24.9097 35.8344 24.7777C34.5976 24.6244 28.9961 22.1616 27.9377 21.1302C27.2904 20.4993 28.8113 19.0112 28.7603 17.5889C28.2145 16.9258 27.3608 16.7455 26.7683 15.9552C25.5715 14.3592 26.3981 11.9328 23.4205 11.2638C21.8334 10.9072 21.2262 12.3082 20.0918 13.0473C19.2882 13.5707 19.0281 13.1739 18.2468 12.9985C17.9561 13.2508 17.8077 13.5218 17.6867 13.8846C18.0395 14.2658 18.5159 14.5486 18.9375 14.8438L20.4376 15.9058C22.1559 17.1297 21.2712 17.3961 20.0557 18.8284L17.6411 21.6756C17.2211 22.1809 16.7795 22.6799 16.4056 23.2221L26.8966 23.2198C27.3599 23.2196 27.8426 23.1936 28.3038 23.2348C28.8792 23.2861 29.1514 23.9788 28.7437 24.4262C28.3692 24.8371 27.5375 24.6557 27.0533 24.6551L15.9948 24.6586C16.2547 25.2069 18.2247 27.812 16.8564 28.0342C16.2432 28.1337 15.1535 28.0622 14.5007 28.0622L9.41666 28.0607C9.88585 28.9068 10.5692 29.6756 11.123 30.469L12.5998 32.6162C14.6462 35.5954 15.3548 35.1631 17.7848 32.9714C19.6359 31.3018 19.3612 34.2816 19.5037 35.3905C20.3596 42.0538 23.9126 50.9827 28.2215 56.2736L28.6156 55.938C29.5869 55.0692 30.5876 54.2382 31.5521 53.3602C31.1137 51.6169 31.3517 50.0692 32.0056 48.4264C31.8995 47.6887 31.6789 47.027 31.6936 46.2659C31.7519 43.2536 32.9816 42.476 34.9329 40.7211C37.6703 38.2593 40.8858 35.8205 43.4672 33.2924C43.1301 32.5837 42.8414 31.8466 42.5205 31.1296C42.2677 30.5649 40.7626 28.1273 42.1415 27.9135C42.547 27.8506 42.7964 28.0599 42.9674 28.4055C43.2251 28.9266 45.0021 33.0481 45.0669 33.4951C45.1714 34.217 43.2182 35.4475 42.7197 35.8708C40.9606 37.3651 39.243 38.9107 37.4986 40.4228L35.6277 42.0629C33.9105 43.5553 32.7408 44.5149 33.2343 47.1844C33.475 48.4862 33.5611 48.3389 33.0791 49.6227C32.647 50.7735 32.6946 51.6374 32.8878 52.828C33.1778 54.6146 31.9112 55.0013 30.5943 56.1551C29.6566 56.9767 28.5702 58.4468 27.2636 57.3701C26.6588 56.8715 25.3946 54.8482 24.9363 54.0951C21.3705 48.2348 18.7618 41.7283 17.9713 34.8344C15.4649 37.1106 13.4632 36.5049 11.5914 33.7539L8.99703 29.9999C8.40195 29.1452 7.43825 27.9772 8.42333 26.9884C9.06331 26.3459 10.4205 26.5825 11.2226 26.5826L15.2802 26.5859C14.9107 25.6995 14.2875 24.8492 14.0961 23.9033L14.1173 23.8325C14.3441 23.0648 18.8985 17.9285 19.5888 17.1231C18.8074 16.4529 16.8099 15.4042 16.4132 14.5518C15.9499 13.5563 16.7705 11.9484 17.7081 11.5526C18.288 11.3079 18.8223 11.6155 19.3453 11.8519ZM48.7577 23.2221C50.7934 20.9184 51.2842 18.8639 54.9644 18.567C56.4835 18.4425 57.9615 19.4529 57.9988 21.1041C58.0433 23.053 56.7949 22.9005 56.3553 23.8607C55.2941 26.1793 56.2919 26.7698 53.8086 29.4357C53.7008 30.4857 53.9968 33.7761 53.7038 34.4455C53.3724 35.2025 52.3349 35.5519 51.6135 35.2057C50.7403 34.7868 50.5515 33.297 50.2687 32.441C49.8054 32.6115 49.4126 32.6845 48.9213 32.5819C48.047 32.3893 47.3457 31.425 47.4691 30.5087C47.59 29.6105 48.6207 28.7897 49.2957 28.2708C46.8475 28.2596 44.0856 28.3473 45.3295 24.8292C43.8134 22.6972 47.7685 23.2182 48.7577 23.2221ZM54.7754 20.094C51.2361 20.6506 50.4349 24.5683 49.1093 24.7835C48.311 24.9131 47.4326 24.6963 46.7037 24.8292C46.618 25.654 46.013 26.7577 47.269 26.81C48.1919 26.8486 51.5485 26.3516 51.4009 27.6309C51.228 29.1305 47.792 30.4066 49.2957 31.2463C49.9395 31.1944 50.1731 29.7685 51.024 30.5493C51.3714 30.8682 51.9629 33.3221 52.3115 34.0474L52.3097 30.9674C52.3092 30.3756 52.2107 29.5317 52.3669 28.9742C52.5606 28.2827 53.3957 27.8177 53.7829 27.223C54.7609 25.7211 54.3418 23.6332 55.7064 22.3121C56.345 21.6938 57.0523 21.2032 56.1407 20.3894C55.7747 20.0627 55.2371 20.0218 54.7754 20.094Z'
      fill='black'
    />
    <path
      d='M29.3358 27.8171C29.2017 27.2134 29.3972 26.9631 30.0128 26.7504C30.7209 26.7331 30.8638 27.2688 30.8552 27.8171C37.0769 28.5023 38.3247 37.2233 30.8552 38.758C30.8855 39.3805 30.7095 39.6856 30.0128 39.75C29.4008 39.6604 29.2453 39.2774 29.3358 38.758C22.3326 38.0455 22.1111 28.5253 29.3358 27.8171ZM29.6887 29.1767C24.2263 29.7199 24.1845 36.5491 29.3358 37.2723V35.9615C25.9024 35.1333 26.2559 30.5013 30.1753 30.5164C33.3128 30.7 34.1724 34.6463 30.8552 35.9615V37.2723C36.4279 36.1279 34.9759 28.7926 29.6887 29.1767Z'
      fill='#FF5B00'
    />
  </svg>
)

export default IndiaIcon
