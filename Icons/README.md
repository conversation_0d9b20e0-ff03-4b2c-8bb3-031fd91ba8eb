# Icons Directory

This directory contains individual React components for all icons used in the NexMove application. Each icon is implemented as a separate component following the project's coding standards.

## Structure

The icons are organized into the following categories:

### Global Icons

These icons were previously nested under `Icons.global` in the original Icons.tsx file:

- `LogoIcon` - Company logo
- `CalculatorIcon` - Calculator/quote calculator icon
- `ChatOutlineIcon` - Chat/communication icon
- `CalendarIcon` - Calendar/scheduling icon
- `DrinkIcon` - Drink/relaxation icon
- `SpeedoMeterIcon` - Speed/performance icon
- `DeliveryBoxIcon` - Delivery/shipping icon
- `WorldwideIcon` - Global/worldwide icon

### Flat Icons

These icons were at the root level of the original Icons object:

- `GlobeIcon` - Globe/world icon
- `SmilyRatingIcon` - Smiley/rating icon
- `ArcheryIcon` - Target/precision icon
- `TeamIcon` - Team/people icon
- `LightBulbIcon` - Innovation/ideas icon
- `PersonIcon` - Person/user icon
- `WebServiceIcon` - Web services icon
- `PaymentIcon` - Payment/credit card icon
- `ChecklistIcon` - Checklist/tasks icon
- `EmailIcon` - Email/contact icon
- `DollarIcon` - Money/pricing icon
- `ShieldIcon` - Security/protection icon
- `SustainabilityIcon` - Sustainability/eco-friendly icon
- `GreenEnergyIcon` - Green energy/environment icon

### Social Media Icons

- `LinkedInIcon` - LinkedIn social media
- `InstagramIcon` - Instagram social media
- `YouTubeIcon` - YouTube social media
- `FacebookIcon` - Facebook social media
- `TwitterIcon` - Twitter social media

### Additional Icons

- `ArrowRightIcon` - Right arrow navigation
- `CustomerSupportIcon` - Customer support/headset
- `ClockIcon` - Time/clock
- `DeliveryVehicleIcon` - Delivery truck/vehicle
- `ShareSessionIcon` - Share/network icon

### Existing Icons

- `ArrowLeftIcon` - Left arrow navigation (already existed)

## Usage

Import icons from the Icons directory:

```tsx
import { LogoIcon, CalculatorIcon, ArrowRightIcon } from "@/Icons"

// Use in components
<LogoIcon className="w-8 h-8" />
<CalculatorIcon className="w-6 h-6 text-blue-500" />
<GlobalArrowRightIcon className="w-4 h-4" />
```

## Component Structure

Each icon component follows this pattern:

```tsx
import React from "react"

const IconName = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width='100%'
    height='100%'
    viewBox='0 0 24 24'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    {/* SVG paths */}
  </svg>
)

export default IconName
```

## Features

- **TypeScript Support**: All icons are properly typed with `React.SVGProps<SVGSVGElement>`
- **Customizable**: Icons accept all standard SVG props (className, style, etc.)
- **Responsive**: Icons use percentage-based dimensions by default
- **Consistent**: All icons follow the same component structure
- **Tree-shakable**: Individual imports prevent unused icons from being bundled

## Migration from Icons.tsx

The original `config/Icons.tsx` file contained all icons in a single object. This new structure provides:

1. **Better organization**: Icons are separated into individual files
2. **Improved maintainability**: Each icon can be updated independently
3. **Better performance**: Tree-shaking eliminates unused icons
4. **Enhanced developer experience**: Better IDE support and autocomplete
5. **Consistent structure**: All icons follow the same pattern

## Adding New Icons

To add a new icon:

1. Create a new file in the Icons directory: `NewIcon.tsx`
2. Follow the component structure pattern shown above
3. Add the export to `index.tsx`
4. Update this README if needed

## Notes

- All icons are designed to work with Tailwind CSS classes
- Icons use `currentColor` for stroke/fill to inherit text color
- Default viewBox is `0 0 24 24` unless the icon requires a different aspect ratio
- Icons are optimized for both light and dark themes
