import React from "react"

const SpeedoMeterIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width='30'
    height='31'
    viewBox='0 0 30 31'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <path
      d='M28 22C28 18.2334 26.3981 14.8409 23.8384 12.4667M2 22C2 14.8203 7.82029 9 15 9C15.5279 9 16.0485 9.03148 16.56 9.09263'
      stroke='black'
      strokeWidth='1.5'
      strokeLinecap='round'
    />
    <path
      d='M17.3713 21.8619C17.1585 22.4351 16.7738 22.9422 16.261 23.3256C13.9657 25.0415 10.566 23.2492 11.0457 20.576C11.1529 19.9788 11.4415 19.421 11.8787 18.9662L21.3444 9.11811C21.6136 8.83795 22.1094 9.09933 21.9783 9.45236L17.3713 21.8619Z'
      fill='#FF5B00'
    />
  </svg>
)

export default SpeedoMeterIcon
