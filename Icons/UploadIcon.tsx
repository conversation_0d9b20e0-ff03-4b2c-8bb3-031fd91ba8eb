import React from "react"

const UploadIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='40'
    height='40'
    viewBox='0 0 40 40'
    fill='none'
    {...props}
  >
    <path
      fillRule='evenodd'
      clipRule='evenodd'
      d='M17.4997 8.01773V12.5002H19.9997V8.01773L26.6172 14.6352L28.3847 12.8652L19.6347 4.11523H17.8672L9.11719 12.8652L10.8847 14.6352L17.4997 8.01773Z'
      fill='#FF5B00'
    />
    <path
      d='M3.75 22.5L2.5 23.75V36.25L3.75 37.5H33.75L35 36.25V23.75L33.75 22.5H24.875C24.5881 23.9129 23.8216 25.1831 22.7053 26.0955C21.5891 27.0079 20.1917 27.5063 18.75 27.5063C17.3083 27.5063 15.9109 27.0079 14.7947 26.0955C13.6784 25.1831 12.9119 23.9129 12.625 22.5H3.75ZM26.6575 25H32.5V35H5V25H10.8425C11.5517 26.497 12.6713 27.762 14.0711 28.6478C15.471 29.5336 17.0935 30.0038 18.75 30.0038C20.4065 30.0038 22.029 29.5336 23.4289 28.6478C24.8287 27.762 25.9483 26.497 26.6575 25ZM17.5 15H20V17.5H17.5V15ZM17.5 20H20V22.5H17.5V20Z'
      fill='black'
    />
    <rect x='17.5' y='15' width='2.5' height='2.5' fill='#FF5B00' />
    <rect x='17.5' y='20' width='2.5' height='2.5' fill='#FF5B00' />
  </svg>
)

export default UploadIcon
