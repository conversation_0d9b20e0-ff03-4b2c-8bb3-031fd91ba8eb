import React from "react"

const RabbitIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='40'
    height='40'
    viewBox='0 0 40 40'
    fill='none'
    {...props}
  >
    <path
      d='M17.501 25.6249C17.501 25.9957 17.3911 26.3582 17.1851 26.6666C16.979 26.9749 16.6862 27.2152 16.3436 27.3571C16.001 27.4991 15.624 27.5362 15.2602 27.4639C14.8965 27.3915 14.5624 27.2129 14.3002 26.9507C14.038 26.6885 13.8594 26.3544 13.7871 25.9907C13.7147 25.627 13.7519 25.25 13.8938 24.9073C14.0357 24.5647 14.276 24.2719 14.5844 24.0659C14.8927 23.8598 15.2552 23.7499 15.626 23.7499C16.1233 23.7499 16.6002 23.9474 16.9519 24.2991C17.3035 24.6507 17.501 25.1276 17.501 25.6249ZM24.376 23.7499C24.0052 23.7499 23.6427 23.8598 23.3343 24.0659C23.026 24.2719 22.7857 24.5647 22.6438 24.9073C22.5019 25.25 22.4647 25.627 22.5371 25.9907C22.6094 26.3544 22.788 26.6885 23.0502 26.9507C23.3124 27.2129 23.6465 27.3915 24.0103 27.4639C24.374 27.5362 24.751 27.4991 25.0936 27.3571C25.4362 27.2152 25.729 26.9749 25.935 26.6666C26.1411 26.3582 26.251 25.9957 26.251 25.6249C26.251 25.1276 26.0535 24.6507 25.7019 24.2991C25.3502 23.9474 24.8733 23.7499 24.376 23.7499ZM33.751 29.3749C33.7509 30.9675 33.2828 32.5249 32.4048 33.8536C31.5268 35.1824 30.2777 36.2238 28.8127 36.8484C27.3477 37.473 25.7314 37.6533 24.1648 37.3669C22.5982 37.0805 21.1503 36.3399 20.001 35.2374C19.2271 35.9841 18.3127 36.5698 17.3107 36.9604C16.3088 37.351 15.2392 37.5389 14.1641 37.5131C13.089 37.4872 12.0297 37.2482 11.0477 36.8099C10.0657 36.3715 9.18043 35.7426 8.4433 34.9596C7.70617 34.1765 7.13182 33.2549 6.75356 32.2482C6.3753 31.2415 6.20065 30.1698 6.23974 29.0951C6.27884 28.0203 6.53089 26.9641 6.98129 25.9875C7.43169 25.011 8.07147 24.1336 8.86355 23.4061C9.0234 22.2792 9.35429 21.1832 9.84479 20.1561C6.87605 14.2343 5.08386 6.36863 7.1323 2.98113C7.4372 2.44771 7.8795 2.00577 8.41316 1.70129C8.94682 1.39681 9.55233 1.24093 10.1667 1.24988C12.3729 1.24988 14.2292 3.10769 15.8604 6.93113C16.8323 9.2155 17.5557 11.8249 18.0479 13.9218C19.34 13.6933 20.6621 13.6933 21.9542 13.9218C22.4432 11.8249 23.1667 9.2155 24.1417 6.93113C25.7667 3.10769 27.6292 1.24988 29.8354 1.24988C30.4498 1.24093 31.0553 1.39681 31.5889 1.70129C32.1226 2.00577 32.5649 2.44771 32.8698 2.98113C34.9182 6.36863 33.126 14.2343 30.1573 20.1561C30.6461 21.1814 30.9759 22.2752 31.1354 23.3999C31.9623 24.1597 32.6218 25.0834 33.0721 26.1121C33.5225 27.1408 33.7537 28.2519 33.751 29.3749ZM24.3589 14.6296C25.9802 15.3088 27.419 16.36 28.5589 17.6983C29.5968 15.3861 30.3739 12.9654 30.876 10.4811C31.4214 7.64831 31.3682 5.32488 30.7307 4.27488C30.6498 4.10893 30.5216 3.97058 30.3624 3.87718C30.2031 3.78378 30.0198 3.73949 29.8354 3.74988C28.8026 3.74988 27.5667 5.2655 26.4417 7.90925C25.5151 10.0874 24.8245 12.6155 24.3589 14.6296ZM11.4432 17.6983C12.5831 16.36 14.0219 15.3088 15.6432 14.6296C15.1745 12.6155 14.487 10.0874 13.5604 7.91081C12.4354 5.2655 11.1995 3.74988 10.1667 3.74988C9.98232 3.73949 9.79901 3.78378 9.63973 3.87718C9.48046 3.97058 9.35231 4.10893 9.27136 4.27488C8.63386 5.328 8.58073 7.65613 9.12605 10.4811C9.62815 12.9654 10.4053 15.3861 11.4432 17.6983ZM31.251 29.3749C31.2531 28.5361 31.0669 27.7076 30.7062 26.9504C30.3454 26.1933 29.8193 25.5267 29.1667 24.9999C29.0379 24.8953 28.9313 24.7662 28.853 24.62C28.7746 24.4738 28.7262 24.3134 28.7104 24.1483C28.5011 21.9836 27.4931 19.9744 25.8829 18.5124C24.2728 17.0504 22.1759 16.2405 20.001 16.2405C17.8262 16.2405 15.7293 17.0504 14.1192 18.5124C12.509 19.9744 11.501 21.9836 11.2917 24.1483C11.2759 24.3134 11.2275 24.4738 11.1491 24.62C11.0708 24.7662 10.9642 24.8953 10.8354 24.9999C10.2419 25.4735 9.75043 26.0625 9.39063 26.7312C9.03083 27.3999 8.81015 28.1345 8.7419 28.8908C8.67364 29.6471 8.75922 30.4094 8.99347 31.1317C9.22773 31.854 9.60581 32.5214 10.1049 33.0937C10.6041 33.666 11.2139 34.1313 11.8976 34.4615C12.5814 34.7918 13.325 34.9802 14.0835 35.0154C14.8421 35.0506 15.5999 34.9318 16.3113 34.6662C17.0227 34.4007 17.673 33.9938 18.2229 33.4702L16.701 32.2061C16.5749 32.1011 16.4706 31.9722 16.3942 31.8268C16.3178 31.6815 16.2708 31.5225 16.2559 31.359C16.2409 31.1955 16.2583 31.0306 16.3071 30.8738C16.3559 30.717 16.4351 30.5714 16.5401 30.4452C16.6452 30.319 16.774 30.2147 16.9194 30.1383C17.0648 30.062 17.2237 30.015 17.3872 30C17.5508 29.9851 17.7156 30.0025 17.8724 30.0513C18.0292 30.1 18.1749 30.1792 18.301 30.2843L20.001 31.703L21.701 30.2889C21.9559 30.0768 22.2846 29.9745 22.6148 30.0047C22.9451 30.0349 23.2498 30.195 23.462 30.4499C23.6742 30.7047 23.7764 31.0334 23.7462 31.3637C23.716 31.6939 23.5559 31.9986 23.301 32.2108L21.7792 33.4749C22.5793 34.2249 23.5813 34.7247 24.6617 34.9127C25.7422 35.1007 26.8541 34.9688 27.8605 34.5331C28.867 34.0974 29.7242 33.3771 30.3265 32.4606C30.9289 31.5441 31.2503 30.4716 31.251 29.3749Z'
      fill='black'
    />
    <circle cx='24.5' cy='25.7998' r='2' fill='#FF5B00' />
    <circle cx='15.8008' cy='25.7998' r='2' fill='#FF5B00' />
  </svg>
)

export default RabbitIcon
