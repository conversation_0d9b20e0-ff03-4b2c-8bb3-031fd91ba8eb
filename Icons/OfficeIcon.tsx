import React from "react"

const OfficeIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='40'
    height='41'
    viewBox='0 0 40 41'
    fill='none'
    {...props}
  >
    <path
      d='M23.332 37.1668V13.8335M23.332 37.1668H13.332C8.6187 37.1668 6.26036 37.1668 4.79703 35.7018C3.33203 34.2385 3.33203 31.8802 3.33203 27.1668V13.8335C3.33203 9.12016 3.33203 6.76183 4.79703 5.2985C6.26036 3.8335 8.6187 3.8335 13.332 3.8335C18.0454 3.8335 20.4037 3.8335 21.867 5.2985C23.332 6.76183 23.332 9.12016 23.332 13.8335M23.332 37.1668H29.9987C33.142 37.1668 34.712 37.1668 35.6887 36.1902C36.6654 35.2135 36.6654 33.6435 36.6654 30.5002V20.5002C36.6654 17.3568 36.6654 15.7868 35.6887 14.8102C34.712 13.8335 33.142 13.8335 29.9987 13.8335H23.332M10.832 18.8335H9.16536M17.4987 18.8335H15.832M10.832 12.1668H9.16536M10.832 25.5002H9.16536M17.4987 12.1668H15.832M17.4987 25.5002H15.832M30.832 25.5002H29.1654M30.832 18.8335H29.1654'
      stroke='black'
      strokeWidth='2.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />

    <path d='M9 12H11' stroke='#FF5B00' strokeWidth='3' strokeLinecap='round' />

    <path
      d='M16 12H18'
      stroke='#FF5B00'
      strokeWidth='3'
      strokeLinecap='round'
    />
    <path
      d='M9 18.5H11'
      stroke='#FF5B00'
      strokeWidth='3'
      strokeLinecap='round'
    />
    <path
      d='M16 18.5H18'
      stroke='#FF5B00'
      strokeWidth='3'
      strokeLinecap='round'
    />
    <path
      d='M29 18.5H31'
      stroke='#FF5B00'
      strokeWidth='3'
      strokeLinecap='round'
    />
    <path
      d='M29 25.5H31'
      stroke='#FF5B00'
      strokeWidth='3'
      strokeLinecap='round'
    />
    <path
      d='M9 25.5H11'
      stroke='#FF5B00'
      strokeWidth='3'
      strokeLinecap='round'
    />
    <path
      d='M16 25.5H18'
      stroke='#FF5B00'
      strokeWidth='3'
      strokeLinecap='round'
    />
  </svg>
)

export default OfficeIcon
