import React from "react"

const ShareSessionIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='48'
    height='49'
    viewBox='0 0 48 49'
    fill='none'
    {...props}
  >
    <g clipPath='url(#clip0_875_36016)'>
      <path
        d='M15 31H7C6.447 31 6 30.552 6 30V29C6 27.085 7.068 25.37 8.789 24.523C9.122 24.357 9.522 24.394 9.823 24.614C10.499 25.112 11.501 25.112 12.177 24.614C12.477 24.393 12.873 24.359 13.207 24.52C13.681 24.75 14.124 25.063 14.524 25.45C15.482 26.421 16 27.676 16 29V30C16 30.552 15.553 31 15 31ZM8 29H14C14 28.205 13.687 27.449 13.118 26.872C13.012 26.77 12.895 26.672 12.772 26.584C11.674 27.126 10.324 27.126 9.228 26.584C8.46 27.141 8 28.028 8 29ZM37 23C32.811 23 29.042 20.669 27.165 16.917C26.403 15.416 26 13.714 26 12C26 5.935 30.935 1 37 1C43.065 1 48 5.935 48 12C48 18.065 43.065 23 37 23ZM37 3C32.037 3 28 7.038 28 12C28 13.421 28.32 14.773 28.951 16.017C30.489 19.093 33.572 21 37 21C41.963 21 46 16.962 46 12C46 7.038 41.963 3 37 3Z'
        fill='black'
      />
      <path
        d='M41.0003 44H33.0003C32.4473 44 32.0003 43.552 32.0003 43V42C32.0003 40.085 33.0683 38.37 34.7893 37.523C35.1233 37.357 35.5223 37.394 35.8233 37.614C36.4993 38.112 37.5013 38.112 38.1773 37.614C38.4773 37.393 38.8743 37.358 39.2073 37.52C39.6813 37.75 40.1243 38.063 40.5243 38.45C41.4823 39.421 42.0003 40.676 42.0003 42V43C42.0003 43.552 41.5533 44 41.0003 44ZM34.0003 42H40.0003C40.0003 41.205 39.6873 40.449 39.1183 39.872C39.0123 39.77 38.8953 39.672 38.7723 39.584C37.6743 40.126 36.3243 40.126 35.2283 39.584C34.4603 40.141 34.0003 41.028 34.0003 42ZM19.9413 21.53C19.5743 21.53 19.2213 21.328 19.0453 20.977C18.7983 20.483 18.9983 19.882 19.4923 19.635L27.6113 15.575C28.1053 15.327 28.7053 15.528 28.9533 16.022C29.2003 16.516 29.0003 17.117 28.5063 17.364L20.3873 21.424C20.2443 21.496 20.0923 21.53 19.9413 21.53ZM28.0593 34.53C27.9093 34.53 27.7563 34.496 27.6133 34.425L19.4943 30.365C19.0003 30.118 18.8003 29.517 19.0473 29.023C19.2953 28.528 19.8963 28.328 20.3893 28.576L28.5083 32.636C29.0023 32.883 29.2023 33.484 28.9553 33.978C28.7783 34.328 28.4263 34.53 28.0593 34.53Z'
        fill='black'
      />
      <path
        d='M41 18.0003H33C32.447 18.0003 32 17.5523 32 17.0003V16.0003C32 14.0853 33.068 12.3703 34.789 11.5233C35.123 11.3583 35.522 11.3933 35.823 11.6143C36.499 12.1123 37.501 12.1123 38.177 11.6143C38.477 11.3943 38.874 11.3583 39.207 11.5203C39.681 11.7503 40.124 12.0633 40.524 12.4503C41.482 13.4213 42 14.6763 42 16.0003V17.0003C42 17.5523 41.553 18.0003 41 18.0003ZM34 16.0003H40C40 15.2053 39.687 14.4493 39.118 13.8723C39.012 13.7703 38.895 13.6723 38.772 13.5843C37.674 14.1263 36.324 14.1263 35.228 13.5843C34.46 14.1413 34 15.0283 34 16.0003ZM37 49.0003C30.935 49.0003 26 44.0653 26 38.0003C26 36.2863 26.403 34.5843 27.168 33.0783C29.042 29.3313 32.811 27.0003 37 27.0003C43.065 27.0003 48 31.9353 48 38.0003C48 44.0653 43.065 49.0003 37 49.0003ZM37 29.0003C33.572 29.0003 30.489 30.9073 28.954 33.9773C28.32 35.2273 28 36.5793 28 38.0003C28 42.9623 32.037 47.0003 37 47.0003C41.963 47.0003 46 42.9623 46 38.0003C46 33.0383 41.963 29.0003 37 29.0003Z'
        fill='black'
      />
      <path
        d='M11 36C4.935 36 0 31.065 0 25C0 18.935 4.935 14 11 14C15.189 14 18.958 16.331 20.835 20.083C21.597 21.584 22 23.286 22 25C22 26.714 21.597 28.416 20.832 29.922C18.958 33.669 15.189 36 11 36ZM11 16C6.037 16 2 20.038 2 25C2 29.962 6.037 34 11 34C14.428 34 17.511 32.093 19.046 29.022C19.68 27.773 20 26.421 20 25C20 23.579 19.68 22.227 19.049 20.982C17.511 17.907 14.428 16 11 16Z'
        fill='black'
      />
      <path
        d='M11 27C10.156 27 9.317 26.725 8.638 26.226C7.615 25.489 7 24.281 7 23C7 20.794 8.794 19 11 19C13.206 19 15 20.794 15 23C15 24.281 14.385 25.489 13.354 26.231C12.683 26.725 11.844 27 11 27Z'
        fill='#FF5B00'
      />
      <path
        d='M37 14C36.156 14 35.317 13.725 34.638 13.226C33.615 12.489 33 11.281 33 10C33 7.794 34.794 6 37 6C39.206 6 41 7.794 41 10C41 11.281 40.385 12.489 39.354 13.231C38.683 13.725 37.844 14 37 14Z'
        fill='#FF5B00'
      />
      <path
        d='M37 40C36.156 40 35.317 39.725 34.638 39.226C33.615 38.489 33 37.281 33 36C33 33.794 34.794 32 37 32C39.206 32 41 33.794 41 36C41 37.281 40.385 38.489 39.354 39.231C38.683 39.725 37.844 40 37 40Z'
        fill='#FF5B00'
      />
      <path
        d='M37 40C36.156 40 35.317 39.725 34.638 39.226C33.615 38.489 33 37.281 33 36C33 33.794 34.794 32 37 32C39.206 32 41 33.794 41 36C41 37.281 40.385 38.489 39.354 39.231C38.683 39.725 37.844 40 37 40Z'
        fill='black'
        fillOpacity='0.2'
      />
    </g>
    <defs>
      <clipPath id='clip0_875_36016'>
        <rect
          width='48'
          height='48'
          fill='white'
          transform='translate(0 0.5)'
        />
      </clipPath>
    </defs>
  </svg>
)

export default ShareSessionIcon
