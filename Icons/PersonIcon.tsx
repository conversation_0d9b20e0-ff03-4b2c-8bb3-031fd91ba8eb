import React from "react"

const PersonIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='40'
    height='40'
    viewBox='0 0 40 40'
    fill='none'
    {...props}
  >
    <path
      d='M20.3027 3.42857C19.6273 3.42857 18.9586 3.56159 18.3346 3.82005C17.7107 4.0785 17.1437 4.45732 16.6662 4.93488C16.1886 5.41244 15.8098 5.97938 15.5513 6.60334C15.2929 7.2273 15.1598 7.89606 15.1598 8.57143C15.1598 9.2468 15.2929 9.91556 15.5513 10.5395C15.8098 11.1635 16.1886 11.7304 16.6662 12.208C17.1437 12.6855 17.7107 13.0644 18.3346 13.3228C18.9586 13.5813 19.6273 13.7143 20.3027 13.7143C21.6667 13.7143 22.9748 13.1724 23.9393 12.208C24.9037 11.2435 25.4456 9.9354 25.4456 8.57143C25.4456 7.20746 24.9037 5.89935 23.9393 4.93488C22.9748 3.97041 21.6667 3.42857 20.3027 3.42857ZM11.7313 8.57143C11.7313 6.29814 12.6343 4.11797 14.2418 2.51051C15.8492 0.903059 18.0294 0 20.3027 0C22.576 0 24.7562 0.903059 26.3636 2.51051C27.9711 4.11797 28.8741 6.29814 28.8741 8.57143C28.8741 10.8447 27.9711 13.0249 26.3636 14.6323C24.7562 16.2398 22.576 17.1429 20.3027 17.1429C18.0294 17.1429 15.8492 16.2398 14.2418 14.6323C12.6343 13.0249 11.7313 10.8447 11.7313 8.57143ZM14.1633 21.872C14.465 22.096 14.8536 22.3589 15.3176 22.6194C16.4901 23.2823 18.209 23.968 20.3004 23.968C22.3918 23.968 24.113 23.2823 25.2856 22.6194C25.7496 22.3589 26.1381 22.096 26.4398 21.872C26.8696 21.9863 27.2955 22.1128 27.7176 22.2514L29.9119 22.9714C31.5576 23.5131 32.801 24.8274 33.2444 26.4526L34.1518 32.9989C34.3233 34.2423 33.6718 35.1451 32.7667 35.36C30.3736 35.9314 26.3393 36.5714 20.3027 36.5714C14.2661 36.5714 10.2296 35.9314 7.83642 35.36C6.93356 35.1451 6.28214 34.2423 6.45128 32.9989L7.35871 26.4526C7.58418 25.6422 8.0067 24.9002 8.58858 24.2928C9.17046 23.6854 9.89362 23.2315 10.6936 22.9714L12.8878 22.2514C13.3084 22.1143 13.7336 21.9878 14.1633 21.872ZM15.7861 18.7749L15.121 18.1189L14.2021 18.3291C13.4006 18.512 12.6052 18.7337 11.8158 18.9943L9.62156 19.7143C8.2584 20.1574 7.02938 20.9379 6.04872 21.9833C5.06807 23.0287 4.3676 24.305 4.01242 25.6937L3.98956 25.7851L3.05699 32.528C2.68899 35.1886 4.11299 37.9977 7.04328 38.6949C9.71756 39.3326 14.0193 40 20.3004 40C26.5838 40 30.8856 39.3326 33.5598 38.6971C36.4901 37.9977 37.9164 35.1886 37.5484 32.5303L36.6136 25.7874L36.5907 25.696C36.2357 24.3067 35.535 23.0297 34.5539 21.9838C33.5728 20.938 32.3431 20.1573 30.9793 19.7143L28.7873 18.9943C27.9995 18.7352 27.204 18.5143 26.401 18.3314L25.4821 18.1189L24.8193 18.7726L24.817 18.7749C24.7286 18.8554 24.6371 18.9324 24.5427 19.0057C24.248 19.2398 23.9346 19.4493 23.6056 19.632C22.6007 20.2128 21.4633 20.5253 20.3027 20.5394C19.1421 20.5253 18.0047 20.2128 16.9998 19.632C16.568 19.3881 16.1598 19.1009 15.7861 18.7749Z'
      fill='currentColor'
    />
  </svg>
)

export default PersonIcon
