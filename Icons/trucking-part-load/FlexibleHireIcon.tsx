import * as React from "react"

const FlexibleHireIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width='28'
    height='28'
    viewBox='0 0 28 28'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    {/* Add the SVG content from Flexible hire.svg here once provided */}
    <path
      fill='#000'
      d='M5.956 24.95h-2.47a2.198 2.198 0 0 1-2.195-2.195V3.486c0-1.21.985-2.195 2.195-2.195h10.186c1.21 0 2.195.985 2.195 2.195v3.266a.395.395 0 0 0 .791 0V3.486A2.99 2.99 0 0 0 13.672.5H3.486A2.99 2.99 0 0 0 .5 3.486v19.269a2.99 2.99 0 0 0 2.986 2.986h2.47a.395.395 0 1 0 0-.79Z'
    />
    <path
      fill='#000'
      d='M16.263 8.042a.395.395 0 0 0-.396.395v8.337a.395.395 0 1 0 .791 0V8.437a.395.395 0 0 0-.395-.395ZM2.477 18.283a.395.395 0 0 0-.395.395v4.077c0 .774.63 1.404 1.404 1.404h2.207a.395.395 0 1 0 0-.79H3.486a.614.614 0 0 1-.613-.614v-4.077a.395.395 0 0 0-.396-.395ZM14.681 16.578a.395.395 0 0 0 .396-.395V3.486c0-.774-.63-1.404-1.405-1.404H11.61a.897.897 0 0 0-.714.357l-.294.391a.3.3 0 0 1-.24.12H6.798a.3.3 0 0 1-.24-.12l-.294-.391a.897.897 0 0 0-.714-.357H3.486c-.774 0-1.404.63-1.404 1.404v13.507a.395.395 0 0 0 .79 0V3.486c0-.338.276-.613.614-.613H5.55c.032 0 .062.015.082.04l.294.392c.205.273.53.436.872.436h3.565c.341 0 .667-.163.872-.436l.294-.392c.02-.025.05-.04.082-.04h2.062c.338 0 .614.275.614.613v12.697c0 .218.177.395.395.395Z'
    />
    <path
      fill='#000'
      d='M16.755 18.154c-.265 0-.512.073-.725.199a1.423 1.423 0 0 0-1.989-.748 1.423 1.423 0 0 0-2.019-.973v-2.668c0-.784-.638-1.421-1.421-1.421-.784 0-1.421.638-1.421 1.421v4.594h-.223c-1.153 0-2.09.938-2.09 2.091v2.672c0 .95.216 1.906.626 2.763l.569 1.191a.396.396 0 1 0 .713-.341l-.569-1.19a5.653 5.653 0 0 1-.549-2.423v-2.672c0-.717.583-1.3 1.3-1.3h.223v2.915a.395.395 0 0 0 .79 0v-8.3a.63.63 0 0 1 1.261 0v5.412a.395.395 0 0 0 .791 0v-1.47a.63.63 0 0 1 1.26 0v1.953a.395.395 0 1 0 .791 0v-.998a.63.63 0 0 1 1.26 0v1.422a.395.395 0 1 0 .792 0v-.708a.631.631 0 0 1 1.26 0v2.973a.395.395 0 1 0 .791 0v-2.973c0-.784-.637-1.421-1.421-1.421ZM17.78 23.838a.395.395 0 0 0-.395.396v2.87a.395.395 0 1 0 .79 0v-2.87a.395.395 0 0 0-.395-.396Z'
    />
    <path
      fill='#FF5B00'
      d='M11.665 8.35c0-.58-.161-1.123-.44-1.586l1.047-1.047a.396.396 0 0 0-.56-.56l-.982.983a3.075 3.075 0 0 0-2.15-.876A3.09 3.09 0 0 0 5.492 8.35a3.09 3.09 0 0 0 3.086 3.086 3.09 3.09 0 0 0 3.086-3.086Zm-5.381 0a2.298 2.298 0 0 1 2.295-2.295c.618 0 1.179.246 1.592.644L8.389 8.481l-.337-.269a.395.395 0 1 0-.493.618l.612.49a.394.394 0 0 0 .527-.03l1.944-1.944a2.298 2.298 0 0 1-2.063 3.299A2.298 2.298 0 0 1 6.284 8.35Z'
    />
  </svg>
)

export default FlexibleHireIcon
