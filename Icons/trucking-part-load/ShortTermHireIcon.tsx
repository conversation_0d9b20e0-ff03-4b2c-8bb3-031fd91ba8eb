import * as React from "react"

const ShortTermHireIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width='28'
    height='28'
    viewBox='0 0 28 28'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <path
      d='M19.3665 14.4768H16.6074C16.3754 14.4768 16.1855 14.287 16.1855 14.0549C16.1855 13.8229 16.3754 13.6331 16.6074 13.6331H19.3665C19.5985 13.6331 19.7884 13.8229 19.7884 14.0549C19.7884 14.287 19.5985 14.4768 19.3665 14.4768Z'
      fill='black'
    />
    <path
      d='M15.7852 13.654C15.5531 13.654 15.3633 13.4642 15.3633 13.2321V8.62524C15.3633 8.39321 15.5531 8.20337 15.7852 8.20337C16.0172 8.20337 16.207 8.39321 16.207 8.62524V13.2279C16.207 13.4642 16.0172 13.654 15.7852 13.654Z'
      fill='black'
    />
    <path
      d='M15.7846 15.2994C15.0969 15.2994 14.54 14.7426 14.54 14.0549C14.54 13.3672 15.0969 12.8104 15.7846 12.8104C16.4722 12.8104 17.0291 13.3672 17.0291 14.0549C17.0291 14.7426 16.4722 15.2994 15.7846 15.2994ZM15.7846 13.6541C15.5652 13.6541 15.3838 13.8355 15.3838 14.0549C15.3838 14.2743 15.5652 14.4557 15.7846 14.4557C16.0039 14.4557 16.1854 14.2743 16.1854 14.0549C16.1854 13.8313 16.0039 13.6541 15.7846 13.6541Z'
      fill='black'
    />
    <path
      d='M15.6619 24.9055C12.5738 24.9055 9.62065 23.5934 7.5619 21.3069C7.2244 20.9356 7.14425 20.4167 7.34675 19.9611C7.54925 19.5055 7.98378 19.2228 8.48581 19.2228C8.83597 19.2228 9.17347 19.3747 9.41394 19.6447C11.0086 21.4081 13.2867 22.4164 15.6619 22.4164C17.9907 22.4164 20.1591 21.4841 21.7664 19.7966C23.3696 18.1091 24.188 15.89 24.0657 13.557C23.8421 9.20749 20.1127 5.63421 15.7505 5.5878C13.1349 5.55827 10.6838 6.72687 9.05534 8.78983C8.81066 9.0978 8.45206 9.27499 8.07237 9.27499C7.59144 9.27499 7.1569 9.00499 6.94597 8.57046C6.73925 8.14437 6.78987 7.64655 7.08097 7.2753C9.25362 4.50358 12.6539 2.95108 16.1766 3.10718C21.6652 3.3603 26.2214 7.84905 26.5505 13.3292C26.7319 16.3751 25.6857 19.2692 23.6058 21.4756C21.5217 23.6905 18.6994 24.9055 15.6619 24.9055ZM8.48581 20.0666C8.24534 20.0666 8.14409 20.248 8.11878 20.3028C8.09347 20.3576 8.02597 20.5601 8.1905 20.7416C10.0932 22.8509 12.8142 24.0617 15.6619 24.0617C18.4632 24.0617 21.0661 22.9395 22.9899 20.8976C24.9094 18.86 25.8755 16.1895 25.7067 13.3798C25.403 8.32155 21.2011 4.18296 16.1386 3.94671C12.886 3.79905 9.74722 5.23343 7.74331 7.78999C7.61253 7.95874 7.67159 8.13171 7.70112 8.19499C7.72644 8.24983 7.83191 8.42702 8.06816 8.42702C8.1905 8.42702 8.30862 8.36796 8.38878 8.26249C10.1817 5.9928 12.8817 4.71452 15.7547 4.73983C20.6316 4.79046 24.6521 8.64218 24.901 13.5064C25.0317 16.0756 24.1332 18.5141 22.3697 20.3703C20.6021 22.2308 18.2185 23.2559 15.6535 23.2559C13.0421 23.2559 10.5361 22.1422 8.78112 20.2058C8.70519 20.1172 8.59972 20.0666 8.48581 20.0666Z'
      fill='black'
    />
    <path
      d='M12.3081 17.9656H4.33887C4.10684 17.9656 3.91699 17.7758 3.91699 17.5438C3.91699 17.3117 4.10684 17.1219 4.33887 17.1219H12.3039C12.5359 17.1219 12.7257 17.3117 12.7257 17.5438C12.7257 17.7758 12.5401 17.9656 12.3081 17.9656Z'
      fill='#FF5B00'
    />
    <path
      d='M8.91246 15.776H1.85449C1.62246 15.776 1.43262 15.5862 1.43262 15.3541C1.43262 15.1221 1.62246 14.9323 1.85449 14.9323H8.91246C9.14449 14.9323 9.33434 15.1221 9.33434 15.3541C9.33434 15.5862 9.14449 15.776 8.91246 15.776Z'
      fill='#FF5B00'
    />
    <path
      d='M9.04707 13.5865H5.84082C5.60879 13.5865 5.41895 13.3967 5.41895 13.1647C5.41895 12.9326 5.60879 12.7428 5.84082 12.7428H9.04707C9.2791 12.7428 9.46895 12.9326 9.46895 13.1647C9.46895 13.3967 9.2791 13.5865 9.04707 13.5865Z'
      fill='#FF5B00'
    />
    <path
      d='M12.3084 11.5531H4.01855C3.78652 11.5531 3.59668 11.3633 3.59668 11.1312C3.59668 10.8992 3.78652 10.7094 4.01855 10.7094H12.3042C12.5362 10.7094 12.7261 10.8992 12.7261 11.1312C12.7261 11.3633 12.5404 11.5531 12.3084 11.5531Z'
      fill='#FF5B00'
    />
  </svg>
)

export default ShortTermHireIcon
