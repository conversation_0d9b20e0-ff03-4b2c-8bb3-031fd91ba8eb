import * as React from "react"

const SpeedyDeliveryIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width='27'
    height='27'
    viewBox='0 0 27 27'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <path
      d='M26.9302 15.4739L25.5636 13.3828C24.9601 12.4588 23.9347 11.907 22.8202 11.907H19.2857V9.87306C19.2857 8.63122 18.2758 7.62136 17.034 7.62136H10.4174C10.0461 5.68232 8.33931 4.21118 6.2936 4.21118C3.97715 4.21118 2.0928 6.09578 2.0928 8.41198C2.0928 9.8684 2.83854 11.1532 3.96758 11.907H0.428663C0.19177 11.907 0 12.0988 0 12.3357C0 12.5726 0.19177 12.7643 0.428663 12.7643H4.71431V13.6214H2.99991C2.76326 13.6214 2.57149 13.8132 2.57149 14.0501C2.57149 14.287 2.76326 14.4785 2.99991 14.4785H4.71431V15.3356H0.428663C0.19177 15.3356 0 15.5273 0 15.7642C0 16.0011 0.19177 16.1929 0.428663 16.1929H4.71431V18.091C3.95876 18.4243 3.42857 19.1781 3.42857 20.0555V20.4785C3.42857 20.7154 3.62058 20.9072 3.85723 20.9072H7.6583C7.90206 21.9558 8.84228 22.7406 9.96421 22.7406C11.0864 22.7406 12.0264 21.9558 12.2701 20.9072H20.5155C20.7595 21.9558 21.6997 22.7406 22.8214 22.7406C23.9431 22.7406 24.8835 21.9558 25.1273 20.9072H26.5715C26.8084 20.9072 26.9999 20.7154 26.9999 20.4785V15.7083C26.9999 15.6249 26.9756 15.5433 26.9302 15.4739ZM2.94988 8.41198C2.94988 6.56834 4.44995 5.06826 6.2936 5.06826C8.13748 5.06826 9.63731 6.56834 9.63731 8.41198C9.63731 10.2556 8.13748 11.7557 6.2936 11.7557C4.44995 11.7557 2.94988 10.2556 2.94988 8.41198ZM5.57139 16.1929H6.42847C6.66537 16.1929 6.85714 16.0011 6.85714 15.7642C6.85714 15.5273 6.66537 15.3356 6.42847 15.3356H5.57139V14.4785H8.57154C8.80819 14.4785 8.99996 14.287 8.99996 14.0501C8.99996 13.8132 8.80819 13.6214 8.57154 13.6214H5.57139V12.7643H11.1428C11.3794 12.7643 11.5714 12.5726 11.5714 12.3357C11.5714 12.0988 11.3794 11.907 11.1428 11.907H8.61961C9.73124 11.1647 10.4686 9.90715 10.4912 8.47844H17.0342C17.803 8.47844 18.4286 9.10427 18.4286 9.87306V17.9071H5.57679L5.57139 17.9076V16.1929ZM18.4286 20.0499H12.3091C12.2417 19.557 12.0229 19.1126 11.6999 18.7641H18.4286V20.0499ZM5.57679 18.7641H8.22847C7.90574 19.1126 7.68675 19.557 7.61956 20.0499H4.28565C4.28859 19.3407 4.86685 18.7641 5.57679 18.7641ZM9.96421 21.8835C9.13067 21.8835 8.45212 21.2052 8.45212 20.3714C8.45212 19.5378 9.13067 18.8593 9.96421 18.8593C10.798 18.8593 11.4763 19.5378 11.4763 20.3714C11.4763 21.2052 10.798 21.8835 9.96421 21.8835ZM22.8214 21.8835C21.9879 21.8835 21.3093 21.2052 21.3093 20.3714C21.3093 19.5378 21.9879 18.8593 22.8214 18.8593C23.6552 18.8593 24.3335 19.5378 24.3335 20.3714C24.3335 21.2052 23.6552 21.8835 22.8214 21.8835ZM26.1428 20.0499H25.1663C25.0086 18.8956 24.0184 18.0022 22.8214 18.0022C21.6244 18.0022 20.6344 18.8956 20.4765 20.0499H19.2857V12.7643H22.8202C23.6439 12.7643 24.4014 13.1707 24.8463 13.8514L26.1428 15.8358V20.0499Z'
      fill='black'
    />
    <path
      d='M23.5223 20.3714C23.5223 20.7586 23.2085 21.0725 22.8212 21.0725C22.434 21.0725 22.1201 20.7586 22.1201 20.3714C22.1201 19.9842 22.434 19.6703 22.8212 19.6703C23.2085 19.6703 23.5223 19.9842 23.5223 20.3714Z'
      fill='black'
    />
    <path
      d='M10.6647 20.3714C10.6647 20.7586 10.3508 21.0725 9.96356 21.0725C9.57659 21.0725 9.2627 20.7586 9.2627 20.3714C9.2627 19.9842 9.57659 19.6703 9.96356 19.6703C10.3508 19.6703 10.6647 19.9842 10.6647 20.3714Z'
      fill='black'
    />
    <path
      d='M6.85718 8.41197V6.53694C6.85718 6.30005 6.66541 6.10852 6.42852 6.10852C6.19187 6.10852 6.0001 6.30005 6.0001 6.53694V8.27219L5.12536 9.4721C4.98583 9.66338 5.02776 9.93117 5.21904 10.0705C5.29555 10.1264 5.38384 10.1531 5.47138 10.1531C5.60356 10.1531 5.73403 10.092 5.81789 9.97679L6.77503 8.66431C6.82824 8.59123 6.85718 8.50295 6.85718 8.41197Z'
      fill='#FF5B00'
    />
    <path
      d='M22.6762 13.1927H21.8564C21.6195 13.1927 21.4277 13.3845 21.4277 13.6214V16.6213C21.4277 16.8582 21.6195 17.05 21.8564 17.05H24.7408C25.0096 17.05 25.2501 16.9109 25.385 16.6787C25.5199 16.4465 25.5201 16.1684 25.3872 15.9366L24.3864 14.185C24.037 13.5733 23.3815 13.1927 22.6762 13.1927ZM22.2851 16.1929V14.0501H22.6762C23.0747 14.0501 23.4448 14.2647 23.6422 14.6104L24.5463 16.1929H22.2851Z'
      fill='black'
    />
  </svg>
)

export default SpeedyDeliveryIcon
