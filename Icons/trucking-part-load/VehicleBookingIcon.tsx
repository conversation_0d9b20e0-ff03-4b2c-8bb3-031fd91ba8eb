import * as React from "react"

const VehicleBookingIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width='27'
    height='27'
    viewBox='0 0 27 27'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <path
      d='M25.1099 12.2008L24.4107 9.40404C24.6019 9.36344 24.7453 9.19384 24.7453 8.99066V8.53973C24.7453 7.56066 23.9487 6.76416 22.9697 6.76416H19.785V5.83403C19.785 5.35225 19.3931 4.96033 18.9113 4.96033H2.67743C2.19565 4.96033 1.80373 5.35225 1.80373 5.83403V13.5C1.80373 13.7335 1.99299 13.9228 2.2265 13.9228C2.45995 13.9228 2.64927 13.7335 2.64927 13.5V5.83403C2.64927 5.81847 2.66187 5.80587 2.67743 5.80587H18.9112C18.9268 5.80587 18.9394 5.81847 18.9394 5.83403V13.5001C18.9394 13.7336 19.1287 13.9229 19.3622 13.9229C19.5956 13.9229 19.7849 13.7336 19.7849 13.5001V13.0209H24.772C24.7724 13.0209 24.7727 13.021 24.7731 13.021C24.7735 13.021 24.7739 13.0209 24.7742 13.0209C25.3879 13.0214 25.909 13.4241 26.088 13.9792H24.7734C24.5399 13.9792 24.3506 14.1684 24.3506 14.4019V15.3038C24.3506 16.0342 24.9448 16.6284 25.6753 16.6284H26.1544V18.4885H25.049C24.6859 17.44 23.6891 16.6848 22.5187 16.6848C21.3482 16.6848 20.3515 17.44 19.9884 18.4885H19.7848V15.3038C19.7848 15.0703 19.5956 14.881 19.3621 14.881C19.1286 14.881 18.9393 15.0702 18.9393 15.3038V18.4884H10.1681C9.80496 17.4399 8.80822 16.6847 7.63778 16.6847C6.46734 16.6847 5.47056 17.4399 5.10748 18.4884H2.67743C2.66187 18.4884 2.64927 18.4758 2.64927 18.4603V17.5302H4.48121C4.71466 17.5302 4.90398 17.3409 4.90398 17.1074C4.90398 16.8739 4.71472 16.6847 4.48121 16.6847H0.422772C0.189316 16.6847 0 16.8739 0 17.1074C0 17.3409 0.189264 17.5302 0.422772 17.5302H1.80378V18.4603C1.80378 18.9421 2.1957 19.334 2.67748 19.334H4.96109C4.96099 19.3434 4.96035 19.3527 4.96035 19.3621C4.96035 20.8385 6.16149 22.0396 7.63778 22.0396C9.11408 22.0396 10.3152 20.8385 10.3152 19.3621C10.3152 19.3526 10.3146 19.3434 10.3145 19.334H19.842C19.8419 19.3434 19.8413 19.3527 19.8413 19.3621C19.8413 20.8385 21.0424 22.0396 22.5187 22.0396C23.995 22.0396 25.1961 20.8385 25.1961 19.3621C25.1961 19.3526 25.1955 19.3434 25.1954 19.334H26.5772C26.8106 19.334 26.9999 19.1447 26.9999 18.9112V14.4018C27 13.2885 26.1786 12.3635 25.1099 12.2008ZM19.785 7.60965H22.9697C23.4826 7.60965 23.8998 8.02688 23.8998 8.53973V8.56789H19.785V7.60965ZM19.785 12.1754V9.41338H23.5415L24.232 12.1754H19.785ZM7.63778 21.1942C6.62766 21.1942 5.80584 20.3724 5.80584 19.3622C5.80584 18.3521 6.62766 17.5303 7.63778 17.5303C8.64791 17.5303 9.46972 18.3521 9.46972 19.3622C9.46972 20.3724 8.64791 21.1942 7.63778 21.1942ZM22.5188 21.1942C21.5087 21.1942 20.6869 20.3724 20.6869 19.3622C20.6869 18.3521 21.5087 17.5303 22.5188 17.5303C23.5289 17.5303 24.3507 18.3521 24.3507 19.3622C24.3507 20.3724 23.5289 21.1942 22.5188 21.1942ZM26.1545 15.7829H25.6754C25.4112 15.7829 25.1962 15.568 25.1962 15.3038V14.8246H26.1545V15.7829H26.1545Z'
      fill='black'
    />
    <path
      d='M7.63738 18.4885C7.15559 18.4885 6.76367 18.8804 6.76367 19.3622C6.76367 19.844 7.15559 20.2359 7.63738 20.2359C8.11916 20.2359 8.51108 19.844 8.51108 19.3622C8.51108 18.8804 8.11916 18.4885 7.63738 18.4885Z'
      fill='black'
    />
    <path
      d='M22.5192 18.4885C22.0374 18.4885 21.6455 18.8804 21.6455 19.3622C21.6455 19.844 22.0374 20.2359 22.5192 20.2359C23.001 20.2359 23.3929 19.844 23.3929 19.3622C23.3929 18.8804 23.001 18.4885 22.5192 18.4885Z'
      fill='black'
    />
    <path
      d='M17.5582 16.6848H11.245C11.0116 16.6848 10.8223 16.8741 10.8223 17.1076C10.8223 17.3411 11.0115 17.5304 11.245 17.5304H17.5582C17.7916 17.5304 17.981 17.3411 17.981 17.1076C17.981 16.8741 17.7917 16.6848 17.5582 16.6848Z'
      fill='black'
    />
    <path
      d='M6.73542 14.881H1.32414C1.09068 14.881 0.901367 15.0702 0.901367 15.3038C0.901367 15.5373 1.09063 15.7265 1.32414 15.7265H6.73542C6.96888 15.7265 7.1582 15.5373 7.1582 15.3038C7.1582 15.0702 6.96888 14.881 6.73542 14.881Z'
      fill='black'
    />
    <path
      d='M14.7008 9.14259C14.5358 8.97753 14.2681 8.97753 14.103 9.14264L10.3435 12.9021L8.3877 10.9464C8.22259 10.7813 7.95491 10.7813 7.78985 10.9464C7.62474 11.1115 7.62474 11.3791 7.78985 11.5442L10.0446 13.7989C10.1271 13.8815 10.2353 13.9227 10.3435 13.9227C10.4516 13.9227 10.5599 13.8815 10.6424 13.7989L14.7008 9.74049C14.8659 9.57532 14.8659 9.3077 14.7008 9.14259Z'
      fill='#FF5B00'
    />
  </svg>
)

export default VehicleBookingIcon
