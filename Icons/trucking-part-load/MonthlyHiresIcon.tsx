import React from "react"

interface MonthlyHiresIconProps extends React.SVGProps<SVGSVGElement> {}

const MonthlyHiresIcon: React.FC<MonthlyHiresIconProps> = (props) => {
  return (
    <svg
      width='28'
      height='28'
      viewBox='0 0 28 28'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <g clipPath='url(#clip0_2045_250)'>
        <path
          d='M11.2051 20.3281C11.2051 20.6194 10.969 20.8555 10.6777 20.8555C10.3865 20.8555 10.1504 20.6194 10.1504 20.3281C10.1504 20.0368 10.3865 19.8008 10.6777 19.8008C10.969 19.8008 11.2051 20.0368 11.2051 20.3281Z'
          fill='#FF5B00'
        />
        <path
          d='M24.3359 20.6445H21.6992V18.0078C21.6992 17.7165 21.4632 17.4805 21.1719 17.4805C20.8806 17.4805 20.6445 17.7165 20.6445 18.0078V21.1719C20.6445 21.4632 20.8806 21.6992 21.1719 21.6992H24.3359C24.6272 21.6992 24.8633 21.4632 24.8633 21.1719C24.8633 20.8806 24.6272 20.6445 24.3359 20.6445Z'
          fill='#FF5B00'
        />
        <path
          d='M4.19141 11.3633H7.51367C7.80495 11.3633 8.04102 11.1272 8.04102 10.8359C8.04102 10.5447 7.80495 10.3086 7.51367 10.3086H4.19141C3.90013 10.3086 3.66406 10.5447 3.66406 10.8359C3.66406 11.1272 3.90013 11.3633 4.19141 11.3633Z'
          fill='#FF5B00'
        />
        <path
          d='M14 10.3086H10.6777C10.3865 10.3086 10.1504 10.5447 10.1504 10.8359C10.1504 11.1272 10.3865 11.3633 10.6777 11.3633H14C14.2913 11.3633 14.5273 11.1272 14.5273 10.8359C14.5273 10.5447 14.2913 10.3086 14 10.3086Z'
          fill='#FF5B00'
        />
        <path
          d='M4.19141 14.5273H7.51367C7.80495 14.5273 8.04102 14.2913 8.04102 14C8.04102 13.7087 7.80495 13.4727 7.51367 13.4727H4.19141C3.90013 13.4727 3.66406 13.7087 3.66406 14C3.66406 14.2913 3.90013 14.5273 4.19141 14.5273Z'
          fill='#FF5B00'
        />
        <path
          d='M8.56836 19.8008H4.19141C3.90013 19.8008 3.66406 20.0368 3.66406 20.3281C3.66406 20.6194 3.90013 20.8555 4.19141 20.8555H8.56836C8.85963 20.8555 9.0957 20.6194 9.0957 20.3281C9.0957 20.0368 8.85963 19.8008 8.56836 19.8008Z'
          fill='#FF5B00'
        />
        <path
          d='M14 13.4727H10.6777C10.3865 13.4727 10.1504 13.7087 10.1504 14C10.1504 14.2913 10.3865 14.5273 10.6777 14.5273H14C14.2913 14.5273 14.5273 14.2913 14.5273 14C14.5273 13.7087 14.2913 13.4727 14 13.4727Z'
          fill='#FF5B00'
        />
        <path
          d='M4.19141 17.6914H7.51367C7.80495 17.6914 8.04102 17.4553 8.04102 17.1641C8.04102 16.8728 7.80495 16.6367 7.51367 16.6367H4.19141C3.90013 16.6367 3.66406 16.8728 3.66406 17.1641C3.66406 17.4553 3.90013 17.6914 4.19141 17.6914Z'
          fill='#FF5B00'
        />
        <path
          d='M14 16.6367H10.6777C10.3865 16.6367 10.1504 16.8728 10.1504 17.1641C10.1504 17.4553 10.3865 17.6914 10.6777 17.6914H14C14.2913 17.6914 14.5273 17.4553 14.5273 17.1641C14.5273 16.8728 14.2913 16.6367 14 16.6367Z'
          fill='#FF5B00'
        />
        <path
          d='M17.1641 11.3633H20.3281C20.6194 11.3633 20.8555 11.1272 20.8555 10.8359C20.8555 10.5447 20.6194 10.3086 20.3281 10.3086H17.1641C16.8728 10.3086 16.6367 10.5447 16.6367 10.8359C16.6367 11.1272 16.8728 11.3633 17.1641 11.3633Z'
          fill='#FF5B00'
        />
        <path
          d='M24.0195 15.5206V4.19141C24.0195 3.31902 23.3099 2.60938 22.4375 2.60938H20.8555V2.08203C20.8555 1.208 20.1475 0.5 19.2734 0.5C18.4011 0.5 17.6914 1.20965 17.6914 2.08203V2.60938H13.3145V2.08203C13.3145 1.208 12.6065 0.5 11.7324 0.5C10.86 0.5 10.1504 1.20965 10.1504 2.08203V2.60938H5.77344V2.08203C5.77344 1.208 5.06544 0.5 4.19141 0.5C3.31902 0.5 2.60938 1.20965 2.60938 2.08203V2.60938H2.08203C1.20965 2.60938 0.5 3.31902 0.5 4.19141V21.3828C0.5 22.2552 1.20965 22.9648 2.08203 22.9648H15.1033C15.8991 25.6537 18.3679 27.5 21.1719 27.5C24.6612 27.5 27.5 24.6612 27.5 21.1719C27.5 18.7725 26.1446 16.5918 24.0195 15.5206ZM18.7461 2.08203C18.7461 1.79117 18.9826 1.55469 19.2734 1.55469C19.5645 1.55469 19.8008 1.79076 19.8008 2.08203V4.19141C19.8008 4.48227 19.5643 4.71875 19.2734 4.71875C18.9826 4.71875 18.7461 4.48227 18.7461 4.19141V2.08203ZM11.2051 2.08203C11.2051 1.79117 11.4416 1.55469 11.7324 1.55469C12.0235 1.55469 12.2598 1.79076 12.2598 2.08203V4.19141C12.2598 4.48227 12.0233 4.71875 11.7324 4.71875C11.4416 4.71875 11.2051 4.48227 11.2051 4.19141V2.08203ZM3.66406 2.08203C3.66406 1.79117 3.90054 1.55469 4.19141 1.55469C4.48248 1.55469 4.71875 1.79076 4.71875 2.08203V4.19141C4.71875 4.48227 4.48227 4.71875 4.19141 4.71875C3.90054 4.71875 3.66406 4.48227 3.66406 4.19141V2.08203ZM2.08203 3.66406H2.60938V4.19141C2.60938 5.06379 3.31902 5.77344 4.19141 5.77344C5.06379 5.77344 5.77344 5.06379 5.77344 4.19141V3.66406H10.1504V4.19141C10.1504 5.06379 10.86 5.77344 11.7324 5.77344C12.6048 5.77344 13.3145 5.06379 13.3145 4.19141V3.66406H17.6914V4.19141C17.6914 5.06379 18.4011 5.77344 19.2734 5.77344C20.1458 5.77344 20.8555 5.06379 20.8555 4.19141V3.66406H22.4375C22.7284 3.66406 22.9648 3.90054 22.9648 4.19141V6.98633H1.55469V4.19141C1.55469 3.90054 1.79117 3.66406 2.08203 3.66406ZM1.55469 21.3828V8.04102H22.9648V15.1017C22.3862 14.9305 21.7859 14.8438 21.1719 14.8438C17.6825 14.8438 14.8438 17.6825 14.8438 21.1719C14.8438 21.4187 14.8586 21.6654 14.887 21.9102H2.08203C1.79117 21.9102 1.55469 21.6737 1.55469 21.3828ZM21.1719 26.4453C18.3452 26.4453 15.8984 24.1421 15.8984 21.1719C15.8984 18.2641 18.2641 15.8984 21.1719 15.8984C24.0902 15.8984 26.4453 18.2661 26.4453 21.1719C26.4453 24.0797 24.0797 26.4453 21.1719 26.4453Z'
          fill='black'
        />
      </g>
      <defs>
        <clipPath id='clip0_2045_250'>
          <rect
            width='27'
            height='27'
            fill='white'
            transform='translate(0.5 0.5)'
          />
        </clipPath>
      </defs>
    </svg>
  )
}

export default MonthlyHiresIcon
