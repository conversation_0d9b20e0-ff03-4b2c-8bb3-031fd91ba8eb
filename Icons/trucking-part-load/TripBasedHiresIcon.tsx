import * as React from "react"

const TripBasedHiresIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width='28'
    height='28'
    viewBox='0 0 28 28'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <g clipPath='url(#clip0_2045_216)'>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M9.58199 20.9478H2.97292C1.84045 20.9478 0.923828 20.0309 0.923828 18.9001V5.48329C0.923828 4.35093 1.8405 3.43426 2.97292 3.43426H14.5982V3.40964C14.5982 2.66433 15.208 2.05546 15.9524 2.05546H16.5903V1.29411C16.5903 1.08944 16.7563 0.923523 16.9609 0.923523H18.2253C18.4299 0.923523 18.5959 1.08944 18.5959 1.29411V2.05546H19.8344V1.29411C19.8344 1.08944 20.0003 0.923523 20.205 0.923523H21.47C21.6747 0.923523 21.8406 1.08944 21.8406 1.29411V2.05546H23.0791V1.29411C23.0791 1.08944 23.245 0.923523 23.4497 0.923523H24.714C24.9187 0.923523 25.0846 1.08944 25.0846 1.29411V2.05546H25.7226C26.4669 2.05546 27.0768 2.66433 27.0768 3.40964V11.8906C27.0768 12.6349 26.4669 13.2447 25.7226 13.2447H25.0381V18.9001C25.0381 20.031 24.1213 20.9478 22.9905 20.9478H16.3291C16.3573 22.1713 16.6658 23.4675 17.0489 24.2924H18.445C19.2106 24.2924 19.8371 24.9188 19.8371 25.6844C19.8371 26.4501 19.2106 27.0765 18.445 27.0765H7.51834C6.7527 27.0765 6.1263 26.4501 6.1263 25.6844C6.1263 24.9188 6.7527 24.2924 7.51834 24.2924H8.91064C9.28398 23.4669 9.56552 22.1708 9.58199 20.9478ZM14.5982 4.17543H2.97292C2.25011 4.17543 1.665 4.76054 1.665 5.48329V16.869H4.94656C4.9103 16.614 4.96382 16.3456 5.11661 16.1123C5.2397 15.9238 5.38958 15.7987 5.57386 15.7279C5.705 15.6775 5.86043 15.6551 6.04832 15.6712L6.04806 12.9059H4.46273C4.25806 12.9059 4.09215 12.7399 4.09215 12.5353V9.13269C4.09215 8.92802 4.25806 8.7621 4.46273 8.7621H10.2553C10.46 8.7621 10.6259 8.92802 10.6259 9.13269V12.5353C10.6259 12.7399 10.46 12.9059 10.2553 12.9059H7.99762L7.99666 13.7268C8.07301 13.7065 8.15263 13.6956 8.23421 13.6957C8.46175 13.6958 8.67177 13.775 8.83816 13.907C9.00482 13.7746 9.21542 13.6955 9.44349 13.6957C9.6713 13.6958 9.88137 13.7749 10.0477 13.9069C10.2143 13.7746 10.4248 13.6955 10.6528 13.6957C11.1893 13.6959 11.6274 14.1344 11.6278 14.6699L11.6426 16.4877V16.4918C11.6422 16.6191 11.6333 16.745 11.6161 16.869H24.2969V13.2447H15.9524C15.208 13.2447 14.5982 12.6349 14.5982 11.8906V4.17543ZM1.665 17.6102V18.9001C1.665 19.6217 2.25022 20.2066 2.97292 20.2066H22.9905C23.712 20.2066 24.2969 19.6217 24.2969 18.9001V17.6102H11.408C10.9681 18.6228 9.95136 19.3522 8.71476 19.2813C6.94165 19.1798 6.85594 18.6694 5.39736 17.6102H1.665ZM16.5903 2.79663H15.9524C15.6154 2.79663 15.3394 3.07225 15.3394 3.40964V4.23547H26.3356V3.40964C26.3356 3.07225 26.0596 2.79663 25.7226 2.79663H25.0846V3.53649C25.0846 3.74116 24.9187 3.90708 24.714 3.90708H23.4497C23.245 3.90708 23.0791 3.74116 23.0791 3.53649V2.79663H21.8406V3.53649C21.8406 3.74116 21.6747 3.90708 21.47 3.90708H20.205C20.0003 3.90708 19.8344 3.74116 19.8344 3.53649V2.79663H18.5959V3.53649C18.5959 3.74116 18.4299 3.90708 18.2253 3.90708H16.9609C16.7563 3.90708 16.5903 3.74116 16.5903 3.53649V2.79663ZM15.3394 4.97665V11.8906C15.3394 12.2275 15.6154 12.5036 15.9524 12.5036H25.7226C26.0596 12.5036 26.3356 12.2275 26.3356 11.8906V4.97665H15.3394ZM16.2441 24.2924C15.8884 23.3919 15.6143 22.1378 15.5879 20.9478H10.323C10.3071 22.1382 10.0554 23.3922 9.71175 24.2924H16.2441ZM18.445 25.0336H7.51834C7.16035 25.0336 6.86748 25.3264 6.86748 25.6844C6.86748 26.0424 7.16035 26.3353 7.51834 26.3353H18.445C18.803 26.3353 19.0959 26.0424 19.0959 25.6844C19.0959 25.3264 18.803 25.0336 18.445 25.0336ZM6.78908 11.4386L6.78955 16.1304C6.78955 16.2495 6.73238 16.3613 6.63581 16.4309C6.53925 16.5006 6.41515 16.5196 6.30212 16.4821C6.00708 16.3841 5.84386 16.3541 5.73703 16.5177C5.63083 16.6797 5.67223 16.8938 5.82862 17.0074C7.12853 17.9506 7.17973 18.4511 8.75716 18.5413C9.97455 18.6112 10.8973 17.6195 10.9014 16.4915L10.8867 14.6745V14.671C10.8868 14.5423 10.7815 14.4369 10.6526 14.4369C10.5243 14.4368 10.4187 14.5414 10.4185 14.6698V14.6716C10.4182 14.876 10.2524 15.0416 10.0479 15.0416C9.84346 15.0416 9.6776 14.876 9.67733 14.6716V14.6705C9.67712 14.5424 9.57182 14.4369 9.44323 14.4369C9.315 14.4368 9.20933 14.5414 9.20918 14.6698V14.6716C9.20886 14.876 9.04305 15.0416 8.83859 15.0416C8.63413 15.0416 8.46826 14.876 8.468 14.6716V14.6705C8.46779 14.5424 8.36233 14.4369 8.23389 14.4369C8.10244 14.4368 7.9964 14.5558 7.99555 14.6872V14.6899V14.692C7.99529 14.8962 7.82979 15.0618 7.62555 15.0621C7.42125 15.0624 7.25522 14.8974 7.25438 14.6931L7.25819 11.4395C7.25813 11.3106 7.15278 11.2049 7.02382 11.2045C6.89501 11.2047 6.78908 11.3098 6.78908 11.4386ZM7.99852 12.1647H9.8847V9.50328H4.83332V12.1647H6.04795L6.0479 11.4386C6.0479 10.9023 6.48773 10.4634 7.02408 10.4634C7.56032 10.4642 7.99936 10.9034 7.99936 11.4396L7.99852 12.1647ZM17.8547 1.6647H17.3315V3.1659H17.8547V1.6647ZM20.5755 1.6647V3.1659H21.0994V1.6647H20.5755ZM12.0582 19.279C11.8537 19.279 11.6876 19.1129 11.6876 18.9084C11.6876 18.7039 11.8537 18.5378 12.0582 18.5378H13.9037C14.1082 18.5378 14.2743 18.7039 14.2743 18.9084C14.2743 19.1129 14.1082 19.279 13.9037 19.279H12.0582ZM23.8203 1.6647V3.1659H24.3434V1.6647H23.8203Z'
        fill='black'
      />
      <path
        d='M24.1718 8.30814C24.1718 7.7284 24.0109 7.18569 23.7317 6.72185L24.7785 5.67506C24.9329 5.52057 24.9329 5.27023 24.7785 5.11579C24.6241 4.9614 24.3737 4.9614 24.2193 5.11579L23.2368 6.0983C22.6805 5.55674 21.9217 5.22224 21.0859 5.22224C19.3843 5.22224 18 6.60659 18 8.30814C18 10.0097 19.3843 11.3941 21.0859 11.3941C22.7875 11.3941 24.1718 10.0097 24.1718 8.30814ZM18.7909 8.30814C18.7909 7.04264 19.8204 6.01315 21.0859 6.01315C21.7036 6.01315 22.2648 6.25891 22.6778 6.65726L20.8958 8.43927L20.5593 8.17031C20.3886 8.03385 20.1398 8.06174 20.0035 8.23231C19.8671 8.40289 19.8948 8.65176 20.0655 8.78811L20.6783 9.27794C20.7509 9.33594 20.8381 9.36452 20.9251 9.36452C21.0268 9.36452 21.1281 9.32545 21.2048 9.24873L23.1493 7.30427C23.2975 7.60771 23.381 7.94833 23.381 8.30814C23.381 9.57363 22.3514 10.6032 21.0859 10.6032C19.8204 10.6032 18.7909 9.57363 18.7909 8.30814Z'
        fill='#FF5B00'
      />
    </g>
    <defs>
      <clipPath id='clip0_2045_216'>
        <rect
          width='27'
          height='27'
          fill='white'
          transform='translate(0.5 0.5)'
        />
      </clipPath>
    </defs>
  </svg>
)

export default TripBasedHiresIcon
