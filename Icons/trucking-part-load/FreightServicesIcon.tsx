import * as React from "react"

const FreightServicesIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width='28'
    height='28'
    viewBox='0 0 28 28'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <path
      d='M22.7519 21.9732C22.5325 21.9732 22.3549 21.7955 22.3549 21.5762V7.11126H21.2502V21.5762C21.2502 21.7955 21.0726 21.9732 20.8531 21.9732C20.6336 21.9732 20.4561 21.7955 20.4561 21.5762V6.7142C20.4561 6.49492 20.6336 6.31714 20.8531 6.31714H22.7519C22.9714 6.31714 23.149 6.49492 23.149 6.7142V21.5762C23.149 21.7955 22.9714 21.9732 22.7519 21.9732Z'
      fill='black'
    />
    <path
      d='M19.7468 5.77477H9.90878C9.68931 5.77477 9.51172 5.59698 9.51172 5.37771V3.48373C9.51172 3.26445 9.68931 3.08667 9.90878 3.08667H19.7468C19.9663 3.08667 20.1439 3.26445 20.1439 3.48373V5.37771C20.1439 5.59698 19.9663 5.77477 19.7468 5.77477ZM10.3058 4.98065H19.3498V3.88079H10.3058V4.98065Z'
      fill='black'
    />
    <path
      d='M23.0746 7.11108H20.5317C20.3122 7.11108 20.1346 6.93329 20.1346 6.71402V6.45888H19.7476C19.5282 6.45888 19.3506 6.28109 19.3506 6.06182V2.80392C19.3506 2.58464 19.5282 2.40686 19.7476 2.40686H23.8551C24.0746 2.40686 24.2522 2.58464 24.2522 2.80392V6.06182C24.2522 6.28109 24.0746 6.45888 23.8551 6.45888H23.4716V6.71402C23.4716 6.93329 23.294 7.11108 23.0746 7.11108ZM20.9287 6.31696H22.6775V6.06182C22.6775 5.84254 22.8551 5.66476 23.0746 5.66476H23.4581V3.20098H20.1447V5.66476H20.5317C20.7511 5.66476 20.9287 5.84254 20.9287 6.06182V6.31696Z'
      fill='black'
    />
    <path
      d='M14.4814 7.0097H10.7799C10.5604 7.0097 10.3828 6.83192 10.3828 6.61264V5.37765C10.3828 5.15838 10.5604 4.98059 10.7799 4.98059H14.4814C14.7008 4.98059 14.8784 5.15838 14.8784 5.37765V6.61264C14.8784 6.83192 14.7008 7.0097 14.4814 7.0097ZM11.1769 6.21558H14.0843V5.77471H11.1769V6.21558Z'
      fill='black'
    />
    <path
      d='M13.1765 8.24469H12.0846C11.8651 8.24469 11.6875 8.0669 11.6875 7.84763V6.61263C11.6875 6.39336 11.8651 6.21558 12.0846 6.21558H13.1765C13.3959 6.21558 13.5735 6.39336 13.5735 6.61263V7.84763C13.5735 8.0669 13.3959 8.24469 13.1765 8.24469ZM12.4816 7.45057H12.7794V7.00969H12.4816V7.45057Z'
      fill='black'
    />
    <path
      d='M12.6325 10.7416C12.0598 10.7416 11.5938 10.2771 11.5938 9.70612C11.5938 9.35986 11.7659 9.03744 12.054 8.84376C12.1692 8.76621 12.2355 8.65318 12.2355 8.53356V7.84762C12.2355 7.62835 12.4131 7.45056 12.6325 7.45056C12.852 7.45056 13.0296 7.62835 13.0296 7.84762V8.53356C13.0296 8.91607 12.8307 9.27843 12.4972 9.50274C12.4286 9.54869 12.3879 9.62469 12.3879 9.70612C12.3879 9.83699 12.4999 9.9475 12.6325 9.9475C12.7632 9.9475 12.8737 9.83699 12.8737 9.70612C12.8737 9.48685 13.0513 9.30906 13.2708 9.30906C13.4902 9.30906 13.6678 9.48685 13.6678 9.70612C13.6678 10.2771 13.2033 10.7416 12.6325 10.7416Z'
      fill='black'
    />
    <path
      d='M19.3226 5.77457C19.221 5.77457 19.1195 5.7358 19.0419 5.65825L17.4362 4.05256L15.8364 5.65282C15.6875 5.80171 15.4238 5.80171 15.2749 5.65282L13.6801 4.05799L12.0817 5.6565C11.9328 5.8054 11.6692 5.8054 11.5203 5.6565L9.62804 3.76446C9.47294 3.60936 9.47294 3.3581 9.62804 3.203C9.78315 3.04789 10.0344 3.04789 10.1895 3.203L11.801 4.8143L13.3993 3.21579C13.5482 3.06689 13.8119 3.06689 13.9608 3.21579L15.5556 4.81062L17.1555 3.21036C17.3044 3.06147 17.5681 3.06147 17.717 3.21036L19.6034 5.09678C19.7585 5.25188 19.7585 5.50315 19.6034 5.65825C19.5258 5.7358 19.4242 5.77457 19.3226 5.77457Z'
      fill='black'
    />
    <path
      d='M20.8707 21.1727C20.7722 21.1727 20.6741 21.1364 20.5969 21.0633C20.4383 20.9123 20.4318 20.661 20.583 20.502L22.2057 18.7953L20.583 17.0885C20.4372 16.9353 20.4372 16.6945 20.583 16.5413L22.2057 14.8344L20.583 13.1276C20.4372 12.9744 20.4372 12.7336 20.583 12.5804L22.2057 10.8736L20.583 9.16686C20.4372 9.01369 20.4372 8.7729 20.583 8.61974L22.464 6.64123C22.6133 6.48419 22.8634 6.47391 23.0231 6.62145C23.1825 6.7688 23.1957 7.01502 23.0502 7.17652L21.4186 8.8933L23.0216 10.5794C23.0623 10.6167 23.0956 10.6624 23.1181 10.715C23.1809 10.8606 23.1526 11.0296 23.0433 11.145L21.4186 12.854L23.0216 14.5401C23.0611 14.5762 23.0937 14.6204 23.1162 14.671C23.1798 14.8143 23.1553 14.9816 23.0502 15.0981L21.4186 16.8149L23.0216 18.501C23.0623 18.5383 23.0956 18.584 23.1181 18.6366C23.1809 18.7822 23.1526 18.9512 23.0433 19.0666L21.1584 21.0492C21.0805 21.1314 20.9758 21.1727 20.8707 21.1727Z'
      fill='black'
    />
    <path
      d='M23.6436 25.5931H4.85157C4.70539 25.5931 4.57123 25.5129 4.50182 25.3841L1.14311 19.1417C1.07021 19.0064 1.08223 18.8412 1.17335 18.7177C1.26486 18.594 1.41996 18.5347 1.57002 18.5642L8.00633 19.8387C8.12653 19.8625 8.2289 19.9405 8.28396 20.0498L9.05133 21.5762H22.7879L26.3284 19.7846C26.4831 19.7053 26.6739 19.7379 26.7949 19.8647C26.9155 19.9913 26.9391 20.1822 26.8531 20.3344L23.9891 25.3917C23.9185 25.5162 23.7867 25.5931 23.6436 25.5931ZM5.08888 24.799H23.4121L25.5149 21.0863L23.0616 22.3275C23.0062 22.3556 22.9449 22.3703 22.8825 22.3703H8.80665C8.65659 22.3703 8.51933 22.2858 8.45186 22.1516L7.66162 20.5801L2.24122 19.5066L5.08888 24.799Z'
      fill='black'
    />
    <path
      d='M16.8217 18.2821H8.43807C8.21861 18.2821 8.04102 18.1043 8.04102 17.885V13.3877C8.04102 13.1684 8.21861 12.9906 8.43807 12.9906H16.8217C17.0411 12.9906 17.2187 13.1684 17.2187 13.3877V17.885C17.2187 18.1043 17.0411 18.2821 16.8217 18.2821ZM8.83513 17.488H16.4246V13.7847H8.83513V17.488Z'
      fill='black'
    />
    <path
      d='M16.8212 13.7839H8.43917C8.27205 13.7839 8.12277 13.6793 8.06577 13.522C8.00877 13.3646 8.05646 13.1887 8.1852 13.0817L11.9805 9.9231C12.359 9.60863 12.9065 9.60902 13.2841 9.92348L17.0752 13.0817C17.2039 13.1887 17.2516 13.3648 17.1946 13.522C17.1376 13.6793 16.9883 13.7839 16.8212 13.7839ZM9.5369 12.9898H15.7243L12.7758 10.5336C12.6924 10.4644 12.5718 10.4638 12.4885 10.5334L9.5369 12.9898Z'
      fill='black'
    />
    <path
      d='M15.6305 17.0909C15.411 17.0909 15.2334 16.9131 15.2334 16.6938V14.579C15.2334 14.3597 15.411 14.1819 15.6305 14.1819C15.8499 14.1819 16.0275 14.3597 16.0275 14.579V16.6938C16.0275 16.9131 15.8499 17.0909 15.6305 17.0909Z'
      fill='#FF5B00'
    />
    <path
      d='M9.62948 17.0909C9.41001 17.0909 9.23242 16.9131 9.23242 16.6938V14.579C9.23242 14.3597 9.41001 14.1819 9.62948 14.1819C9.84895 14.1819 10.0265 14.3597 10.0265 14.579V16.6938C10.0265 16.9131 9.84895 17.0909 9.62948 17.0909Z'
      fill='#FF5B00'
    />
    <path
      d='M11.1295 17.0909C10.91 17.0909 10.7324 16.9131 10.7324 16.6938V14.579C10.7324 14.3597 10.91 14.1819 11.1295 14.1819C11.3489 14.1819 11.5265 14.3597 11.5265 14.579V16.6938C11.5265 16.9131 11.3489 17.0909 11.1295 17.0909Z'
      fill='#FF5B00'
    />
    <path
      d='M12.6295 17.0909C12.41 17.0909 12.2324 16.9131 12.2324 16.6938V14.579C12.2324 14.3597 12.41 14.1819 12.6295 14.1819C12.8489 14.1819 13.0265 14.3597 13.0265 14.579V16.6938C13.0265 16.9131 12.8489 17.0909 12.6295 17.0909Z'
      fill='#FF5B00'
    />
    <path
      d='M14.1295 17.0908C13.91 17.0908 13.7324 16.913 13.7324 16.6937V14.5789C13.7324 14.3597 13.91 14.1819 14.1295 14.1819C14.3489 14.1819 14.5265 14.3597 14.5265 14.5789V16.6937C14.5265 16.913 14.3489 17.0908 14.1295 17.0908Z'
      fill='#FF5B00'
    />
    <path
      d='M7.22504 24.3162C6.821 24.3162 6.49219 23.9874 6.49219 23.5832C6.49219 23.1792 6.821 22.8503 7.22504 22.8503C7.62908 22.8503 7.95789 23.1792 7.95789 23.5832C7.95789 23.9874 7.62908 24.3162 7.22504 24.3162ZM7.22504 23.2474C7.03969 23.2474 6.88925 23.398 6.88925 23.5832C6.88925 23.7685 7.03969 23.9192 7.22504 23.9192C7.41038 23.9192 7.56083 23.7685 7.56083 23.5832C7.56083 23.398 7.41038 23.2474 7.22504 23.2474Z'
      fill='#FF5B00'
    />
    <path
      d='M10.0141 24.3162C9.61006 24.3162 9.28125 23.9874 9.28125 23.5832C9.28125 23.1792 9.61006 22.8503 10.0141 22.8503C10.4181 22.8503 10.747 23.1792 10.747 23.5832C10.747 23.9874 10.4181 24.3162 10.0141 24.3162ZM10.0141 23.2474C9.82876 23.2474 9.67831 23.398 9.67831 23.5832C9.67831 23.7685 9.82876 23.9192 10.0141 23.9192C10.1994 23.9192 10.3499 23.7685 10.3499 23.5832C10.3499 23.398 10.1994 23.2474 10.0141 23.2474Z'
      fill='#FF5B00'
    />
    <path
      d='M12.8041 24.3162C12.4001 24.3162 12.0713 23.9874 12.0713 23.5832C12.0713 23.1792 12.4001 22.8503 12.8041 22.8503C13.2082 22.8503 13.537 23.1792 13.537 23.5832C13.537 23.9874 13.2082 24.3162 12.8041 24.3162ZM12.8041 23.2474C12.6188 23.2474 12.4683 23.398 12.4683 23.5832C12.4683 23.7685 12.6188 23.9192 12.8041 23.9192C12.9895 23.9192 13.1399 23.7685 13.1399 23.5832C13.1399 23.398 12.9895 23.2474 12.8041 23.2474Z'
      fill='#FF5B00'
    />
    <path
      d='M15.5932 24.3162C15.1892 24.3162 14.8604 23.9874 14.8604 23.5832C14.8604 23.1792 15.1892 22.8503 15.5932 22.8503C15.9972 22.8503 16.3261 23.1792 16.3261 23.5832C16.3261 23.9874 15.9972 24.3162 15.5932 24.3162ZM15.5932 23.2474C15.4079 23.2474 15.2574 23.398 15.2574 23.5832C15.2574 23.7685 15.4079 23.9192 15.5932 23.9192C15.7786 23.9192 15.929 23.7685 15.929 23.5832C15.929 23.398 15.7786 23.2474 15.5932 23.2474Z'
      fill='#FF5B00'
    />
    <path
      d='M18.3832 24.3162C17.9792 24.3162 17.6504 23.9874 17.6504 23.5832C17.6504 23.1792 17.9792 22.8503 18.3832 22.8503C18.7873 22.8503 19.1161 23.1792 19.1161 23.5832C19.1161 23.9874 18.7873 24.3162 18.3832 24.3162ZM18.3832 23.2474C18.1979 23.2474 18.0474 23.398 18.0474 23.5832C18.0474 23.7685 18.1979 23.9192 18.3832 23.9192C18.5686 23.9192 18.719 23.7685 18.719 23.5832C18.719 23.398 18.5686 23.2474 18.3832 23.2474Z'
      fill='#FF5B00'
    />
    <path
      d='M21.1723 24.3162C20.7683 24.3162 20.4395 23.9874 20.4395 23.5832C20.4395 23.1792 20.7683 22.8503 21.1723 22.8503C21.5763 22.8503 21.9052 23.1792 21.9052 23.5832C21.9052 23.9874 21.5763 24.3162 21.1723 24.3162ZM21.1723 23.2474C20.987 23.2474 20.8365 23.398 20.8365 23.5832C20.8365 23.7685 20.987 23.9192 21.1723 23.9192C21.3577 23.9192 21.5081 23.7685 21.5081 23.5832C21.5081 23.398 21.3577 23.2474 21.1723 23.2474Z'
      fill='#FF5B00'
    />
  </svg>
)

export default FreightServicesIcon
