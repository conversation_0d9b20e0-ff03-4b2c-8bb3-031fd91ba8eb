import * as React from "react"

const CostEffectiveSolutionsIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width='27'
    height='27'
    viewBox='0 0 27 27'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <g clipPath='url(#clip0_2045_370)'>
      <path
        d='M16.6637 5.27388C16.3729 5.27388 16.1363 5.03732 16.1363 4.74654C16.1363 4.45576 16.3729 4.2192 16.6637 4.2192C16.8505 4.2192 17.0478 4.31449 17.234 4.49484C17.4433 4.69739 17.7771 4.69191 17.9796 4.48255C18.1821 4.27325 18.1766 3.93944 17.9673 3.73694C17.6997 3.47797 17.4319 3.33152 17.1909 3.25152V2.63728C17.1909 2.34602 16.9548 2.10999 16.6636 2.10999C16.3723 2.10999 16.1362 2.34608 16.1362 2.63728V3.25527C15.5225 3.47296 15.0816 4.05904 15.0816 4.74654C15.0816 5.61882 15.7913 6.32846 16.6635 6.32846C16.9543 6.32846 17.1909 6.56503 17.1909 6.85581C17.1909 7.14658 16.9543 7.38315 16.6635 7.38315C16.4383 7.38315 16.195 7.24145 15.9788 6.98421C15.7914 6.76125 15.4588 6.73246 15.2359 6.91993C15.0129 7.10735 14.9842 7.44 15.1716 7.66285C15.4535 7.99819 15.7849 8.2318 16.1362 8.34961V8.96502C16.1362 9.25627 16.3723 9.49231 16.6636 9.49231C16.9548 9.49231 17.1909 9.25622 17.1909 8.96502V8.34703C17.8046 8.12934 18.2455 7.54325 18.2455 6.85575C18.2456 5.98353 17.536 5.27388 16.6637 5.27388Z'
        fill='#FF5B00'
      />
      <path
        d='M16.6637 0.000732422C13.4653 0.000732422 10.8633 2.60285 10.8633 5.80119C10.8633 8.99952 13.4654 11.6016 16.6637 11.6016C19.8621 11.6016 22.4642 8.99952 22.4642 5.80119C22.4642 2.60285 19.8621 0.000732422 16.6637 0.000732422ZM16.6637 10.547C14.0469 10.547 11.9179 8.41802 11.9179 5.80119C11.9179 3.18435 14.0469 1.05537 16.6637 1.05537C19.2806 1.05537 21.4096 3.18435 21.4096 5.80119C21.4096 8.41802 19.2806 10.547 16.6637 10.547Z'
        fill='black'
      />
      <path
        d='M25.0052 11.7456C24.1977 11.7142 23.443 12.1614 23.0816 12.884C22.4237 14.1994 21.8005 14.8761 20.6012 15.4966C20.2487 14.8088 19.53 14.3438 18.7224 14.3438H13.4387C13.1018 14.3438 12.8297 14.2003 12.4529 14.0019C11.8502 13.6843 11.1001 13.2891 9.74751 13.2891C7.33122 13.2891 4.98376 15.6602 4.00907 17.3212L3.8833 17.1954C3.67742 16.9896 3.34346 16.9896 3.13753 17.1954L0.154459 20.1785C0.0555292 20.2774 -5.26968e-05 20.4117 3.74901e-08 20.5516C5.27718e-05 20.6916 0.0557929 20.8258 0.154881 20.9246L6.08875 26.8453C6.29426 27.0504 6.62722 27.0509 6.8332 26.8458L9.81622 23.8759C10.0228 23.6702 10.0233 23.3356 9.81707 23.1294L9.33413 22.6464C9.6953 22.1356 10.6157 21.7262 11.2677 21.7262C11.2679 21.7262 11.2683 21.7262 11.2685 21.7262L18.3865 21.7283C18.3876 21.7283 18.3887 21.7283 18.3898 21.7283C19.3101 21.7283 20.1929 21.4732 20.9428 20.9907C22.8663 19.7537 25.5717 17.7407 26.8525 14.5535C27.3855 13.2265 26.4061 11.8004 25.0052 11.7456ZM6.46174 25.7276L1.27359 20.551L3.51047 18.3141C4.75732 19.5609 6.07388 20.8778 8.69783 23.5015L6.46174 25.7276ZM25.874 14.1604C24.7056 17.0679 22.1747 18.9447 20.3723 20.1037C19.7929 20.4766 19.1074 20.6736 18.3897 20.6736C18.3889 20.6736 18.388 20.6736 18.3872 20.6736L11.2691 20.6715C11.2686 20.6715 11.2682 20.6715 11.2676 20.6715C10.3466 20.6715 9.20577 21.1496 8.58435 21.8965L4.78564 18.0978C5.20398 17.2378 7.3906 14.3438 9.74751 14.3438C10.8393 14.3438 11.4097 14.6443 11.9613 14.9349C12.3939 15.1627 12.8411 15.3984 13.4387 15.3984H18.7225C19.2457 15.3984 19.6944 15.7879 19.7668 16.3078C19.8572 16.9257 19.3721 17.5077 18.7225 17.5077H13.4387C13.1474 17.5077 12.9114 17.7438 12.9114 18.035C12.9114 18.3262 13.1474 18.5623 13.4387 18.5623H18.7224C19.8491 18.5623 20.7722 17.6744 20.8289 16.5615C22.3921 15.8042 23.2227 14.9595 24.0248 13.3557C24.2014 13.0025 24.5699 12.7843 24.9642 12.7994C25.6581 12.8266 26.1277 13.5287 25.874 14.1604Z'
        fill='black'
      />
      <path
        d='M6.49388 22.7879L5.74774 22.0423C5.54176 21.8365 5.2079 21.8367 5.00197 22.0426C4.7961 22.2486 4.79625 22.5824 5.00223 22.7884L5.74837 23.534C5.9543 23.7398 6.28811 23.7397 6.49414 23.5337C6.70001 23.3277 6.69986 22.9938 6.49388 22.7879Z'
        fill='black'
      />
    </g>
    <defs>
      <clipPath id='clip0_2045_370'>
        <rect width='27' height='27' fill='white' />
      </clipPath>
    </defs>
  </svg>
)

export default CostEffectiveSolutionsIcon
