import React from "react"

const TaskIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='40'
    height='40'
    viewBox='0 0 40 40'
    fill='none'
    {...props}
  >
    <path
      fillRule='evenodd'
      clipRule='evenodd'
      d='M13.7507 6.45825C14.3259 6.45825 14.7923 6.92463 14.7923 7.49992L19.501 11L18.654 17.1799C19.0606 17.5868 19.0606 18.2464 18.654 18.6533C18.2471 19.0599 17.5875 19.0599 17.1807 18.6533L13.0141 14.4865C12.8187 14.2911 12.709 14.0262 12.709 13.7499V7.49992C12.709 6.92463 13.1754 6.45825 13.7507 6.45825Z'
      fill='#FF5B00'
    />
    <path
      fillRule='evenodd'
      clipRule='evenodd'
      d='M0.208984 9.58325C0.208984 4.40559 4.40632 0.208252 9.58398 0.208252H30.4173C35.595 0.208252 39.7923 4.40559 39.7923 9.58325V30.4166C39.7923 35.5943 35.595 39.7916 30.4173 39.7916H9.58398C4.40632 39.7916 0.208984 35.5943 0.208984 30.4166V9.58325ZM9.58398 2.29159C5.5569 2.29159 2.29232 5.55617 2.29232 9.58325V30.4166C2.29232 34.4437 5.5569 37.7083 9.58398 37.7083H30.4173C34.4444 37.7083 37.709 34.4437 37.709 30.4166V9.58325C37.709 5.55617 34.4444 2.29159 30.4173 2.29159H9.58398Z'
      fill='black'
    />
    <path
      fillRule='evenodd'
      clipRule='evenodd'
      d='M8.54232 32.4999C8.54232 31.9247 9.00869 31.4583 9.58398 31.4583H30.4173C30.9925 31.4583 31.459 31.9247 31.459 32.4999C31.459 33.0751 30.9925 33.5416 30.4173 33.5416H9.58398C9.00869 33.5416 8.54232 33.0751 8.54232 32.4999ZM8.54232 26.2499C8.54232 25.6747 9.00869 25.2083 9.58398 25.2083H30.4173C30.9925 25.2083 31.459 25.6747 31.459 26.2499C31.459 26.8251 30.9925 27.2916 30.4173 27.2916H9.58398C9.00869 27.2916 8.54232 26.8251 8.54232 26.2499ZM25.209 13.7499C25.209 13.1746 25.6754 12.7083 26.2507 12.7083H32.5007C33.0759 12.7083 33.5423 13.1746 33.5423 13.7499C33.5423 14.3252 33.0759 14.7916 32.5007 14.7916H26.2507C25.6754 14.7916 25.209 14.3252 25.209 13.7499ZM13.7507 8.54159C10.8742 8.54159 8.54232 10.8734 8.54232 13.7499C8.54232 16.6264 10.8742 18.9583 13.7507 18.9583C16.6271 18.9583 18.959 16.6264 18.959 13.7499C18.959 10.8734 16.6271 8.54159 13.7507 8.54159ZM6.45898 13.7499C6.45898 9.72284 9.72357 6.45825 13.7507 6.45825C17.7777 6.45825 21.0423 9.72284 21.0423 13.7499C21.0423 17.777 17.7777 21.0416 13.7507 21.0416C9.72357 21.0416 6.45898 17.777 6.45898 13.7499Z'
      fill='black'
    />
  </svg>
)

export default TaskIcon
