import React from "react"

const QuoteOpenIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='80'
    height='60'
    viewBox='0 0 80 60'
    fill='none'
    {...props}
  >
    <path
      d='M25 10L30 0H20C8.95 0 0 13.95 0 25V60H35V25H15C15 10 25 10 25 10ZM60 25C60 10 70 10 70 10L75 0H65C53.95 0 45 13.95 45 25V60H80V25H60Z'
      fill='url(#paint0_linear_855_3433)'
    />
    <defs>
      <linearGradient
        id='paint0_linear_855_3433'
        x1='40'
        y1='0'
        x2='40'
        y2='60'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#FF5B00' />
        <stop offset='1' stopColor='#D32E0E' />
      </linearGradient>
    </defs>
  </svg>
)

export default QuoteOpenIcon
