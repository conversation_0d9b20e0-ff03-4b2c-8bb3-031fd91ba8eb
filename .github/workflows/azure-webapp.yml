name: Deploy Next.js App to Azure Web App

on:
  push:
    branches:
      - main # Change if your default branch is different

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"

      - name: Set up pnpm
        uses: pnpm/action-setup@v3
        with:
          version: 8

      - name: Install dependencies
        run: pnpm install --no-frozen-lockfile

      - name: Build Next.js app
        env:
          SANITY_PROJECT_ID: ${{ secrets.SANITY_PROJECT_ID }}
          SANITY_DATASET: ${{ secrets.SANITY_DATASET }}
          NEXT_PUBLIC_GOOGLE_MAPS_API_KEY: ${{ secrets.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY }}
          TRACKING_API_KEY: ${{ secrets.TRACKING_API_KEY }}
          NEXT_PUBLIC_SERVER_URL: ${{ secrets.NEXT_PUBLIC_SERVER_URL }}
          FAST2SMS_API_KEY: ${{ secrets.NEXT_PUBLIC_FAST2SMS_API_KEY }}
          FAST2SMS_ROUTE: ${{ secrets.NEXT_PUBLIC_FAST2SMS_ROUTE }}
          NEXT_PUBLIC_EMAIL_FROM: ${{ secrets.NEXT_PUBLIC_EMAIL_FROM }}
          NEXT_PUBLIC_EMAIL_TO: ${{ secrets.NEXT_PUBLIC_EMAIL_TO }}
        run: pnpm run build

      - name: Copy static files
        run: |
          cp -r public .next/standalone/
          cp -r .next/static .next/standalone/.next/

      - name: Upload artifact for deployment job
        uses: actions/upload-artifact@v4
        with:
          name: app
          path: .next/standalone

  deploy:
    runs-on: ubuntu-latest
    needs: build

    steps:
      - name: Download artifact from build job
        uses: actions/download-artifact@v4
        with:
          name: app
          path: .

      - name: Login to Azure
        uses: azure/login@v2
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}

      - name: Deploy to Azure Web App
        uses: azure/webapps-deploy@v3
        with:
          app-name: nex-move-app
          package: .
