import type { NextConfig } from "next"

// Add bundle analyzer import
const withBundleAnalyzer = require("@next/bundle-analyzer")({
  enabled: process.env.ANALYZE === "true",
})

const nextConfig: NextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "cdn.sanity.io",
      },
      {
        protocol: "https",
        hostname: "i.ibb.co",
      },
      {
        protocol: "https",
        hostname: "example.com",
      },
      {
        protocol: "https",
        hostname: "cdn.jsdelivr.net",
      },
    ],
  },
  devIndicators: {
    buildActivity: false,
  },
}

// Wrap the config with bundle analyzer
export default withBundleAnalyzer(nextConfig)

// https://cdn.jsdelivr.net/gh/PRISM-SCALE/cdn/
