import { FlatCompat } from "@eslint/eslintrc"
import { dirname } from "path"
import { fileURLToPath } from "url"

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

const compat = new FlatCompat({
  baseDirectory: __dirname,
})

const eslintConfig = [
  // Global ignores
  {
    ignores: [
      // Dependencies
      "node_modules/**",
      ".pnp/**",
      ".pnp.js",

      // Production builds
      ".next/**",
      "out/**",
      "build/**",
      "dist/**",

      // Environment variables
      ".env*",

      // Logs
      "*.log",

      // Runtime data
      "pids/**",
      "*.pid",
      "*.seed",
      "*.pid.lock",

      // Coverage directory
      "coverage/**",

      // Dependency directories
      "jspm_packages/**",

      // Optional npm cache directory
      ".npm/**",

      // Optional eslint cache
      ".eslintcache",

      // Nuxt.js build / generate output
      ".nuxt/**",

      // Storybook build outputs
      ".out/**",
      ".storybook-out/**",

      // Temporary folders
      "tmp/**",
      "temp/**",

      // OS generated files
      ".DS_Store",
      "Thumbs.db",

      // Lock files
      "package-lock.json",
      "yarn.lock",
      "pnpm-lock.yaml",

      // Generated files
      "*.tsbuildinfo",

      // Public assets
      "public/**",

      // Sanity Studio
      "/studio/**",

      // Config files that don't need linting
      "*.config.js",
      "*.config.mjs",
      "*.config.ts",
      "tailwind.config.js",
      "postcss.config.js",
    ],
  },

  // Main configuration
  ...compat.extends("next", "next/core-web-vitals", "next/typescript"),
  ...compat.extends("prettier"),

  {
    rules: {
      // Custom rules for better code quality
      "@typescript-eslint/no-unused-vars": [
        "warn",
        {
          argsIgnorePattern: "^_",
          varsIgnorePattern: "^_",
          caughtErrorsIgnorePattern: "^_",
        },
      ],
      "@typescript-eslint/no-explicit-any": "warn",
      "prefer-const": "warn",
      "no-var": "warn",
      "no-console": ["warn", { allow: ["warn", "error"] }],

      // React specific rules
      "react/jsx-key": "error",
      "react/jsx-no-duplicate-props": "error",
      "react/jsx-no-undef": "error",
      "react/no-unescaped-entities": "off", // Next.js handles this
    },
  },
]

export default eslintConfig
