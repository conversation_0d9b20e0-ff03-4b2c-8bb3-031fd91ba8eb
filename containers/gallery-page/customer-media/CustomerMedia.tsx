import { media } from "@/config"
import { SectionTitle, SectionWrapper } from "@/app/shared/components"
import { Wrapper } from "@/app/shared/components/Wrapper"
import ScrollVelocity from "@/app/components/common/ScrollVelocity"

const CustomerMedia = () => {
  return (
    <SectionWrapper className='~pt-14/20'>
      <Wrapper className=''>
        <SectionTitle
          subtitle='REAL STORIES'
          title='Customer Photos & Videos'
        />
        <div>
          <div>
            <ScrollVelocity
              numCopies={2}
              items={[
                <div className='flex overflow-x-hidden ~gap-2/4'>
                  {media.slice(0, media.length / 2).map((item, index) => (
                    <div key={index} className='relative break-inside-avoid'>
                      <div
                        className={`relative mb-4 overflow-hidden rounded-xl ${item.type === "video" ? "aspect-video" : ""}`}
                      >
                        <img
                          src={item.src}
                          alt={item.alt}
                          className='h-60 w-full object-cover transition-transform duration-300 hover:scale-105'
                          loading='lazy'
                        />
                      </div>
                    </div>
                  ))}
                </div>,
                <div className='flex overflow-x-hidden ~mt-2/4 ~gap-2/4'>
                  {media.slice(media.length / 2).map((item, index) => (
                    <div key={index} className='relative break-inside-avoid'>
                      <div
                        className={`relative mb-4 overflow-hidden rounded-xl ${item.type === "video" ? "aspect-video" : ""}`}
                      >
                        <img
                          src={item.src}
                          alt={item.alt}
                          className='h-60 w-full object-cover transition-transform duration-300 hover:scale-105'
                          loading='lazy'
                        />
                      </div>
                    </div>
                  ))}
                </div>,
              ]}
            />
          </div>
        </div>
      </Wrapper>
    </SectionWrapper>
  )
}

export default CustomerMedia
