"use client"
import { Section<PERSON><PERSON><PERSON>, SectionWrapper } from "@/app/shared/components"
import { Wrapper } from "@/app/shared/components/Wrapper"

import { GALLERY_DATA, TGalleryCategories } from "@/config"
import { useState } from "react"
import GalleryCarouselBtn from "../gallery-section/GalleryCarouselBtn"
import GalleryItem from "../gallery-section/GalleryItem"

const FleetMedia = () => {
  const [tabs] = useState<(keyof TGalleryCategories)[]>(
    Object.keys(GALLERY_DATA) as (keyof TGalleryCategories)[],
  )
  const [activeTab, setActiveTab] =
    useState<keyof TGalleryCategories>("option_1")
  const [expandedIndex, setExpandedIndex] = useState<number>(0)

  return (
    <SectionWrapper className='~pt-14/20'>
      <Wrapper className='~pb-8/16'>
        <SectionTitle subtitle=' ' title='Fleet Photos' />
        <div>
          <ul className='flex overflow-hidden ~gap-1/2'>
            {GALLERY_DATA[activeTab].map((item, index) => (
              <GalleryItem
                key={index}
                item={item}
                index={index}
                activeIndex={expandedIndex}
                onSelect={() => setExpandedIndex(index)}
              />
            ))}
          </ul>
          <GalleryCarouselBtn
            setActiveIndex={setExpandedIndex}
            length={GALLERY_DATA[activeTab].length}
          />
        </div>
      </Wrapper>
    </SectionWrapper>
  )
}

export default FleetMedia
