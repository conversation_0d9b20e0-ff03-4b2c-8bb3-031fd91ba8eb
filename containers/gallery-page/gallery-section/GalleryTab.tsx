import { Button } from "@/app/components/ui"
import { TGalleryCategories } from "@/config"
import { cn, snakeToTitle } from "@/app/shared/utils"
import { Dispatch, SetStateAction } from "react"

type TProps = {
  tab: keyof TGalleryCategories
  setActiveTab: Dispatch<SetStateAction<keyof TGalleryCategories>>
  activeTab: string
}

const GalleryTab = ({ tab, setActiveTab, activeTab }: TProps) => {
  return (
    <Button
      key={tab}
      onClick={() => setActiveTab(tab)}
      className={cn(
        "rounded-lg border border-zinc-300 bg-white font-normal uppercase text-zinc-400 ~text-xs/sm",
        activeTab === tab && "bg-primary font-semibold text-white",
      )}
    >
      {snakeToTitle(tab)}
    </Button>
  )
}

export default GalleryTab
