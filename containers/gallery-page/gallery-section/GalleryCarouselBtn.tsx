import { Button } from "@/app/components/ui"
import { cn } from "@/app/shared/utils"
import { GlobalArrowLeftIcon, GlobalArrowRightIcon } from "@/Icons"
import { Dispatch, SetStateAction } from "react"

type TProps = {
  setActiveIndex: Dispatch<SetStateAction<number>>
  length: number
}

const GalleryCarouselBtn = ({ setActiveIndex, length }: TProps) => {
  return (
    <div className='flex justify-center ~mt-4/6 ~gap-2/4'>
      <Button
        className={cn(
          "pointer-events-auto rounded-full bg-gradient-to-r from-primary to-zinc-50 !p-[1px] ~size-10/12",
        )}
        onClick={() => setActiveIndex((prev) => (prev - 1 + length) % length)}
        size='sm'
      >
        <span className='grid size-full place-content-center rounded-full bg-zinc-50 text-black'>
          <GlobalArrowLeftIcon />
        </span>
      </Button>
      <Button
        className={cn(
          "pointer-events-auto rounded-full bg-gradient-to-r from-zinc-50 to-primary !p-[1px] ~size-10/12",
        )}
        onClick={() => setActiveIndex((prev) => (prev + 1) % length)}
        size='sm'
      >
        <span className='grid size-full place-content-center rounded-full bg-zinc-50 text-black'>
          <GlobalArrowRightIcon />
        </span>
      </Button>
    </div>
  )
}

export default GalleryCarouselBtn
