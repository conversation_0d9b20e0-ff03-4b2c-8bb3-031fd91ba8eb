"use client"

import { cn } from "@/app/shared/utils"
import { motion } from "framer-motion"

type GalleryItemProps = {
  item: { image: string; description: string }
  index: number
  activeIndex: number
  onSelect: () => void
}

export const GalleryItem = ({
  item,
  index,
  activeIndex,
  onSelect,
}: GalleryItemProps) => {
  const isActive = activeIndex === index

  return (
    <li
      className={cn(
        "cursor-pointer transition-all duration-500 ease-in-out ~h-80/96",
        isActive ? "flex-[0_0_50%] brightness-100" : "flex-1 brightness-75",
      )}
      onClick={onSelect}
    >
      <div
        className='flex h-full items-end bg-cover bg-center'
        style={{ backgroundImage: `url(${item.image})` }}
      >
        <div
          className='relative h-1/2 w-full'
          style={{
            backgroundImage: "linear-gradient(to top, #00000080, transparent)",
          }}
        >
          {isActive && (
            <motion.span
              className='absolute block font-medium !leading-tight text-white ~text-base/2xl ~bottom-4/6 ~left-4/6'
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, type: "tween" }}
            >
              {item.description}
            </motion.span>
          )}
        </div>
      </div>
    </li>
  )
}

export default GalleryItem
