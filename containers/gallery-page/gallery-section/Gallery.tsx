"use client"

import { SectionWrapper } from "@/app/shared/components"
import { GALLERY_DATA, TGalleryCategories } from "@/config"
import { useState } from "react"
import GalleryCarouselBtn from "./GalleryCarouselBtn"
import GalleryItem from "./GalleryItem"
import GalleryTab from "./GalleryTab"

const Gallery = () => {
  // Gallery category tabs
  const [tabs] = useState<(keyof TGalleryCategories)[]>(
    Object.keys(GALLERY_DATA) as (keyof TGalleryCategories)[],
  )
  const [activeTab, setActiveTab] =
    useState<keyof TGalleryCategories>("option_1")
  const [expandedIndex, setExpandedIndex] = useState<number>(0)

  return (
    <SectionWrapper>
      {/* Tabs */}
      <div className='flex flex-wrap justify-center ~mb-4/6 ~gap-2/3'>
        {tabs.map((tab) => (
          <GalleryTab
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            tab={tab}
            key={tab}
          />
        ))}
      </div>

      <ul className='flex overflow-hidden ~gap-1/2'>
        {GALLERY_DATA[activeTab].map((item, index) => (
          <GalleryItem
            key={index}
            item={item}
            index={index}
            activeIndex={expandedIndex}
            onSelect={() => setExpandedIndex(index)}
          />
        ))}
      </ul>
      <GalleryCarouselBtn
        setActiveIndex={setExpandedIndex}
        length={GALLERY_DATA[activeTab].length}
      />
    </SectionWrapper>
  )
}

export default Gallery
