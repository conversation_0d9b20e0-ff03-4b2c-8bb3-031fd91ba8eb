"use client"

import { SectionTitle } from "@/app/shared/components"
import { Wrapper } from "@/app/shared/components/Wrapper"
import { heroVideoPosterUrl, heroVideoUrl } from "@/config"
import { PauseIcon, PlayIcon, WavePatternIcon } from "@/Icons"
import { useRef, useState } from "react"

const PackingProcess = () => {
  const [isPlaying, setIsPlaying] = useState(false)
  const videoRef = useRef<HTMLVideoElement>(null)

  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause()
      } else {
        videoRef.current.play().catch((error) => {
          console.error("Video play failed:", error)
        })
      }
    }
  }

  return (
    <section className='relative isolate min-h-60 overflow-hidden bg-darkGray'>
      <span className='absolute inset-0 -z-10'>
        <WavePatternIcon className='w-full scale-y-75' />
      </span>
      <div className='text-white ~px-4/8 ~py-8/16'>
        <Wrapper>
          <SectionTitle
            subtitle=''
            title='Have a look at our packing process!'
          />
        </Wrapper>
        <div className='group relative aspect-[21/9] min-h-80 w-full bg-sky-400/50'>
          <video
            ref={videoRef}
            loop
            playsInline
            preload='metadata'
            className='h-full w-full bg-black/50 object-cover'
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
            poster={heroVideoPosterUrl}
          >
            <source src={heroVideoUrl} type='video/mp4' />
          </video>
          <button
            onClick={togglePlay}
            className='absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full bg-white/20 p-4 transition hover:bg-white/30 group-hover:opacity-100 sm:opacity-0'
          >
            {isPlaying ? (
              <PauseIcon className='text-white ~size-4/7' />
            ) : (
              <PlayIcon className='text-white ~size-4/7' />
            )}
          </button>
        </div>
      </div>
    </section>
  )
}

export default PackingProcess
