import { SectionTitle, SectionWrapper } from "@/app/shared/components"
import { Wrapper } from "@/app/shared/components/Wrapper"
import { photos } from "@/config"

const TeamInAction = () => {
  return (
    <SectionWrapper className='~pt-14/20'>
      <Wrapper className='~pb-8/16'>
        <SectionTitle subtitle='' title='Team In Action' />
        <div className='columns-1 gap-4 space-y-4 sm:columns-2 md:columns-3'>
          {photos.map((photo, index) => (
            <div
              key={index}
              className='break-inside-avoid overflow-hidden rounded-xl'
            >
              <img
                src={photo.src}
                alt={photo.alt}
                className='h-auto w-full rounded-xl object-cover transition-transform duration-300 hover:scale-105'
                loading='lazy'
              />
            </div>
          ))}
        </div>
      </Wrapper>
    </SectionWrapper>
  )
}

export default TeamInAction
