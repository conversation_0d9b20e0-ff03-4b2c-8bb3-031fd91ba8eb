"use client"

import { SectionTit<PERSON>, SectionWrapper } from "@/app/shared/components"
import { Wrapper } from "@/app/shared/components/Wrapper"
import { imageGroups } from "@/config"
import { useWindowSize } from "@/hooks"
import { motion } from "framer-motion"
import { useEffect, useState } from "react"

const CompanyMedia = () => {
  const { width } = useWindowSize()
  const [columns, setColumns] = useState<string[][]>([])
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    if (!isClient) return

    // Determine number of columns based on screen width
    const columnCount = width < 640 ? 1 : width < 768 ? 2 : 3

    // Create columns array
    const cols: string[][] = Array.from({ length: columnCount }, () => [])

    // Distribute images across columns
    imageGroups.forEach((image, index) => {
      cols[index % columnCount].push(image)
    })

    setColumns(cols)
  }, [width, isClient, imageGroups])

  if (!isClient) return null

  return (
    <SectionWrapper className='~pt-14/20'>
      <Wrapper className='~pb-8/16'>
        <SectionTitle
          subtitle=''
          title='Office, Packing Material & Warehouse Photos'
        />

        <motion.div
          className='grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3'
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {columns.map((column, columnIndex) => (
            <div key={columnIndex} className='flex flex-col gap-4'>
              {column.map((src, imgIdx) => (
                <motion.div
                  key={imgIdx}
                  className='overflow-hidden rounded-lg'
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: columnIndex * 0.1 + imgIdx * 0.05 }}
                >
                  <img
                    className='h-auto w-full object-cover transition-transform duration-300 hover:scale-105'
                    src={src}
                    alt={`Company photo ${columnIndex * 10 + imgIdx}`}
                    loading='lazy'
                  />
                </motion.div>
              ))}
            </div>
          ))}
        </motion.div>
      </Wrapper>
    </SectionWrapper>
  )
}

export default CompanyMedia
