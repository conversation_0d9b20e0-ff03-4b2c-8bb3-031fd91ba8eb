import { TextBanner } from "@/app/components/common"
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Section<PERSON><PERSON><PERSON>,
  SectionWrapper,
} from "@/app/shared/components"
import { SHIPPING_AND_DELIVERY_POLICY } from "@/config"

const ShippingAndDeliveryPolicy = () => {
  return (
    <>
      <PageHeader secondLine='SHIPPING & DELIVERY POLICY' />
      <SectionWrapper className='font-inter !leading-tight text-zinc-400 ~text-sm/lg'>
        <SectionTitle
          title='SHIPPING & DELIVERY POLICY'
          subtitle='LEGAL'
          className='text-black'
        />
        <p>{SHIPPING_AND_DELIVERY_POLICY.introduction}</p>
        <div className='~mt-5/7 ~space-y-5/7'>
          {SHIPPING_AND_DELIVERY_POLICY.data.map((data, index) => (
            <div key={index}>
              <p className='uppercase text-primary ~mb-3/5'>{data.title}</p>
              {data.contentList ? (
                <ul className='list-disc'>
                  {data.contentList.map((list, index) => (
                    <li className='ml-5' key={index}>
                      {list}
                    </li>
                  ))}
                </ul>
              ) : data.tableData ? (
                <div className='overflow-x-auto'>
                  <table className='w-full border-collapse border border-gray-300'>
                    <thead>
                      <tr className='bg-gray-100'>
                        {data.tableData.headers.map((header, index) => (
                          <th
                            key={index}
                            className='border border-gray-300 px-4 py-2 text-left font-semibold text-black'
                          >
                            {header}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      {data.tableData.rows.map((row, rowIndex) => (
                        <tr
                          key={rowIndex}
                          className={
                            rowIndex % 2 === 0 ? "bg-white" : "bg-gray-50"
                          }
                        >
                          {row.map((cell, cellIndex) => (
                            <td
                              key={cellIndex}
                              className='border border-gray-300 px-4 py-2 text-zinc-600'
                            >
                              {cell}
                            </td>
                          ))}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <p>{data.content}</p>
              )}
            </div>
          ))}
        </div>
      </SectionWrapper>
      <TextBanner
        text='Thank you for choosing Nex Move – your trusted partner in logistics and mobility solutions.'
        className='mx-auto max-w-screen-xl'
      />
    </>
  )
}

export default ShippingAndDeliveryPolicy
