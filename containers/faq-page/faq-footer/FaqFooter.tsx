import { Wrapper } from "@/app/shared/components/Wrapper"
import { SOCIAL_LINKS } from "@/config"
import Link from "next/link"

const FaqFooter = () => {
  return (
    <div className='relative ~py-5/8'>
      <Wrapper className='mx-auto max-w-screen-md text-center'>
        <p className='!leading-tight ~text-sm/lg'>
          Still Have Questions? If you couldn't find the answer you were looking
          for, please don't hesitate to{" "}
          <Link
            href='/contact'
            className='text-primary underline underline-offset-2'
            aria-label='Contact Us'
            title='Contact Us'
          >
            contact us
          </Link>
        </p>
        <div className='flex flex-wrap justify-center ~mt-5/6 ~gap-2/4'>
          {SOCIAL_LINKS.map((social, idx) => (
            <Link href={social.url} key={idx} target='_blank'>
              <social.icon
                aria-label={social.url + " Icon"}
                className='~size-5/6'
              />
            </Link>
          ))}
        </div>
      </Wrapper>

      <span className='absolute bottom-0 left-0 -z-10 h-[130%] w-full bg-[#ffefe6]'></span>
    </div>
  )
}

export default FaqFooter
