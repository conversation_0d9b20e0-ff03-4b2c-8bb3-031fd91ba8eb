import { cn } from "@/app/shared/utils"
import { TFaqEntry } from "@/config"
import { MinusIcon, PlusIcon } from "@/Icons"

type TFaqItemProps = {
  isExpanded: boolean
  onClick: () => void
  index: number
} & TFaqEntry

const FAQItem = ({ isExpanded, onClick, index, ...FAQ }: TFaqItemProps) => {
  return (
    <li className='border-b border-zinc-300 ~p-3/4 [&:last-child]:border-b-0'>
      <h3
        onClick={onClick}
        className={cn(
          "flex cursor-pointer select-none items-center justify-between gap-3 font-inter font-medium !leading-tight transition-colors sm:font-semibold sm:~text-base/lg",
          isExpanded && "text-primary",
        )}
      >
        <span>
          {index + 1}. {FAQ.question}
        </span>
        {isExpanded ? (
          <MinusIcon className='flex-shrink-0' />
        ) : (
          <PlusIcon className='flex-shrink-0 text-black' />
        )}
      </h3>
      <div
        className={cn(
          "grid grid-rows-[0fr] transition-all duration-300",
          isExpanded && "grid-rows-[1fr] ~mt-2/3",
        )}
      >
        <div className='overflow-hidden text-zinc-500 ~space-y-1/2'>
          <p>{FAQ.answer.introduction}</p>
          {FAQ.answer.list && FAQ.answer.list.length > 0 && (
            <ul>
              {FAQ.answer.list.map((step, idx) => (
                <li key={idx} className='list-inside list-disc'>
                  {step}
                </li>
              ))}
            </ul>
          )}
          {FAQ.answer.conclusion && <p>{FAQ.answer.conclusion}</p>}
        </div>
      </div>
    </li>
  )
}

export default FAQItem
