"use client"

import { <PERSON><PERSON> } from "@/app/components/ui"
import { Wrapper } from "@/app/shared/components/Wrapper"
import { cn, snakeToTitle } from "@/app/shared/utils"
import { FAQ_DATA, TFaqCategories } from "@/config"
import { useState } from "react"
import FAQItem from "./FAQItem"

const FAQ = () => {
  // faq category tabs
  const [tabs] = useState<(keyof TFaqCategories)[]>(
    Object.keys(FAQ_DATA) as (keyof TFaqCategories)[],
  )
  const [activeTab, setActiveTab] = useState<keyof TFaqCategories>("general")
  const [expandedIndex, setExpandedIndex] = useState<number | null>(0)
  const handleAccordionClick = (index: number) => {
    setExpandedIndex(expandedIndex === index ? null : index)
  }

  return (
    <Wrapper>
      <div className='flex flex-wrap ~gap-2/3'>
        {tabs.map((tab) => (
          <Button
            aria-label={"Learn more about " + tab}
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={cn(
              "rounded-lg border border-zinc-300 bg-white font-normal uppercase text-zinc-400 ~text-xs/sm",
              activeTab === tab && "bg-primary font-semibold text-white",
            )}
          >
            {snakeToTitle(tab)}
          </Button>
        ))}
      </div>
      <div>
        <ul className='rounded-xl bg-zinc-100 ~mt-4/6 ~p-5/8'>
          {FAQ_DATA[activeTab].map((faq, index) => (
            <FAQItem
              key={index}
              {...faq}
              isExpanded={expandedIndex === index}
              onClick={() => handleAccordionClick(index)}
              index={index}
            />
          ))}
        </ul>
      </div>
    </Wrapper>
  )
}

export default FAQ
