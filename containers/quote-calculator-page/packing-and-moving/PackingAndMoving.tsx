"use client"

import PackingAndMovingForm from "@/app/components/forms/PackingAndMovingForm"
import {
  SectionTitle,
  SectionWrapper,
  SplitView,
} from "@/app/shared/components"

export const PackingAndMoving = () => {
  const serviceName = "Packing and Moving"

  return (
    <main>
      <SectionWrapper>
        <SectionTitle title={serviceName} showBackButton />
        <SplitView
          image='/images/shipment.webp'
          className='[&>div:first-child_img]:object-[center_top]'
        >
          <PackingAndMovingForm />
        </SplitView>
      </SectionWrapper>
    </main>
  )
}
