"use client"

import CourierCargoForm from "@/app/components/forms/CourierAndParcelForm"
import {
  SectionTitle,
  SectionWrapper,
  SplitView,
} from "@/app/shared/components"

const CourierAndParcel = () => {
  return (
    <SectionWrapper>
      <SectionTitle title='Courier and Parcel' showBackButton />
      <SplitView
        image='/images/shipment.webp'
        className='[&>div:first-child_img]:object-[center_top]'
      >
        <CourierCargoForm />
      </SplitView>
    </SectionWrapper>
  )
}

export default CourierAndParcel
