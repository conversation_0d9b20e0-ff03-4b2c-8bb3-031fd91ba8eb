import { Service } from "@/app/shared/data/services"
import { GlobalArrowRightIcon } from "@/Icons"
import Image from "next/image"
import Link from "next/link"

const ServicesCard = ({ icon, name, href }: Service) => {
  return (
    <Link href={`${href}`}>
      <div className='group flex h-full items-center justify-between gap-5 border border-gray-200 p-5 transition-shadow ~text-base/xl ~rounded-lg/2xl hover:shadow-md active:shadow-none'>
        <div>
          <span className='relative inline-block ~size-12/20'>
            <Image src={icon} alt={name} fill />
          </span>
          <h3 className='font-medium'>{name}</h3>
        </div>
        <GlobalArrowRightIcon className='-translate-x-2 opacity-0 transition group-hover:translate-x-0 group-hover:opacity-100 group-active:translate-x-1' />
      </div>
    </Link>
  )
}

export default ServicesCard
