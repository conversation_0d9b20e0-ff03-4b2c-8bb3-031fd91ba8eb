import { TextBanner } from "@/app/components/common"
import {
  <PERSON><PERSON><PERSON><PERSON>,
  SectionT<PERSON><PERSON>,
  SectionWrapper,
} from "@/app/shared/components"
import { TERMS_AND_CONDITIONS } from "@/config"

const TermsAndConditions = () => {
  return (
    <>
      <PageHeader secondLine='TERMS AND CONDITIONS' />
      <SectionWrapper className='font-inter !leading-tight text-zinc-400 ~text-sm/lg'>
        <SectionTitle
          title='TERMS AND CONDITIONS'
          subtitle='LEGAL'
          className='text-black'
          titleElement='h2'
        />
        <p>{TERMS_AND_CONDITIONS.introduction}</p>
        <div className='~mt-5/7 ~space-y-5/7'>
          {TERMS_AND_CONDITIONS.data.map((data, index) => (
            <div key={index}>
              <p className='uppercase text-primary ~mb-3/5'>{data.title}</p>
              {data.contentList ? (
                <ul className='list-disc'>
                  {data.contentList.map((list, index) => (
                    <li className='ml-5' key={index}>
                      {list}
                    </li>
                  ))}
                </ul>
              ) : (
                <p>{data.content}</p>
              )}
            </div>
          ))}
        </div>
      </SectionWrapper>
      <TextBanner
        text='Thank you for choosing Nex Move. We appreciate your understanding and are here to assist with any questions or concerns regarding our Terms and Conditions.'
        className='mx-auto max-w-screen-xl'
      />
    </>
  )
}

export default TermsAndConditions
