import { FEATURE_HIGHLIGHTS } from "@/config"
import { SectionWrapper } from "@/app/shared/components"

const FeatureHighlights = () => {
  return (
    <SectionWrapper className='~pt-10/16'>
      <div className='grid grid-cols-3 gap-[10%] max-md:grid-cols-1 max-md:gap-8'>
        {FEATURE_HIGHLIGHTS.map((feature) => (
          <div
            key={feature.title}
            className='flex items-start gap-3 font-inter'
          >
            <feature.icon className='flex-shrink-0 max-sm:w-10' />
            <div>
              <h2 className='font-semibold ~text-sm/lg'>{feature.title}</h2>
              <p className='leading-snug text-gray-400 ~text-sm/base'>
                {feature.description}
              </p>
            </div>
          </div>
        ))}
      </div>
    </SectionWrapper>
  )
}

export default FeatureHighlights
