"use client"

import { ButtonLink } from "@/app/components/ui"
import { SectionTitle, SplitView, StepProgress } from "@/app/shared/components"
import { HOW_TO_START_STEPS } from "@/config"
import { motion, useScroll } from "framer-motion"
import Link from "next/link"
import { useEffect, useRef, useState } from "react"

const HowToStart = () => {
  const ref = useRef<HTMLDivElement>(null)
  const [stepsToAnimate, setStepsToAnimate] = useState<number[]>([])

  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start center", "end center"],
  })

  useEffect(() => {
    const unsubscribe = scrollYProgress.on("change", (value) => {
      const numSteps = HOW_TO_START_STEPS.length
      const stepThreshold = 1 / numSteps

      const newStepsToAnimate = HOW_TO_START_STEPS.reduce(
        (acc: number[], _, index) => {
          if (value >= index * stepThreshold) {
            acc.push(index)
          }
          return acc
        },
        [],
      )

      setStepsToAnimate(newStepsToAnimate)
    })

    return () => unsubscribe()
  }, [scrollYProgress])

  return (
    <div>
      <SectionTitle
        title='How to Get Started with NexMove?'
        subtitle='Our Something'
        className='~px-4/16'
        titleElement='h5'
      />
      <SplitView
        image='/images/shipment.webp'
        labelBoldText='GET'
        labelText='QUOTE'
        className='[&>div:first-child_img]:object-[center_top]'
      >
        <>
          <p className='~text-lg/xl' aria-hidden>
            Use our Instant{" "}
            <Link href='/services' className='font-bold hover:underline'>
              Quote Calculator
            </Link>{" "}
            to get an <br className='max-sm:hidden' /> estimate for your move.
          </p>

          <em className='sr-only hidden'>
            Use our Instant Quote Calculator to get an estimate for your move.
          </em>
          <div className='flex items-center gap-4 ~py-4/6'>
            <p aria-hidden className='~text-sm/base'>
              Or
            </p>
            <span className='h-[0.5px] w-full bg-gray-600/50'></span>
          </div>
          <div ref={ref} className='relative isolate grid ~gap-5/10'>
            {HOW_TO_START_STEPS.map((step, index) => (
              <StepProgress
                key={step.number}
                {...step}
                animate={stepsToAnimate.includes(index)}
              />
            ))}

            {/* steps bottom line */}
            <motion.span
              style={{ scaleY: scrollYProgress }}
              className='absolute bottom-5 top-5 -z-10 origin-top border-l-[1px] border-dashed border-primary ~left-5/6 sm:border-l-2'
            />
          </div>
          <ButtonLink
            aria-label='Request Quote'
            href='/request-quote'
            className='~mt-8/16'
          >
            Request Quote
          </ButtonLink>
        </>
      </SplitView>
    </div>
  )
}

export default HowToStart
