import { SectionTitle, SectionWrapper } from "@/app/shared/components"
import { VALUE_ADDED_SERVICES } from "@/config"
import Image from "next/image"

const ValueAddedService = () => {
  return (
    <SectionWrapper>
      <SectionTitle
        title='Nex Value-Added Services'
        subtitle='Our Something'
        titleElement='h4'
      />
      <div className='group grid ~gap-2/4 sm:grid-cols-2 md:grid-cols-3'>
        {VALUE_ADDED_SERVICES.map((card, index) => (
          <div
            key={index}
            className='grid scale-100 content-between text-white opacity-100 transition duration-300 hover:!scale-100 group-hover:scale-[0.98]'
            style={{ backgroundColor: card.cardColor }}
            aria-label={card.title}
          >
            <h2 className='font-medium !leading-tight ~text-lg/2xl ~p-4/6'>
              {card.title}
            </h2>
            <div className='relative aspect-square w-full overflow-hidden bg-white/20'>
              <Image
                src={card.img}
                alt={card.title}
                fill
                sizes='(max-width: 768px) 100vw, 33vw'
                draggable={false}
                className='object-cover transition-transform duration-500 group-hover:scale-105'
              />
            </div>
          </div>
        ))}
      </div>
    </SectionWrapper>
  )
}

export default ValueAddedService
