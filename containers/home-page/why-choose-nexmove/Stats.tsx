"use client"

import { CountUp } from "@/app/components/common"
import { SERVICE_STATS } from "@/config"

export const Stats = () => {
  return (
    <>
      <div className='grid grid-cols-[repeat(auto-fit,minmax(300px,1fr))] divide-x-2 text-center ~gap-y-3/5 2xl:grid-cols-5'>
        {SERVICE_STATS.map((stat) => (
          <div
            key={stat.label}
            className='grid content-start bg-gray-100 font-bold ~text-3xl/5xl ~px-2/4 ~py-8/12'
            aria-label={`${stat.value}+ ${stat.label}`}
          >
            <CountUp
              from={0}
              to={stat.value}
              separator=','
              direction='up'
              duration={0.5}
              suffix={"+"}
            />
            <p
              aria-description='Stat Label'
              className='mx-auto max-w-[200px] font-normal !leading-tight text-black ~text-base/xl ~pt-1/3'
            >
              {stat.label}
            </p>
          </div>
        ))}
      </div>
    </>
  )
}
