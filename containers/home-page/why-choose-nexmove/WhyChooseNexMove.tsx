"use client"

import { <PERSON><PERSON> } from "@/app/components/ui"
import { SectionTitle, SectionWrapper } from "@/app/shared/components"
import { cn } from "@/app/shared/utils"
import { FEATURES_LIST } from "@/config"
import { LogoElementIcon, LogoIcon, MobileIcon } from "@/Icons"
import { AnimatePresence, motion as m } from "framer-motion"
import Image from "next/image"
import { useState } from "react"
import { Stats } from "./Stats"

import { GlobalArrowLeftIcon, GlobalArrowRightIcon } from "@/Icons"

const WhyChooseNexMove = () => {
  const [activeIndex, setActiveIndex] = useState(0)

  const handleNext = () => {
    setActiveIndex((prevIndex) => (prevIndex + 1) % FEATURES_LIST.length)
  }

  const handlePrev = () => {
    setActiveIndex(
      (prevIndex) =>
        (prevIndex - 1 + FEATURES_LIST.length) % FEATURES_LIST.length,
    )
  }

  return (
    <SectionWrapper className='!px-0'>
      <SectionTitle
        title='Why Choose NexMove?'
        subtitle='Our Something'
        className='~px-4/16'
        titleElement='h3'
      />
      <div className='mx-auto max-w-screen-xl ~px-4/16 ~pb-4/8'>
        <div className='mx-auto flex min-h-28 max-w-screen-md items-center justify-center overflow-hidden ~gap-5/8'>
          <AnimatePresence mode='wait'>
            <m.p
              aria-description={FEATURES_LIST[activeIndex].title}
              className='flex-1 text-right !leading-tight ~text-lg/2xl'
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -30 }}
              transition={{ duration: 0.3 }}
              key={`${activeIndex}-title`}
            >
              {FEATURES_LIST[activeIndex].title}
            </m.p>
          </AnimatePresence>
          <AnimatePresence mode='wait'>
            <m.p
              aria-description={FEATURES_LIST[activeIndex].description}
              className='flex-1 text-left font-inter !leading-tight text-zinc-400 ~text-sm/base'
              initial={{ opacity: 0, x: 30 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 30 }}
              transition={{ duration: 0.3 }}
              key={`${activeIndex}-description`}
            >
              {FEATURES_LIST[activeIndex].description}
            </m.p>
          </AnimatePresence>
        </div>

        <div className='relative ~pt-5/8'>
          <div className='group relative mx-auto aspect-video max-w-screen-md overflow-hidden'>
            <LogoElementIcon className='absolute inset-0 size-full' />
            <MobileIcon className='absolute left-1/2 top-1/2 size-[90%] -translate-x-1/2 -translate-y-1/2' />
            <LogoIcon className='absolute left-1/2 top-[25%] w-[16%] -translate-x-1/2 -translate-y-1/2 text-white' />
            <m.div
              className='absolute left-1/2 top-[60%] aspect-square w-[37%] origin-center'
              onClick={() => handleNext()}
              initial={{
                opacity: 0,
                transform: "translateX(-50%) translateY(-50%) scale(0.8)",
              }}
              animate={{
                opacity: 1,
                transform: "translateX(-50%) translateY(-50%) scale(1)",
              }}
              transition={{ duration: 0.5, type: "spring", stiffness: 120 }}
              key={activeIndex}
            >
              <Image
                src={FEATURES_LIST[activeIndex].image}
                alt={FEATURES_LIST[activeIndex].title}
                className='object-cover'
                draggable='false'
                // loading='lazy'
                loading='eager'
                fill
                sizes='(max-width: 768px) 100vw, 60vw'
              />
            </m.div>
          </div>

          {/* Next and Previous Buttons*/}
          <div className='pointer-events-none absolute left-0 right-0 top-1/2 flex items-center justify-between'>
            <PrevButton onClick={handlePrev} />
            <NextButton onClick={handleNext} />
          </div>
        </div>

        <div className='flex flex-wrap items-center justify-center ~gap-2/5 ~py-5/8'>
          {FEATURES_LIST.map((feature, index) => (
            <Button
              onClick={() => setActiveIndex(index)}
              key={index}
              size='sm'
              aria-label={"Learn more about " + feature.title}
              className={cn(
                "rounded-lg bg-zinc-100",
                activeIndex === index &&
                  "bg-gradient-to-b from-primary to-secondary",
              )}
            >
              <feature.icon
                className={cn(
                  "~size-6/8",
                  activeIndex === index && "[&>path]:stroke-white",
                )}
              />
            </Button>
          ))}
        </div>
      </div>
      {/* Stats */}
      <Stats />
    </SectionWrapper>
  )
}

export default WhyChooseNexMove

const NextButton = ({ onClick }: { onClick: () => void }) => {
  return (
    <Button
      disabled={false}
      className='pointer-events-auto rounded-full bg-gradient-to-r from-zinc-50 to-primary !p-[1px] ~size-10/12'
      onClick={onClick}
      size='sm'
      aria-label='Next'
    >
      <span className='grid size-full place-content-center rounded-full bg-zinc-50 text-black'>
        <GlobalArrowRightIcon className='size-6' strokeWidth='1px' />
      </span>
    </Button>
  )
}

const PrevButton = ({ onClick }: { onClick: () => void }) => {
  return (
    <Button
      disabled={false}
      className='pointer-events-auto rounded-full bg-gradient-to-r from-primary to-zinc-50 !p-[1px] ~size-10/12'
      onClick={onClick}
      size='sm'
      aria-label='Previous'
    >
      <span className='grid size-full place-content-center rounded-full bg-zinc-50 text-black'>
        <GlobalArrowLeftIcon className='size-6' strokeWidth='1px' />
      </span>
    </Button>
  )
}
