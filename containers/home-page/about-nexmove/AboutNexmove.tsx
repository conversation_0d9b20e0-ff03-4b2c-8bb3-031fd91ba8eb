import { But<PERSON> } from "@/app/components/ui"
import { DashboardIcon, WavePatternIcon } from "@/Icons"
import Link from "next/link"

const AboutNexmove = () => {
  return (
    <div className='relative isolate overflow-hidden bg-[#131415] text-white'>
      <WavePatternIcon className='absolute bottom-0 left-0 -z-10 w-full sm:translate-y-[35%]' />

      <div className='mx-auto w-[min(1000px,100%-2rem)] ~pt-10/20'>
        <div>
          <span className='font-inter font-light uppercase ~text-xs/sm'>
            Smart logistics platform
          </span>
          <p
            aria-label='NexMove Description'
            aria-description="NexMove Packing & Moving ******** is your trusted partner for seamless relocations. Whether it's your home, office, industry, or even your beloved pet, we're here to make every move stress-free and efficient."
            className='~text-lg/3xl ~pt-3/5 max-sm:leading-tight'
          >
            NexMove Packing & Moving ******** is your trusted partner for
            seamless relocations. Whether it&apos;s your home, office, industry,
            or even your beloved pet, we&apos;re here to make every move
            stress-free and efficient.
            <br />
            <br />
            Contact us today or request a free quote to start your journey with
            NexMove!
          </p>
        </div>

        <Link href='/contact'>
          <Button
            aria-label='Contact Sales'
            className='bg-white text-black ~mt-5/8 max-sm:text-sm'
          >
            Contact Sales
          </Button>
        </Link>

        <div className='-mb-10 rounded-[2.5rem] bg-gradient-to-b from-zinc-500 to-black ~mt-14/20 ~p-3/4'>
          <div className='aspect-[16/10] w-full bg-gray-200 pt-2 ~rounded-[2rem]/[1.8rem]'>
            <DashboardIcon
              aria-label='Dashboard Icon'
              className='h-full w-full scale-x-110'
            />
          </div>
        </div>
      </div>
    </div>
  )
}

export default AboutNexmove
