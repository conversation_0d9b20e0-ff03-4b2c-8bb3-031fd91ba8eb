/* eslint-disable @next/next/no-img-element */
import { OUR_PARTNERS } from "@/config"

const OurPartnersMarquee = () => {
  return (
    <div className='from flex overflow-hidden bg-gradient-to-r from-primary to-secondary ~py-3/3.5'>
      {/* <PERSON><PERSON> multiple times to create the infinite effect */}
      <OurPartnersMarqueeSlide />
      <OurPartnersMarqueeSlide />
      <OurPartnersMarqueeSlide />
    </div>
  )
}

export default OurPartnersMarquee

const OurPartnersMarqueeSlide = () => {
  return (
    <div className='flex flex-shrink-0 animate-slide-left items-center will-change-transform'>
      {OUR_PARTNERS.map((brand) => (
        <span
          aria-label={brand.name}
          key={brand.name}
          className='relative aspect-[24/9] ~w-24/32 ~px-2/4'
        >
          <img
            src={brand.logo}
            alt={brand.name}
            className='size-full object-contain'
            draggable={false}
            loading='lazy'
          />
        </span>
      ))}
    </div>
  )
}
