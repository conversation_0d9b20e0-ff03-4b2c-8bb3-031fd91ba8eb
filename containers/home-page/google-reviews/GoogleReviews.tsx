"use client"

import { <PERSON><PERSON> } from "@/app/components/ui"
import { SectionTitle, SectionWrapper } from "@/app/shared/components"
import { getGoogleReviews } from "@/app/shared/services/google-reviews"
import { cn } from "@/app/shared/utils"
import { GOOGLE_REVIEWS } from "@/config"
import {
  useActiveSlideIndex,
  useCanScroll,
  useIsClient,
  useWindowSize,
} from "@/hooks"
import { GlobalArrowLeftIcon, GlobalArrowRightIcon } from "@/Icons"
import { QUERY_KEYS } from "@/lib/react-query"
import { useQuery } from "@tanstack/react-query"
import useEmblaCarousel from "embla-carousel-react"
import { motion } from "framer-motion"
import Image from "next/image"
import { useCallback, useEffect, useState } from "react"
import GoogleReviewCard from "./GoogleReviewCard"

// Custom dot button component for the carousel
const DotButton = ({
  selected,
  onClick,
}: {
  selected: boolean
  onClick: () => void
}) => (
  <button
    className={cn(
      "h-2 w-2 rounded-full transition-all",
      selected ? "w-6 bg-primary" : "bg-gray-300 hover:bg-gray-400",
    )}
    type='button'
    onClick={onClick}
    aria-label='Carousel dot button'
  />
)

const GoogleReviews = () => {
  const { width } = useWindowSize()
  const isClient = useIsClient()
  const [slidesPerView, setSlidesPerView] = useState(3)

  // Fetch Google reviews using React Query
  const { data: reviewsData, isLoading } = useQuery({
    queryKey: [QUERY_KEYS.GOOGLE_REVIEWS],
    queryFn: getGoogleReviews,
    staleTime: 1000 * 60 * 60, // 1 hour
    refetchOnWindowFocus: false,
  })

  // Use fetched reviews or fall back to mock data
  const reviews = reviewsData?.reviews || GOOGLE_REVIEWS
  const averageRating = reviewsData?.averageRating || 4.8
  const totalReviews = reviewsData?.totalReviews || reviews.length

  // Configure Embla Carousel options
  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: true,
    align: "start",
    slidesToScroll: 1,
    skipSnaps: false,
    dragFree: true,
    containScroll: "trimSnaps",
  })

  // Get active slide index for dot navigation
  const { activeIndex } = useActiveSlideIndex(emblaApi)
  const { canScrollPrev, canScrollNext } = useCanScroll(emblaApi)
  const [scrollSnaps, setScrollSnaps] = useState<number[]>([])

  // Update slides per view based on screen size
  useEffect(() => {
    if (!isClient) return

    let newSlidesPerView = 3
    if (width < 640) {
      newSlidesPerView = 1
    } else if (width < 1024) {
      newSlidesPerView = 2
    }

    setSlidesPerView(newSlidesPerView)

    // Reinitialize carousel when slidesPerView changes
    if (emblaApi) {
      setTimeout(() => {
        emblaApi.reInit()
      }, 0)
    }
  }, [width, isClient, emblaApi])

  // Update scroll snaps when emblaApi or slidesPerView changes
  useEffect(() => {
    if (!emblaApi) return

    // Update scroll snaps without triggering reInit
    const updateScrollSnaps = () => {
      setScrollSnaps(emblaApi.scrollSnapList())
    }

    // Initialize with current slidesPerView
    emblaApi.reInit()
    updateScrollSnaps()

    // Listen for future reInit events
    emblaApi.on("reInit", updateScrollSnaps)

    return () => {
      emblaApi.off("reInit", updateScrollSnaps)
    }
  }, [emblaApi, slidesPerView])

  // Scroll to specific slide
  const scrollTo = useCallback(
    (index: number) => emblaApi && emblaApi.scrollTo(index),
    [emblaApi],
  )

  // Handle previous slide
  const handlePrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev()
  }, [emblaApi])

  // Handle next slide
  const handleNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext()
  }, [emblaApi])

  return (
    <SectionWrapper className='~pt-14/20'>
      <div className='mx-auto max-w-screen-xl'>
        <div className='flex flex-wrap items-center justify-between gap-4 ~mb-6/8'>
          <SectionTitle
            subtitle='Google Reviews'
            title='See what our customers are saying about us on Google'
            titleElement='h5'
          />
          <motion.div
            className='flex items-center gap-2 rounded-lg border border-gray-100 bg-white px-4 py-2 shadow-sm'
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.2 }}
          >
            <Image
              src='/svgs/google-logo.svg'
              alt='Google'
              width={24}
              height={24}
            />
            <div className='flex items-center'>
              <span className='mr-1 font-bold text-yellow-400'>
                {averageRating.toFixed(1)}
              </span>
              <div className='flex' aria-label='Star Rating Icon'>
                {[...Array(5)].map((_, i) => (
                  <svg
                    key={i}
                    className='h-4 w-4 text-yellow-400'
                    aria-hidden='true'
                    xmlns='http://www.w3.org/2000/svg'
                    fill='currentColor'
                    viewBox='0 0 22 20'
                  >
                    <path d='M20.924 7.625a1.523 1.523 0 0 0-1.238-1.044l-5.051-.734-2.259-4.577a1.534 1.534 0 0 0-2.752 0L7.365 5.847l-5.051.734A1.535 1.535 0 0 0 1.463 9.2l3.656 3.563-.863 5.031a1.532 1.532 0 0 0 2.226 1.616L11 17.033l4.518 2.375a1.534 1.534 0 0 0 2.226-1.617l-.863-5.03L20.537 9.2a1.523 1.523 0 0 0 .387-1.575Z' />
                  </svg>
                ))}
              </div>
            </div>
          </motion.div>
        </div>

        <div className='relative'>
          {/* Embla Carousel */}
          <div className='overflow-hidden' ref={emblaRef}>
            <div className='flex' style={{ marginLeft: "-1rem" }}>
              {reviews.map((review) => (
                <div
                  aria-label='Google Review'
                  key={review.id}
                  className='min-w-0 pl-4'
                  style={{
                    flex: "0 0 auto",
                    width: `calc(100% / ${slidesPerView})`,
                    paddingLeft: "1rem",
                  }}
                >
                  <div className='h-full'>
                    <GoogleReviewCard {...review} />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Navigation Dots */}
          <div className='flex justify-center ~mt-6/8 ~gap-2/3'>
            {scrollSnaps.map((_, index) => (
              <DotButton
                key={index}
                selected={index === activeIndex}
                onClick={() => scrollTo(index)}
              />
            ))}
          </div>

          {/* Navigation Buttons */}
          <div className='flex justify-center ~mt-4/6 ~gap-2/4'>
            <Button
              aria-label='Previous Review'
              className={cn(
                "pointer-events-auto rounded-full bg-gradient-to-r from-primary to-zinc-50 !p-[1px] ~size-10/12",
                !canScrollPrev && "cursor-not-allowed opacity-50",
              )}
              onClick={handlePrev}
              size='sm'
              disabled={!canScrollPrev}
            >
              <span className='grid size-full place-content-center rounded-full bg-zinc-50 text-black'>
                <GlobalArrowLeftIcon className='size-5' />
              </span>
            </Button>
            <Button
              aria-label='Next Review'
              className={cn(
                "pointer-events-auto rounded-full bg-gradient-to-r from-zinc-50 to-primary !p-[1px] ~size-10/12",
                !canScrollNext && "cursor-not-allowed opacity-50",
              )}
              onClick={handleNext}
              size='sm'
              disabled={!canScrollNext}
            >
              <span className='grid size-full place-content-center rounded-full bg-zinc-50 text-black'>
                <GlobalArrowRightIcon
                  aria-label='Arrow Right Icon '
                  className='size-5'
                />
              </span>
            </Button>
          </div>
        </div>
      </div>
    </SectionWrapper>
  )
}

export default GoogleReviews
