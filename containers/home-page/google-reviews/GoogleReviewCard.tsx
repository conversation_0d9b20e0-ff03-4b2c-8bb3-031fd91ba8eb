"use client"
import { motion } from "framer-motion"
import Image from "next/image"

// Extended type to handle both API and mock data
type TGoogleReviewCardProps = {
  id: string | number
  name: string
  rating: number
  review: string
  date: string
  profileImage: string
}

const StarRating = ({ rating }: { rating: number }) => {
  return (
    <div className='flex gap-1'>
      {[...Array(5)].map((_, index) => (
        <svg
          key={index}
          className={`h-4 w-4 ${
            index < rating ? "text-yellow-400" : "text-gray-300"
          }`}
          aria-hidden='true'
          xmlns='http://www.w3.org/2000/svg'
          fill='currentColor'
          viewBox='0 0 22 20'
        >
          <path d='M20.924 7.625a1.523 1.523 0 0 0-1.238-1.044l-5.051-.734-2.259-4.577a1.534 1.534 0 0 0-2.752 0L7.365 5.847l-5.051.734A1.535 1.535 0 0 0 1.463 9.2l3.656 3.563-.863 5.031a1.532 1.532 0 0 0 2.226 1.616L11 17.033l4.518 2.375a1.534 1.534 0 0 0 2.226-1.617l-.863-5.03L20.537 9.2a1.523 1.523 0 0 0 .387-1.575Z' />
        </svg>
      ))}
    </div>
  )
}

const GoogleReviewCard = (props: TGoogleReviewCardProps) => {
  const { name, rating, review, date, profileImage } = props

  return (
    <motion.div
      className='flex h-full flex-col rounded-lg border border-gray-100 bg-white p-5 shadow-sm'
      whileHover={{ y: -5 }}
      transition={{ duration: 0.3 }}
    >
      <div className='mb-3 flex items-center'>
        <div className='relative mr-3 h-10 w-10 flex-shrink-0'>
          <Image
            // src={profileImage}
            src={"/images/testimonial-avatar.webp"}
            alt={name}
            className='rounded-full object-cover'
            fill
            sizes='40px'
          />
        </div>
        <div className='overflow-hidden'>
          <h3 className='truncate font-medium text-gray-900'>{name}</h3>
          <div className='flex items-center'>
            <StarRating rating={rating} />
            <span className='ml-2 text-xs text-gray-500'>{date}</span>
          </div>
        </div>
      </div>

      <p className='mb-3 line-clamp-4 flex-grow font-inter text-sm leading-relaxed text-gray-700'>
        {review}
      </p>

      <div className='mt-auto flex items-center border-t border-gray-100 pt-3'>
        <Image
          src='/svgs/google-logo.svg'
          alt='Google Review'
          width={16}
          height={16}
          className='mr-2'
        />
        <span className='text-xs text-gray-500'>Posted on Google</span>
      </div>
    </motion.div>
  )
}

export default GoogleReviewCard
