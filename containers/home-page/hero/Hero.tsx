"use client"
import { <PERSON><PERSON> } from "@/app/components/ui"
import {
  HERO_SLIDES,
  heroBgImageUrl,
  heroVideoPosterUrl,
  heroVideoUrl,
} from "@/config"
import { AnimatePresence, motion, useInView } from "framer-motion"
import Link from "next/link"
import { useCallback, useEffect, useRef, useState } from "react"

import { PauseIcon, PlayIcon } from "../../../Icons"


const Hero = () => {
  const [currentSlide, setCurrentSlide] = useState<number>(0)
  const [isPlaying, setIsPlaying] = useState<boolean>(false)
  const heroRef = useRef<HTMLDivElement>(null)
  const videoRef = useRef<HTMLVideoElement>(null)
  const isInView = useInView(heroRef, { once: false })

  const handleNextSlide = useCallback(() => {
    setCurrentSlide((prevSlide) => (prevSlide + 1) % HERO_SLIDES.length)
  }, [])

  const togglePlayPause = (): void => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause()
      } else {
        videoRef.current.play()
      }
      setIsPlaying(!isPlaying)
    }
  }

  useEffect(() => {
    const interval = setInterval(handleNextSlide, 4000)
    return () => {
      clearInterval(interval)
    }
  }, [handleNextSlide])

  useEffect(() => {
    if (!isInView && videoRef.current) {
      videoRef.current.pause()
      setIsPlaying(false)
    }
  }, [isInView])

  return (
    <div
      ref={heroRef}
      className='grid h-[100dvh] grid-cols-[45%_1fr] bg-primary bg-cover bg-center max-md:grid-cols-1'
      style={{ backgroundImage: `url(${heroBgImageUrl})` }}
    >
      {/* Hero Left View */}
      <div className='grid items-center pb-[5%] pt-[15%] max-md:content-center max-md:gap-[15%] max-md:text-center'>
        <AnimatePresence mode='wait'>
          <h1 className='hidden'>
            Welcome to Nexmove, your partner in moving forward!
          </h1>
          {isInView ? (
            <motion.p
              className='h-[25vh] font-light uppercase !leading-tight text-white ~text-4xl/6xl ~px-4/16 md:h-[35vh]'
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              transition={{ duration: 0.5 }}
              key={currentSlide}
            >
              {HERO_SLIDES[currentSlide]}
            </motion.p>
          ) : (
            <p className='h-[25vh] font-light uppercase !leading-tight text-white ~text-4xl/6xl ~px-4/16 md:h-[35vh]'>
              {HERO_SLIDES[currentSlide]}
            </p>
          )}
        </AnimatePresence>
        <div className='flex ~gap-2/4 ~px-4/16 max-lg:flex-col'>
          <Link href='/services'>
            <Button className='bg-white text-black' size='lg'>
              Get instant quote
            </Button>
          </Link>
          <Link
            href='/track-shipment'
            target='_blank'
            rel='noopener noreferrer'
          >
            <Button className='bg-black/15' size='lg'>
              Track way bill
            </Button>
          </Link>
        </div>
      </div>

      {/* Hero Right View */}
      <div className="relative before:absolute before:-left-4 before:bottom-0 before:top-0 before:w-[12%] before:backdrop-blur-sm before:content-[''] max-md:hidden">
        <video
          muted
          loop
          playsInline
          preload='none'
          poster={heroVideoPosterUrl}
          className='h-full w-full bg-black/50 object-cover'
          ref={videoRef}
        >
          <source src={heroVideoUrl} type='video/mp4' />
        </video>
        <motion.button
          onClick={togglePlayPause}
          className='absolute bottom-10 left-1/2 flex -translate-x-1/2 transform items-center justify-center rounded-full bg-white/20 p-4 text-white backdrop-blur-sm'
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >

          {isPlaying ? (
            <PauseIcon className='text-white ~size-4/7' />
          ) : (
            <PlayIcon className='text-white ~size-4/7' />
          )}

        </motion.button>
      </div>
    </div>
  )
}

export default Hero
