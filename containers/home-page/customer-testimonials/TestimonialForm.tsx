"use client"

import { Form<PERSON>rovider } from "@/app/components/forms/FormProvider"
import { CheckboxInputController } from "@/app/components/forms/controllers/checkbox-field"
import { SelectInputController } from "@/app/components/forms/controllers/select-field"
import { TextInputController } from "@/app/components/forms/controllers/text-field"
import { Button } from "@/app/components/ui"
import { InputLabel } from "@/app/shared/components"
import { zodResolver } from "@hookform/resolvers/zod"
import { motion } from "framer-motion"
import { useState } from "react"
import { useForm } from "react-hook-form"
import { z } from "zod"

// Service options for the dropdown
const SERVICE_OPTIONS = [
  { value: "packing_moving", label: "Packing & Moving Services" },
  { value: "storage", label: "Storage Services" },
  { value: "trucking", label: "Trucking & Part-Load Services" },
  { value: "courier", label: "Courier & Parcel Services" },
  { value: "ambulance", label: "Ambulance Services" },
  { value: "marketplace", label: "Marketplace for Old Items" },
]

// Rating options
const RATING_OPTIONS = [
  { value: "5", label: "Excellent (5 Stars)" },
  { value: "4", label: "Very Good (4 Stars)" },
  { value: "3", label: "Good (3 Stars)" },
  { value: "2", label: "Fair (2 Stars)" },
  { value: "1", label: "Poor (1 Star)" },
]

// Define the schema for the testimonial form
const testimonialFormSchema = z.object({
  name: z.string().min(1, "Please enter your name"),
  email: z
    .string()
    .email("Please enter a valid email address")
    .optional()
    .or(z.literal("")),
  phone: z
    .string()
    .min(10, "Phone number must be at least 10 digits")
    .max(15, "Phone number is too long")
    .optional()
    .or(z.literal("")),
  location: z.string().min(1, "Please enter your location"),
  serviceUsed: z.string().min(1, "Please select the service you used"),
  rating: z.string().min(1, "Please select a rating"),
  testimonial: z
    .string()
    .min(10, "Testimonial must be at least 10 characters long"),
  testimonialType: z.string().optional(),
  allowContact: z.boolean().optional(),
})

// Define the type for the form values
type TTestimonialFormValues = z.infer<typeof testimonialFormSchema>

const TestimonialForm = () => {
  const [isSubmitted, setIsSubmitted] = useState(false)

  // Default values for the form
  const defaultValues: TTestimonialFormValues = {
    name: "",
    email: "",
    phone: "",
    location: "",
    serviceUsed: "",
    rating: "",
    testimonial: "",
    testimonialType: "Customer", // Hardcoded as per requirements
    allowContact: false,
  }

  // Initialize form with react-hook-form
  const methods = useForm<TTestimonialFormValues>({
    defaultValues,
    resolver: zodResolver(testimonialFormSchema),
    mode: "onChange",
  })

  // Store submitted data for the success message
  const [submittedData, setSubmittedData] =
    useState<TTestimonialFormValues | null>(null)

  // Handle form submission
  const onSubmit = (data: TTestimonialFormValues) => {
    // In a real application, you would send this data to your backend
    // For now, we'll just show a success message
    setSubmittedData(data)
    setIsSubmitted(true)
  }

  if (isSubmitted && submittedData) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className='rounded-lg border border-green-200 bg-green-50 text-center ~p-6/8'
        aria-description='Thank You Message After Submitting Testimonial'
      >
        <p className='font-medium text-green-700 ~text-lg/xl ~mb-2/3'>
          Thank You for Your Testimonial!
        </p>
        <p className='text-green-600'>
          Your testimonial has been submitted successfully. We value your
          feedback and appreciate you taking the time to share your experience
          with us.
        </p>
        {submittedData.allowContact && (
          <p className='text-green-600 ~mt-2/3'>
            We may contact you shortly to discuss your experience further.
          </p>
        )}
        <Button
          aria-label='Submit Another Testimonial'
          onClick={() => setIsSubmitted(false)}
          className='bg-green-600 ~mt-4/6 hover:bg-green-700'
        >
          Submit Another Testimonial
        </Button>
      </motion.div>
    )
  }

  return (
    <div
      aria-description='Share your experience with us and help us improve our services.'
      className='rounded-lg border border-zinc-200 bg-zinc-50 ~p-6/8'
    >
      <p
        aria-label='Share Your Experience'
        className='font-medium ~text-lg/xl ~mb-4/6'
      >
        Share Your Experience
      </p>
      <FormProvider methods={methods} onSubmit={onSubmit}>
        <div className='grid ~gap-x-4/6 ~gap-y-3/5 sm:grid-cols-2'>
          <div>
            <InputLabel label='Name' id='name' />
            <TextInputController name='name' placeholder='Enter your name' />
          </div>
          <div>
            <InputLabel label='Location' id='location' />
            <TextInputController
              name='location'
              placeholder='Enter your location'
            />
          </div>
          <div>
            <InputLabel label='Email (Optional)' id='email' />
            <TextInputController
              name='email'
              placeholder='Enter your email'
              type='email'
            />
          </div>
          <div>
            <InputLabel label='Phone (Optional)' id='phone' />
            <TextInputController
              name='phone'
              placeholder='Enter your phone number'
              type='tel'
            />
          </div>
          <div>
            <InputLabel label='Service Used' id='serviceUsed' />
            <SelectInputController
              name='serviceUsed'
              options={SERVICE_OPTIONS}
              placeholder='Select the service you used'
            />
          </div>
          <div>
            <InputLabel label='Rating' id='rating' />
            <SelectInputController
              name='rating'
              options={RATING_OPTIONS}
              placeholder='How would you rate our service?'
            />
          </div>
          <div className='hidden'>
            <InputLabel label='Testimonial Type' id='testimonialType' />
            <TextInputController
              name='testimonialType'
              overrideValue='Customer'
              disabled
            />
          </div>
          <div className='sm:col-span-2'>
            <InputLabel label='Your Testimonial' id='testimonial' />
            <TextInputController
              name='testimonial'
              placeholder='Share your experience with us...'
              isTextarea
              rows={4}
            />
          </div>
          <div className='~mt-2/3 sm:col-span-2'>
            <CheckboxInputController
              name='allowContact'
              label='I allow NexMove to contact me regarding my testimonial'
            />
          </div>
        </div>
        <div className='flex justify-center ~mt-6/8'>
          <Button
            aria-label='Submit Testimonial'
            type='submit'
            className='~px-8/10'
          >
            Submit Testimonial
          </Button>
        </div>
      </FormProvider>
    </div>
  )
}

export default TestimonialForm
