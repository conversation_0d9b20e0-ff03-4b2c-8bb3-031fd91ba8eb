import { TCustomerTestimonials } from "@/config"
import Image from "next/image"

type TCustomerTestimonialCardProps = TCustomerTestimonials & {
  test?: boolean
}

const CustomerTestimonialCard = (props: TCustomerTestimonialCardProps) => {
  const { image, name, location, service, review } = props

  return (
    <div className='grid grid-cols-[30%_55%] items-center justify-center px-[1px] ~gap-4/8 max-sm:grid-cols-1'>
      {/* Left View */}
      <div className='relative h-full min-h-60 sm:aspect-[9/12]'>
        <Image
          loading='lazy'
          src={image}
          alt={name}
          className='object-cover'
          draggable={false}
          aria-hidden
          sizes='(max-width: 768px) 100vw, 50vw'
          fill
        />
      </div>
      {/* Right View */}
      <div className='grid font-manrope ~gap-3/5 sm:pr-10'>
        <div className='grid ~gap-0/0.5'>
          <p
            aria-label='Customer Name'
            className='font-medium !leading-none ~text-lg/2xl'
          >
            {name}
          </p>
          <span className='text-xs capitalize text-zinc-500'>{location}</span>
          <span className='text-xs font-medium uppercase text-primary ~pt-0/0.5'>
            {service}
          </span>
        </div>
        <p
          aria-label='Customer Review'
          className='max-w-[600px] font-inter !leading-snug ~text-sm/lg'
        >
          {review}
        </p>
      </div>
    </div>
  )
}

export default CustomerTestimonialCard
