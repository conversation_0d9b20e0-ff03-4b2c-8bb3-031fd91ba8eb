"use client"

import { <PERSON><PERSON> } from "@/app/components/ui"
import { SectionTit<PERSON>, SectionWrapper } from "@/app/shared/components"
import { Carousel } from "@/app/shared/components/carousel"
import { CUSTOMER_TESTIMONIALS } from "@/config"
import { AnimatePresence, motion } from "framer-motion"
import { useState } from "react"
import { CustomerTestimonialCard } from "./index"
import TestimonialForm from "./TestimonialForm"

const CustomerTestimonials = () => {
  const [showForm, setShowForm] = useState(false)

  // toggle
  const toggleForm = () => {
    setShowForm((prev) => !prev)
  }

  return (
    <SectionWrapper className='~pt-14/20'>
      <div className='mx-auto max-w-screen-xl'>
        <SectionTitle
          subtitle='Customer Testimonials'
          title="We are proud to serve thousands of satisfied customers across all our services. Here's what some of them have to say about their experience with NexMove:"
          titleElement='h6'
        />
        <div>
          <Carousel
            slideNodes={CUSTOMER_TESTIMONIALS.map((testimonial) => (
              <CustomerTestimonialCard key={testimonial.id} {...testimonial} />
            ))}
            navigationContainerClassName='absolute left-0 top-1/2 -translate-y-1/2 flex-col max-sm:hidden'
          />
        </div>

        <div className='flex justify-center ~mt-8/12'>
          <Button
            aria-label='Add Review'
            type='button'
            onClick={toggleForm}
            className='bg-primary text-white hover:bg-primary/90'
          >
            {showForm ? "Hide Form" : "Add Review"}
          </Button>
        </div>

        <AnimatePresence>
          {showForm && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className='overflow-hidden ~mt-6/8'
            >
              <TestimonialForm />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </SectionWrapper>
  )
}

export default CustomerTestimonials
