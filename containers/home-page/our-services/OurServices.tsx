import ScrollVelocity from "@/app/components/common/ScrollVelocity"
import { SectionTitle, SectionWrapper } from "@/app/shared/components"
import { SERVICES } from "@/config"
import ServiceCard from "./ServiceCard"

const OurServices = () => {
  return (
    <SectionWrapper className='!px-0 ~pt-10/16'>
      <SectionTitle
        subtitle='Our Something'
        title='Our Services'
        className='~px-4/16'
        titleElement='h2'
      />
      <div>
        <ScrollVelocity
          numCopies={2}
          items={[
            <div key={0} className='flex overflow-x-hidden ~gap-2/4'>
              {SERVICES.slice(0, SERVICES.length / 2).map((service) => (
                <ServiceCard key={service.title} {...service} />
              ))}
            </div>,
            <div key={1} className='flex overflow-x-hidden ~mt-2/4 ~gap-2/4'>
              {SERVICES.slice(SERVICES.length / 2).map((service, index) => (
                <ServiceCard key={service.title + "-" + index} {...service} />
              ))}
            </div>,
          ]}
        />
      </div>
    </SectionWrapper>
  )
}

export default OurServices
