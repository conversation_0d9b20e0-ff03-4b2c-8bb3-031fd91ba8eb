import { cn } from "@/app/shared/utils"
import { TService } from "@/config/services-data"
import Image from "next/image"

type TServiceCard = TService & {
  className?: string
}

const ServiceCard = ({
  title,
  description,
  image,
  className,
}: TServiceCard) => {
  return (
    <div className={cn("flex-shrink-0 bg-[#ececec] ~w-60/96", className)}>
      <div className='~p-3/4'>
        <span className='group relative inline-block aspect-[16/10] w-full overflow-hidden bg-gray-200'>
          <Image
            src={image}
            alt={title ?? "Service Image"}
            fill
            sizes='(max-width: 768px) 100vw, 33vw'
            loading='lazy'
            draggable={false}
            className='object-cover transition-transform duration-500 group-hover:scale-105'
          />
        </span>
        <p className='inline-flex flex-wrap gap-x-1.5 font-medium uppercase ~text-xl/2xl ~py-1/3'>
          {title.split(" ").map((word, index) => (
            <span
              key={index}
              className={
                index === title.split(" ").length - 1 ? "font-light" : ""
              }
            >
              {word}
            </span>
          ))}
        </p>
        {/* Semantic HTML for SEO */}
        <strong aria-description='Service Name' className='hidden'>
          {title}
        </strong>
        <p
          aria-description='Service Description'
          className='font-inter font-light text-gray-500 ~text-sm/base ~pb-5/10'
        >
          {description}
        </p>
      </div>
    </div>
  )
}
export default ServiceCard
