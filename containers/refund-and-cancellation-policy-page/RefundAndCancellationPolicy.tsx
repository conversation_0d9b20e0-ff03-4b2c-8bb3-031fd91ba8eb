import { TextBanner } from "@/app/components/common"
import { REFUND_AND_CANCELLATION_POLICY } from "@/config"
import {
  PageHeader,
  SectionTitle,
  SectionWrapper,
} from "@/app/shared/components"

const RefundAndCancellationPolicy = () => {
  return (
    <>
      <PageHeader secondLine='Refund and Cancellation Policy' />
      <SectionWrapper className='font-inter !leading-tight text-zinc-400 ~text-sm/lg'>
        <SectionTitle
          title='Refund and Cancellation Policy'
          subtitle='LEGAL'
          className='text-black'
        />
        <p>{REFUND_AND_CANCELLATION_POLICY.introduction}</p>
        <div className='~mt-5/7 ~space-y-5/7'>
          {REFUND_AND_CANCELLATION_POLICY.data.map((data, index) => (
            <div key={index}>
              <p className='uppercase text-primary ~mb-3/5'>{data.title}</p>
              <ul className='list-disc'>
                {data.contentList.map((list, index) => (
                  <li className='ml-5' key={index}>
                    {list}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
        <div className='~mt-5/7 ~mb-8/10 ~space-y-3/5'>
          <p>{REFUND_AND_CANCELLATION_POLICY.extraContent.title}</p>
          <p>{REFUND_AND_CANCELLATION_POLICY.extraContent.content}</p>
        </div>
      </SectionWrapper>
      <TextBanner
        text='Thank you for choosing Nex Move. We appreciate your understanding and are here to assist with any questions or concerns regarding our refund and cancellation policy.'
        className='mx-auto max-w-screen-xl'
      />
    </>
  )
}

export default RefundAndCancellationPolicy
