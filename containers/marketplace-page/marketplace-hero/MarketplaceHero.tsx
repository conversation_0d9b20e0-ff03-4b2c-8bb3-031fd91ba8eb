import { Button } from "@/app/components/ui"
import { SectionWrapper } from "@/app/shared/components"
import Image from "next/image"
import Link from "next/link"

const MarketplaceHero = () => {
  return (
    <SectionWrapper className='!px-0'>
      <div className='relative min-h-[60vh] overflow-hidden bg-black'>
        <div className='absolute inset-0'>
          <Image
            src='/images/about-banner.webp'
            alt='Join Our Marketplace'
            fill
            className='object-cover opacity-20'
            priority
          />
        </div>
        <div className='relative z-10 mx-auto max-w-screen-xl ~px-4/16 ~py-16/24'>
          <div className='grid items-center gap-8 lg:grid-cols-2'>
            <div className='~space-y-4/6'>
              <span className='block font-inter font-medium uppercase text-zinc-100 ~text-xs/sm'>
                Service Provider Portal
              </span>
              <h1 className='font-bold leading-tight text-primary ~text-3xl/5xl'>
                Join Our
                <span className='block text-white'>Marketplace</span>
              </h1>
              <p className='max-w-md leading-relaxed text-zinc-200 ~text-base/lg'>
                Partner with NexMove and expand your business reach. Connect
                with customers who need reliable logistics and moving services
                across the country.
              </p>
              <div className='flex flex-col gap-4 ~pt-2/4 sm:flex-row'>
                <div className='flex gap-4'>
                  <Link href='#register'>
                    <Button>Register Now</Button>
                  </Link>
                  <Link href='#benefits'>
                    <Button className='border border-zinc-400 bg-transparent text-zinc-100 hover:bg-zinc-100 hover:text-zinc-900'>
                      Learn More
                    </Button>
                  </Link>
                </div>
                {/* <div className='flex gap-4'>
                  <Link href='/marketplace/demo-seller'>
                    <Button className='bg-blue-600 text-white hover:bg-blue-700'>
                      Seller Dashboard
                    </Button>
                  </Link>
                </div> */}
              </div>
            </div>
            <div className='relative'>
              <div className='aspect-[4/3] overflow-hidden rounded-lg bg-white shadow-lg'>
                <Image
                  src='/services-image/workplace-moving.webp'
                  alt='Service Provider'
                  fill
                  className='object-cover'
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </SectionWrapper>
  )
}

export default MarketplaceHero
