import { SectionTitle, SectionWrapper } from "@/app/shared/components"
import { HOW_IT_WORKS_STEPS } from "@/config"

const HowItWorks = () => {
  return (
    <SectionWrapper className='~pt-14/20'>
      <SectionTitle subtitle='Simple Process' title='How It Works' />
      <div className='grid ~gap-2/4 ~pt-3/8 md:grid-cols-3'>
        {HOW_IT_WORKS_STEPS.map((step, index) => (
          <div key={index} className='relative text-center'>
            {/* Step connector line for desktop */}
            {index < HOW_IT_WORKS_STEPS.length - 1 && (
              <div className='absolute left-1/2 top-6 hidden h-0.5 w-full bg-zinc-200 md:block'>
                {/* <div className='absolute right-0 top-1/2 h-2 w-2 -translate-y-1/2 rounded-full bg-zinc-300'></div> */}
              </div>
            )}

            <div className='relative z-10 mx-auto mb-4 inline-flex h-12 w-12 items-center justify-center rounded-full bg-primary font-bold text-white ~text-lg/xl'>
              {step.step}
            </div>

            <h3 className='font-medium ~text-lg/xl ~mb-2/3'>{step.title}</h3>
            <p className='mx-auto max-w-sm leading-relaxed text-zinc-500 ~text-sm/base'>
              {step.description}
            </p>
          </div>
        ))}
      </div>
    </SectionWrapper>
  )
}

export default HowItWorks
