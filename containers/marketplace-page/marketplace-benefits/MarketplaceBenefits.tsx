import { SectionTitle, SectionWrapper } from "@/app/shared/components"
import { MARKETPLACE_BENEFITS } from "@/config"

const MarketplaceBenefits = () => {
  return (
    <SectionWrapper className='~pt-14/20'>
      <div id='benefits'>
        <SectionTitle
          subtitle='Why Partner With Us'
          title='Benefits of Joining Our Marketplace'
        />
        <div className='grid gap-8 sm:grid-cols-2 lg:grid-cols-4'>
          {MARKETPLACE_BENEFITS.map((benefit, index) => (
            <div
              key={index}
              className='group rounded-lg bg-zinc-100 p-6 text-center shadow-sm transition-all duration-300 hover:scale-105 hover:shadow-md'
            >
              <div className='mx-auto mb-4 inline-flex h-16 w-16 items-center justify-center rounded-full bg-zinc-200 transition-colors duration-300 group-hover:bg-primary/10'>
                <benefit.icon className='h-8 w-8 text-zinc-600 transition-colors duration-300 group-hover:text-primary' />
              </div>
              <h3 className='mb-3 text-lg font-semibold text-zinc-800 md:text-xl'>
                {benefit.title}
              </h3>
              <p className='text-sm leading-relaxed text-zinc-600 md:text-base'>
                {benefit.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </SectionWrapper>
  )
}

export default MarketplaceBenefits
