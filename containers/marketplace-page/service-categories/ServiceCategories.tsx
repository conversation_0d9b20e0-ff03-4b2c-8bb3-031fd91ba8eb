import { SectionTitle, SectionWrapper } from "@/app/shared/components"
import { SERVICE_CATEGORIES } from "@/config"
import Image from "next/image"

const ServiceCategories = () => {
  return (
    <SectionWrapper className='~pt-14/20'>
      <SectionTitle
        subtitle='Service Categories'
        title='Choose Your Area of Expertise'
      />
      <div className='grid gap-8 md:grid-cols-2'>
        {SERVICE_CATEGORIES.map((category, index) => (
          <div
            key={index}
            className='group overflow-hidden rounded-lg bg-zinc-100 transition-all duration-300 hover:shadow-xl'
          >
            <div className='relative aspect-[16/10] overflow-hidden'>
              <Image
                src={category.image}
                alt={category.title}
                fill
                className='object-cover transition-transform duration-500 group-hover:scale-105'
              />
            </div>
            <div className='p-6'>
              <h3 className='mb-3 text-xl font-semibold text-zinc-800'>
                {category.title}
              </h3>
              <p className='mb-4 text-base leading-relaxed text-zinc-600'>
                {category.description}
              </p>
              <div className='space-y-2'>
                <p className='text-xs font-medium uppercase tracking-wide text-zinc-500'>
                  Popular Services:
                </p>
                <div className='flex flex-wrap gap-2'>
                  {category.services.map((service, serviceIndex) => (
                    <span
                      key={serviceIndex}
                      className='rounded-full bg-zinc-200 px-3 py-1 text-xs font-medium text-zinc-700'
                    >
                      {service}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </SectionWrapper>
  )
}

export default ServiceCategories
