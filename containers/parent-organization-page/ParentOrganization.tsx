import { TextBanner } from "@/app/components/common"
import { PARENT_ORGANIZATION } from "@/config"
import {
  <PERSON><PERSON><PERSON><PERSON>,
  SectionTitle,
  SectionWrapper,
} from "@/app/shared/components"

const ParentOrganization = () => {
  return (
    <>
      <PageHeader secondLine='PARENT ORGANIZATION / LEGAL ENTITY' />
      <SectionWrapper className='font-inter !leading-tight text-zinc-400 ~text-sm/lg'>
        <SectionTitle
          title='PARENT ORGANIZATION / LEGAL ENTITY'
          subtitle='LEGAL'
          className='text-black'
        />
        <p>{PARENT_ORGANIZATION.title}</p>
        <div className='~mt-5/7 ~space-y-5/7'>
          {PARENT_ORGANIZATION.data.map((data, index) => (
            <div key={index}>
              <p className='uppercase text-primary ~mb-3/5'>{data.title}</p>
              <p>{data.content}</p>
            </div>
          ))}
        </div>
      </SectionWrapper>
      <TextBanner
        text='Thank you for choosing Nex Move. We appreciate your understanding and are here to assist with any questions or concerns regarding our Parent Organization / Legal Entity.'
        className='mx-auto max-w-screen-xl'
      />
    </>
  )
}

export default ParentOrganization
