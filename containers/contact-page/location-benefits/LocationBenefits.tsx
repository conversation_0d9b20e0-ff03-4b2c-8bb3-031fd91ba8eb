import { SectionTit<PERSON>, SectionW<PERSON><PERSON> } from "@/app/shared/components"
import { cn } from "@/app/shared/utils"
import { LOCATION_BENEFITS, TLocationBenefit } from "@/config"

const LocationBenefits = () => {
  return (
    <SectionWrapper>
      <SectionTitle
        subtitle='Why Choose NexMove in These Locations?'
        title='Wherever you are in NexMove is here to make your logistics, relocation, and transportation needs easy and stress-free. Contact us today to get started!'
        titleElement='h6'
      />
      <div className='max-sm:jus mx-auto grid max-w-screen-lg ~mt-3/5 ~gap-2/4 sm:grid-cols-2'>
        {LOCATION_BENEFITS.map((benefits, idx) => (
          <BenefitCard {...benefits} key={idx} />
        ))}
      </div>
    </SectionWrapper>
  )
}

export default LocationBenefits

// TODO: Move in another folder
const BenefitCard = (props: TLocationBenefit) => {
  const shouldScaleIcon = props.label === LOCATION_BENEFITS[0].label
  return (
    <div className='grid rounded-lg bg-zinc-100 ~gap-2/3 ~px-5/8 ~py-8/16 max-sm:text-center'>
      <props.icon
        aria-label={props.label + " Icon"}
        className={cn(
          "~size-7/8 max-sm:mx-auto",
          shouldScaleIcon && "scale-[1.15]",
        )}
      />
      <p aria-label={props.label} className='~text-lg/xl'>
        {props.label}
      </p>
    </div>
  )
}
