import ContactForm from "@/app/components/forms/ContactForm"
import { <PERSON><PERSON><PERSON><PERSON>, SectionWrapper } from "@/app/shared/components"
import { EmailIcon, MapPinHouseIcon, PhoneIcon } from "@/Icons"

const ContactSection = () => {
  return (
    <SectionWrapper aria-label='Contact Us Section' className='~pt-14/20'>
      <SectionTitle
        subtitle='GET QUOTE'
        title='Fill out the form below, or use any of the contact methods listed. Our team will respond promptly to ensure all your needs are met with the utmost care and professionalism.'
        className='!px-0'
        titleElement={"h3"}
      />
      <ContactForm />
      <ReachOut />
    </SectionWrapper>
  )
}

export default ContactSection

// TODO: Move in another folder
const ReachOut = () => {
  return (
    <div className='bg-zinc-100 ~mt-10/12 ~px-5/20 ~py-5/8'>
      <h6 className='font-semibold uppercase ~text-sm/base'>Reach us out</h6>
      <div className='mt-4 grid font-inter ~gap-4/8 sm:grid-cols-2 md:grid-cols-3'>
        <div className='flex ~gap-2/3'>
          <MapPinHouseIcon className='flex-shrink-0' />
          <div className='grid h-fit text-sm ~gap-0/1'>
            <span className='text-primary'>Visit Us</span>
            <span className='text-zinc-400'>
              18, ITC Info Tech Park, <br />
              Bangalore, Karnataka, IN 560005
            </span>
          </div>
        </div>
        <div className='flex h-fit text-sm ~gap-2/3'>
          <PhoneIcon className='flex-shrink-0' />
          <div className='grid ~gap-0/1'>
            <span className='text-primary'>Call Us</span>
            <span className='text-zinc-400'>
              TOLL FREE 1800 419 5949 <br />
              LANDLINE : 080-4202-4367/ 2991 5864
            </span>
          </div>
        </div>
        <div className='flex h-fit text-sm ~gap-2/3'>
          <EmailIcon className='flex-shrink-0' />
          <div className='grid ~gap-0/1'>
            <span className='text-primary'>Email Us</span>
            <span className='text-zinc-400'><EMAIL></span>
          </div>
        </div>
      </div>
    </div>
  )
}
