"use client"
import { <PERSON><PERSON> } from "@/app/components/ui"
import { Section<PERSON><PERSON><PERSON>, SplitView, StepProgress } from "@/app/shared/components"
import { Wrapper } from "@/app/shared/components/Wrapper"
import { SUPPORT_OPTIONS } from "@/config"
import Link from "next/link"

const SupportOptions = () => {
  return (
    <div>
      <Wrapper>
        <SectionTitle
          subtitle='Contact Us'
          title='We&rsquo;re here to assist you with all your logistics, transportation, and specialized service needs. Whether you have a question, need a quote, or require immediate assistance, our team is ready to help.'
          className='~pb-8/12'
          titleElement={"h2"}
        />
      </Wrapper>
      <SplitView
        image='/images/shipment.webp'
        labelBoldText='NEX'
        labelText='MOVE'
        className='[&>div:first-child_img]:object-[center_top]'
      >
        <div>
          <strong className='uppercase ~text-lg/xl ~pb-5/8'>
            How can we help you?
          </strong>
          <div className='relative isolate grid ~gap-5/10'>
            {SUPPORT_OPTIONS.map((step) => (
              <StepProgress key={step.number} {...step} />
            ))}

            {/* steps bottom line */}
            <span className='absolute bottom-[10%] top-[10%] -z-10 border-l-[1px] border-dashed border-gray-500/20 ~left-5/6 sm:border-l-2'></span>
          </div>
          <Link href={"/services"}>
            <Button
              aria-label='Get Instant Quote Button'
              className='w-full ~mt-8/16'
            >
              Get Instant Quote
            </Button>
          </Link>
        </div>
      </SplitView>
    </div>
  )
}

export default SupportOptions
