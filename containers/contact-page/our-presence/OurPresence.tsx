import { SectionTitle, SectionWrapper } from "@/app/shared/components"

const OurPresence = () => {
  return (
    <SectionWrapper>
      <SectionTitle
        subtitle='Location'
        title='Our Presence'
        titleElement='h4'
      />
      <p
        aria-label='Our Presence'
        className='!leading-tight text-zinc-400 ~text-lg/xl'
      >
        At NexMove, we are committed to providing exceptional logistics,
        relocation, and transportation services across some of India's most
        dynamic cities. With our strong presence in Ahmedabad, Bangalore,
        Chennai, Delhi NCR, Hyderabad, Kolkata, Mumbai, and Pune, we ensure that
        our clients receive reliable, efficient, and cost-effective solutions
        wherever they are.
      </p>
      <p className='!leading-tight ~text-lg/xl ~pt-4/5'>
        18, ITC Info Tech Park, Bangalore, Karnataka, IN 560005.{" "}
        <a
          target='_blank'
          rel='noopener noreferrer'
          aria-label='View Map'
          href='https://www.google.com/maps?q=18,+ITC+Info+Tech+Park,+Bangalore,+Karnataka,+IN+560005.'
          className='text-primary hover:underline'
        >
          View Map
        </a>
      </p>
    </SectionWrapper>
  )
}

export default OurPresence
