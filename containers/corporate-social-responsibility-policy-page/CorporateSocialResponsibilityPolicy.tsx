import { TextBanner } from "@/app/components/common"
import { CORPORATE_SOCIAL_RESPONSIBILITY_POLICY } from "@/config"
import {
  <PERSON>Header,
  SectionTitle,
  SectionWrapper,
} from "@/app/shared/components"

const CorporateSocialResponsibilityPolicy = () => {
  return (
    <>
      <PageHeader secondLine='CORPORATE SOCIAL RESPONSIBILITY (CSR) POLICY' />
      <SectionWrapper className='font-inter !leading-tight text-zinc-400 ~text-sm/lg'>
        <SectionTitle
          title='CORPORATE SOCIAL RESPONSIBILITY (CSR) POLICY'
          subtitle='LEGAL'
          className='text-black'
        />
        <div className='~mt-5/7 ~space-y-5/7'>
          {CORPORATE_SOCIAL_RESPONSIBILITY_POLICY.data.map((data, index) => (
            <div key={index}>
              <p className='uppercase text-primary ~mb-3/5'>{data.title}</p>
              {data.contentList ? (
                <ul className='list-decimal ~space-y-4/6'>
                  {data.contentList.map((list, index) => (
                    <li className='~ml-4/5' key={index}>
                      <p className='font-bold ~mb-3/5'>{list.title}</p>
                      <ul className='list-disc'>
                        {list.list.map((item, index) => (
                          <li className='~ml-4/5' key={index}>
                            {item}
                          </li>
                        ))}
                      </ul>
                    </li>
                  ))}
                </ul>
              ) : (
                <p>{data.content}</p>
              )}
            </div>
          ))}
        </div>
      </SectionWrapper>
      <TextBanner
        text='Thank you for choosing Nex Move. We appreciate your understanding and are here to assist with any questions or concerns regarding our Corporate Social Responsibility Policy.'
        className='mx-auto max-w-screen-xl'
      />
    </>
  )
}

export default CorporateSocialResponsibilityPolicy
