"use client"

import Link from "next/link"
import ServiceDetailsCard from "../../../app/components/service-details-card/ServiceDetailsCard"
import { Button } from "../../../app/components/ui"
import {
  IntroText,
  PageHeader,
  QuickActionCards,
  SectionTitle,
  SectionWrapper,
  SplitView,
  StepProgress,
} from "../../../app/shared/components"
import { Carousel } from "../../../app/shared/components/carousel"
import { NEXMOVE_TRUCKING_DETAILS } from "../../../config/services-data"

const NexMoveTruckingContainer = () => {
  return (
    <main>
      <PageHeader secondLine='NexMove Trucking & Part-Load' />
      <IntroText
        subtitle='SERVICE OVERVIEW'
        description={NEXMOVE_TRUCKING_DETAILS?.description ?? ""}
      />

      <SectionWrapper>
        <SectionTitle
          subtitle='Services'
          title='TRUCKING SERVICES'
          titleElement={"h2"}
        />
        <div>
          <Carousel
            fitContent
            dragFree
            slideNodes={NEXMOVE_TRUCKING_DETAILS?.trucking?.map((service) => (
              <ServiceDetailsCard
                icon={<service.icon />}
                imageUrl={service.image}
                title={service.title}
                description={service.description}
                key={service.title}
              />
            ))}
            actionButtonsContainerClassName='justify-end gap-5 translate-x-0 translate-y-0 top-[108%] [&_button]:~size-14/16'
          />
          <div className='flex ~mt-8/14 ~gap-2/3'>
            <Link
              href={"/services"}
              className='whitespace-nowrap max-sm:basis-full'
            >
              <Button
                size='lg'
                className='w-full whitespace-nowrap max-sm:basis-full'
                aria-label='Get Instant Quote Button'
              >
                Get instant quote
              </Button>
            </Link>
            <Link
              // href={"/track-shipment"}
              className='whitespace-nowrap max-sm:basis-full'
              target='_blank'
              rel='noopener noreferrer'
              href='https://development9p.trackingmore.org/'
            >
              <Button
                size='lg'
                className='w-full whitespace-nowrap bg-zinc-100 text-black max-sm:basis-full'
                aria-label='Track Way Bill Button'
              >
                Track way bill
              </Button>
            </Link>
          </div>
        </div>
      </SectionWrapper>

      <SectionWrapper className='!px-0'>
        <SplitView
          image='/images/pay-now.webp'
          labelBoldText='Why'
          labelText='NexMove Trucking'
        >
          <div>
            <h3 className='uppercase ~text-lg/xl ~pb-5/8'>
              Why Choose NexMove Trucking?
            </h3>
            <div className='relative isolate grid ~gap-5/10'>
              {NEXMOVE_TRUCKING_DETAILS?.whyNexMoveTrucking?.map((step) => (
                <StepProgress
                  key={step.number}
                  {...step}
                  icon={step.icon ?? null}
                />
              ))}

              {/* steps bottom line */}
              <span className='absolute bottom-[10%] top-[10%] -z-10 border-l-[1px] border-dashed border-gray-500/20 ~left-5/6 sm:border-l-2'></span>
            </div>
          </div>
        </SplitView>
      </SectionWrapper>

      <IntroText
        subtitle=''
        description={NEXMOVE_TRUCKING_DETAILS?.description2 ?? ""}
      />

      <QuickActionCards />
    </main>
  )
}

export default NexMoveTruckingContainer
