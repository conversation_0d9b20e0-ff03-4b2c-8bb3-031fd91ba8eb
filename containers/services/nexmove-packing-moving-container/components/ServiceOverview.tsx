import Link from "next/link"
import React from "react"
import ServiceDetailsCard from "../../../../app/components/service-details-card/ServiceDetailsCard"
import { Button } from "../../../../app/components/ui"
import { SectionTitle, SectionWrapper } from "../../../../app/shared/components"
import { Carousel } from "../../../../app/shared/components/carousel"
import { NEXMOVE_PACKING_MOVING_DETAILS } from "../../../../config"

const ServiceOverview: React.FC = () => {
  return (
    <SectionWrapper>
      <SectionTitle subtitle='Services' title='OUR COMPREHENSIVE SERVICES' />

      <div>
        <Carousel
          fitContent
          dragFree
          slideNodes={NEXMOVE_PACKING_MOVING_DETAILS?.children?.map(
            (service) => (
              <ServiceDetailsCard
                icon={<service.icon />}
                imageUrl={service.image}
                title={service.title}
                description={service.description}
                key={service.title}
              />
            ),
          )}
          actionButtonsContainerClassName='justify-end gap-5 translate-x-0 translate-y-0 top-[108%] [&_button]:~size-14/16'
        />
        <div className='flex ~mt-8/14 ~gap-2/3'>
          <Link
            href={"/services/packing-and-moving"}
            className='whitespace-nowrap max-sm:basis-full'
          >
            <Button
              size='lg'
              className='w-full whitespace-nowrap ~text-sm/lg max-sm:basis-full'
            >
              Get instant quote
            </Button>
          </Link>
          <Link href={"/relocation-checklist"} className='w-full'>
            <Button
              size='lg'
              className='bg-zinc-100 text-black ~text-sm/lg max-sm:basis-full'
            >
              Relocation Checklist
            </Button>
          </Link>
        </div>
      </div>
    </SectionWrapper>
  )
}

export default ServiceOverview
