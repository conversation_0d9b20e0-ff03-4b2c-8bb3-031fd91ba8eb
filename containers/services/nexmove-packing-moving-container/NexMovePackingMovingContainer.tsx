"use client"

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON>ction<PERSON><PERSON>,
  SectionWrapper,
  SplitView,
  StepProgress,
} from "../../../app/shared/components"
import { NEXMOVE_PACKING_MOVING_DETAILS } from "../../../config"
import ServiceOverview from "./components/ServiceOverview"

const NexMovePackingMovingContainer = () => {
  return (
    <main>
      <PageHeader secondLine='NexMove Packing & Moving' />

      <IntroText
        subtitle='SERVICE OVERVIEW'
        description={NEXMOVE_PACKING_MOVING_DETAILS?.description ?? ""}
      />

      <ServiceOverview />

      <SectionWrapper className='!px-0'>
        <SplitView
          image='/images/pay-now.webp'
          labelBoldText='Why'
          labelText='Packing & Moving'
        >
          <div>
            <h3 className='uppercase ~text-lg/xl ~pb-5/8'>
              Why Choose NexMove Packing & Moving?
            </h3>
            <div className='relative isolate grid ~gap-5/10'>
              {NEXMOVE_PACKING_MOVING_DETAILS?.whyNexMovePackingMoving?.map(
                (step) => (
                  <StepProgress
                    key={step.number}
                    {...step}
                    icon={step.icon ?? null}
                  />
                ),
              )}

              {/* steps bottom line */}
              <span className='absolute bottom-[10%] top-[10%] -z-10 border-l-[1px] border-dashed border-gray-500/20 ~left-5/6 sm:border-l-2'></span>
            </div>
          </div>
        </SplitView>
      </SectionWrapper>

      <IntroText
        subtitle=''
        description={NEXMOVE_PACKING_MOVING_DETAILS?.description2 ?? ""}
      />

      <QuickActionCards />
    </main>
  )
}

export default NexMovePackingMovingContainer
