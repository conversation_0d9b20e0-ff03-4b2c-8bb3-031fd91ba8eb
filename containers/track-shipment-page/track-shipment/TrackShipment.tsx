"use client"

import { <PERSON><PERSON> } from "@/app/components/ui"
import { SectionWrapper } from "@/app/shared/components"
import { useRouter } from "next/navigation"
import { useForm } from "react-hook-form"

const TrackShipment = () => {
  const { push } = useRouter()
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm()

  const onSubmit = (data: any) => {
    push(`/track-shipment/${data.shipmentId}`)
  }

  // validate shipment id
  const validateShipmentId = (value: string) => {
    if (value.length < 5) {
      return "Shipment ID must be greater than 5 digits"
    }
  }

  return (
    <SectionWrapper>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className='mx-auto max-w-screen-md rounded-2xl bg-zinc-50 ~space-y-8/10 ~p-8/14'
      >
        <h2 className='text-center !leading-tight ~text-2xl/4xl'>
          Enter Shipment ID <br /> to track your order
        </h2>
        <div className='grid font-inter'>
          <label htmlFor='shipment-id' className='font-medium ~mb-1/1.5'>
            Shipment ID (8 to 11 digit code)
          </label>
          <input
            type='text'
            id='shipment-id'
            placeholder='NMC0000000000'
            className='rounded-md border-2 border-zinc-300 bg-inherit uppercase outline-none ~px-3/3.5 ~py-2/2.5 hover:border-zinc-600 focus-visible:border-blue-500'
            {...register("shipmentId", {
              required: "Shipment ID is required",
              validate: validateShipmentId,
            })}
          />
          {errors.shipmentId && (
            <small className='text-red-500 ~mt-0/0.5 ~pl-3.5/4'>
              {errors.shipmentId.message as string}
            </small>
          )}
        </div>
        <Button size='sm' className='mx-auto block w-full max-w-sm'>
          Track Shipment
        </Button>
      </form>
    </SectionWrapper>
  )
}

export default TrackShipment
