import { SectionTitle, SectionWrapper } from "@/app/shared/components"

const HowToTrack = () => {
  return (
    <SectionWrapper>
      <SectionTitle
        subtitle='Track Shipment'
        title='How To Track'
        titleElement={"h3"}
      />
      <div className='grid font-inter ~mt-2/6 ~gap-3/4 max-sm:gap-6 md:grid-cols-2'>
        <p aria-label='How To Track Your Shipment' className='text-zinc-400'>
          Shipments can be tracked for a period of 45 days from the date of
          dispatch. You may track the status of your domestic shipments in
          India, Nepal, Bangladesh and Bhutan and/or international shipments by
          Waybill or Reference number. Enter the required Waybill or Reference
          number by using the TrackTM box on the right of the page.
          International shipments destined to India for delivery through Halef
          International may also be tracked from the date of formal entry into
          India. Single or multiple waybills, both domestic and international,
          may be tracked simultaneously.
        </p>
        <div className='~space-y-3/5'>
          <div>
            <p className='font-medium ~mb-1/2'>
              The following are guidelines on how to use our TrackTM:
            </p>
            <p className='text-zinc-400'>
              If you have shipped through Halef International, you will have
              received a copy of your Waybill. Each waybill has a distinct
              number which can vary between 8 to 11 digits.
            </p>
          </div>
          <div>
            <p className='font-medium ~mb-1/2'>To track your shipment:</p>
            <ul className='list-decimal pl-5 text-zinc-400'>
              <li>
                Select the Waybill option below TrackTM on the upper, right side
                of this page.
              </li>
              <li>Enter your waybill number in the box provided.</li>
              <li>
                If you wish to track more than 1 waybill, separate each waybill
                number by a comma.
              </li>
              <li>
                After entering your waybill number, click on GO to receive the
                latest update on the status of your shipment.
              </li>
            </ul>
          </div>
        </div>
      </div>
    </SectionWrapper>
  )
}

export default HowToTrack
