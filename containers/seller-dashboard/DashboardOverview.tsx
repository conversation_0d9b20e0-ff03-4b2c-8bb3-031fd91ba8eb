"use client"

import { SellerDashboardService } from "@/app/shared/services/marketplace/seller-dashboard-service"
import { TOrder, TProduct, TSellerStats } from "@/config/seller-dashboard"

import Image from "next/image"
import { useEffect, useState } from "react"

export default function DashboardOverview() {
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState<TSellerStats | null>(null)
  const [recentOrders, setRecentOrders] = useState<TOrder[]>([])
  const [topProducts, setTopProducts] = useState<TProduct[]>([])

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        const data = await SellerDashboardService.getDashboardSummary()
        setStats(data.stats)
        setRecentOrders(data.recentOrders)
        setTopProducts(data.topProducts)
      } catch (error) {
        console.error("Error fetching dashboard data:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchDashboardData()
  }, [])

  if (loading) {
    return (
      <div className='animate-pulse space-y-8'>
        {/* Header Skeleton */}
        <div>
          <div className='mb-2 h-8 w-48 rounded-lg bg-zinc-200'></div>
          <div className='h-5 w-80 rounded bg-zinc-100'></div>
        </div>

        {/* Stats Cards Skeleton */}
        <div className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4'>
          {[...Array(4)].map((_, i) => (
            <div
              key={i}
              className='rounded-xl border border-zinc-200 bg-white p-6 shadow-sm'
            >
              <div className='flex items-center'>
                <div className='h-12 w-12 rounded-lg bg-zinc-200'></div>
                <div className='ml-4 space-y-2'>
                  <div className='h-4 w-20 rounded bg-zinc-200'></div>
                  <div className='h-6 w-16 rounded bg-zinc-300'></div>
                  <div className='h-3 w-14 rounded bg-zinc-100'></div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Content Sections Skeleton */}
        <div className='grid grid-cols-1 gap-8 lg:grid-cols-2'>
          {[...Array(2)].map((_, i) => (
            <div
              key={i}
              className='rounded-xl border border-zinc-200 bg-white shadow-sm'
            >
              <div className='border-b border-zinc-200 bg-zinc-50/50 px-6 py-4'>
                <div className='h-6 w-32 rounded bg-zinc-200'></div>
              </div>
              <div className='space-y-4 p-6'>
                {[...Array(3)].map((_, j) => (
                  <div
                    key={j}
                    className='flex items-center justify-between rounded-lg border border-zinc-200 p-4'
                  >
                    <div className='flex items-center space-x-4'>
                      <div className='h-12 w-12 rounded-lg bg-zinc-200'></div>
                      <div className='space-y-2'>
                        <div className='h-4 w-24 rounded bg-zinc-200'></div>
                        <div className='h-3 w-16 rounded bg-zinc-100'></div>
                      </div>
                    </div>
                    <div className='space-y-2 text-right'>
                      <div className='h-4 w-16 rounded bg-zinc-200'></div>
                      <div className='h-3 w-12 rounded bg-zinc-100'></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (!stats) {
    return (
      <div className='py-12 text-center'>
        <p className='text-gray-500'>Unable to load dashboard data</p>
      </div>
    )
  }

  const statCards = [
    {
      title: "Total Products",
      value: stats.totalProducts,
      description: `${stats.activeProducts} active`,
      color: "bg-primary",
    },
    {
      title: "Total Orders",
      value: stats.totalOrders,
      description: `${stats.pendingOrders} pending`,
      color: "bg-secondary",
    },
    {
      title: "Monthly Revenue",
      value: `₹${stats.monthlyRevenue.toLocaleString()}`,
      description: "This month",
      color: "bg-zinc-600",
    },
    {
      title: "Average Rating",
      value: stats.averageRating,
      description: `${stats.totalViews} total views`,
      color: "bg-primary/80",
    },
  ]

  const getStatusColor = (status: TOrder["status"]) => {
    switch (status) {
      case "pending":
        return "bg-amber-100 text-amber-800 border-amber-200"
      case "confirmed":
        return "bg-primary/10 text-primary border-primary/20"
      case "in-progress":
        return "bg-blue-100 text-blue-800 border-blue-200"
      case "completed":
        return "bg-green-100 text-green-800 border-green-200"
      case "cancelled":
        return "bg-red-100 text-red-800 border-red-200"
      default:
        return "bg-zinc-100 text-zinc-800 border-zinc-200"
    }
  }

  return (
    <div className='space-y-8'>
      {/* Header */}
      <div>
        <h1 className='text-2xl font-bold text-zinc-900'>Dashboard</h1>
        <p className='text-zinc-600'>
          Welcome back! Here's an overview of your marketplace performance.
        </p>
      </div>

      {/* Stats Cards */}
      <div className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4'>
        {statCards.map((card, index) => (
          <div
            key={index}
            className='overflow-hidden rounded-xl border border-zinc-200 bg-white p-6 shadow-sm transition-all duration-200 hover:border-primary/20 hover:shadow-md'
          >
            <div className='flex items-center'>
              <div className={`${card.color} rounded-lg p-3 shadow-sm`}>
                <div className='flex h-6 w-6 items-center justify-center text-white'>
                  📊
                </div>
              </div>
              <div className='ml-4'>
                <p className='text-sm font-medium text-zinc-600'>
                  {card.title}
                </p>
                <p className='text-2xl font-bold text-zinc-900'>{card.value}</p>
                <p className='text-sm text-zinc-500'>{card.description}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className='grid grid-cols-1 gap-8 lg:grid-cols-2'>
        {/* Recent Orders */}
        <div className='overflow-hidden rounded-xl border border-zinc-200 bg-white shadow-sm'>
          <div className='border-b border-zinc-200 bg-zinc-50/50 px-6 py-4'>
            <h2 className='text-lg font-semibold text-zinc-900'>
              Recent Orders
            </h2>
          </div>
          <div className='p-6'>
            {recentOrders.length === 0 ? (
              <p className='py-4 text-center text-zinc-500'>No recent orders</p>
            ) : (
              <div className='space-y-4'>
                {recentOrders.map((order) => (
                  <div
                    key={order.id}
                    className='flex items-center justify-between rounded-lg border border-zinc-200 p-4 transition-colors hover:bg-zinc-50'
                  >
                    <div className='flex-1'>
                      <p className='font-medium text-zinc-900'>
                        {order.productName}
                      </p>
                      <p className='text-sm text-zinc-600'>
                        {order.customerName}
                      </p>
                      <p className='text-sm text-zinc-500'>
                        ₹{order.totalAmount.toLocaleString()}
                      </p>
                    </div>
                    <div className='text-right'>
                      <span
                        className={`inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-medium ${getStatusColor(order.status)}`}
                      >
                        {order.status}
                      </span>
                      <p className='mt-1 text-xs text-zinc-500'>
                        {new Date(order.orderDate).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Top Products */}
        <div className='overflow-hidden rounded-xl border border-zinc-200 bg-white shadow-sm'>
          <div className='border-b border-zinc-200 bg-zinc-50/50 px-6 py-4'>
            <h2 className='text-lg font-semibold text-zinc-900'>
              Top Performing Products
            </h2>
          </div>
          <div className='p-6'>
            {topProducts.length === 0 ? (
              <p className='py-4 text-center text-zinc-500'>
                No products available
              </p>
            ) : (
              <div className='space-y-4'>
                {topProducts.map((product) => (
                  <div
                    key={product.id}
                    className='flex items-center justify-between rounded-lg border border-zinc-200 p-4 transition-colors hover:bg-zinc-50'
                  >
                    <div className='flex items-center space-x-4'>
                      <Image
                        src={product.images[0] || "/placeholder-product.jpg"}
                        alt={product.name}
                        width={48}
                        height={48}
                        className='h-12 w-12 rounded-lg object-cover ring-2 ring-zinc-100'
                      />
                      <div>
                        <p className='font-medium text-zinc-900'>
                          {product.name}
                        </p>
                        <p className='text-sm text-zinc-600'>
                          {product.category}
                        </p>
                      </div>
                    </div>
                    <div className='text-right'>
                      <p className='font-semibold text-zinc-900'>
                        ₹{product.price.toLocaleString()}
                      </p>
                      <p className='text-sm text-zinc-500'>
                        {product.sales} sales
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
