"use client"

import {
  ArrowRightIcon,
  BoxesIcon,
  CloseIcon,
  HomeIcon,
  MenuIcon,
  PersonIcon,
} from "@/Icons"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useState } from "react"

interface SellerDashboardLayoutProps {
  children: React.ReactNode
  sellerId?: string
}

export default function SellerDashboardLayout({
  children,
  sellerId = "demo-seller",
}: SellerDashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const pathname = usePathname()

  const navigation = [
    {
      name: "Dashboard",
      href: `/marketplace/${sellerId}`,
      icon: HomeIcon,
    },
    {
      name: "Products",
      href: `/marketplace/${sellerId}/products`,
      icon: BoxesIcon,
    },
    {
      name: "Orders",
      href: `/marketplace/${sellerId}/orders`,
      icon: ArrowRightIcon,
    },
  ]

  return (
    <div className='min-h-screen bg-zinc-50'>
      {/* Sidebar for mobile */}
      <div
        className={`fixed inset-0 z-40 lg:hidden ${sidebarOpen ? "block" : "hidden"}`}
      >
        <div
          className='fixed inset-0 bg-zinc-900 bg-opacity-75 transition-opacity'
          onClick={() => setSidebarOpen(false)}
        />

        <div className='relative flex w-full max-w-xs flex-1 flex-col bg-white shadow-xl'>
          <div className='absolute right-0 top-0 -mr-12 pt-2'>
            <button
              type='button'
              className='ml-1 flex h-10 w-10 items-center justify-center rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white'
              onClick={() => setSidebarOpen(false)}
            >
              <CloseIcon className='h-6 w-6 text-white' />
            </button>
          </div>

          <div className='h-0 flex-1 overflow-y-auto pb-4 pt-5'>
            <div className='flex items-center px-4'>
              <h2 className='text-lg font-semibold text-zinc-900'>
                Seller Dashboard
              </h2>
            </div>
            <nav className='mt-5 space-y-1 px-2'>
              {navigation.map((item) => {
                const Icon = item.icon
                const isActive = pathname === item.href
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={`group flex items-center rounded-lg px-3 py-2.5 text-sm font-medium transition-all duration-200 ${
                      isActive
                        ? "bg-primary text-white shadow-sm"
                        : "text-zinc-600 hover:bg-zinc-100 hover:text-zinc-900"
                    }`}
                  >
                    <Icon
                      className={`mr-3 h-5 w-5 transition-colors ${isActive ? "text-white" : "text-zinc-400 group-hover:text-zinc-600"}`}
                    />
                    {item.name}
                  </Link>
                )
              })}
            </nav>
          </div>
        </div>
      </div>

      {/* Static sidebar for desktop */}
      <div className='hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col'>
        <div className='flex min-h-0 flex-1 flex-col border-r border-zinc-200 bg-white shadow-sm'>
          <div className='flex flex-1 flex-col overflow-y-auto pb-4 pt-5'>
            <div className='flex items-center px-4'>
              <h2 className='text-lg font-semibold text-zinc-900'>
                Seller Dashboard
              </h2>
            </div>
            <nav className='mt-5 flex-1 space-y-1 px-2'>
              {navigation.map((item) => {
                const Icon = item.icon
                const isActive = pathname === item.href
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={`group flex items-center rounded-lg px-3 py-2.5 text-sm font-medium transition-all duration-200 ${
                      isActive
                        ? "bg-primary text-white shadow-sm"
                        : "text-zinc-600 hover:bg-zinc-100 hover:text-zinc-900"
                    }`}
                  >
                    <Icon
                      className={`mr-3 h-5 w-5 transition-colors ${isActive ? "text-white" : "text-zinc-400 group-hover:text-zinc-600"}`}
                    />
                    {item.name}
                  </Link>
                )
              })}
            </nav>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className='flex flex-1 flex-col lg:pl-64'>
        {/* Top navigation */}
        <div className='sticky top-0 z-10 border-b border-zinc-200 bg-white/95 backdrop-blur-sm lg:border-none'>
          <div className='px-4 sm:px-6 lg:px-8'>
            <div className='flex h-16 items-center justify-between'>
              <button
                type='button'
                className='border-r border-zinc-200 px-4 text-zinc-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary lg:hidden'
                onClick={() => setSidebarOpen(true)}
              >
                <MenuIcon className='h-6 w-6' />
              </button>

              <div className='flex items-center gap-x-4 lg:gap-x-6'>
                <button
                  type='button'
                  className='p-2 text-zinc-400 transition-colors hover:text-zinc-600'
                >
                  <span className='text-sm'>🔔</span>
                </button>

                <div className='hidden lg:block lg:h-6 lg:w-px lg:bg-zinc-200' />

                <div className='flex items-center gap-x-2'>
                  <div className='flex h-8 w-8 items-center justify-center rounded-full bg-primary/10'>
                    <PersonIcon className='h-5 w-5 text-primary' />
                  </div>
                  <span className='hidden text-sm font-semibold text-zinc-900 lg:block'>
                    Seller Account
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className='flex-1'>
          <div className='px-4 py-8 sm:px-6 lg:px-8'>{children}</div>
        </main>
      </div>
    </div>
  )
}
