"use client"

import { MOCK_PRODUCT_CATEGORIES, TProduct } from "@/config/seller-dashboard"
import { CloseIcon } from "@/Icons"
import { SellerDashboardService } from "@/lib/seller-dashboard-service"
import Image from "next/image"
import { useEffect, useState } from "react"

interface ProductFormModalProps {
  isOpen: boolean
  onClose: () => void
  product?: TProduct | null
  onSave: (product: TProduct) => void
}

export default function ProductFormModal({
  isOpen,
  onClose,
  product,
  onSave,
}: ProductFormModalProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    category: "",
    subcategory: "",
    description: "",
    price: 0,
    stock: 0,
    sku: "",
    status: "draft" as TProduct["status"],
  })
  const [selectedImages, setSelectedImages] = useState<File[]>([])
  const [imageUrls, setImageUrls] = useState<string[]>([])

  // Reset form when modal opens/closes or product changes
  useEffect(() => {
    if (isOpen) {
      if (product) {
        // Edit mode
        setFormData({
          name: product.name,
          category: product.category,
          subcategory: product.subcategory,
          description: product.description,
          price: product.price,
          stock: product.stock,
          sku: product.sku,
          status: product.status,
        })
        setImageUrls(product.images)
        setSelectedImages([])
      } else {
        // Add mode
        setFormData({
          name: "",
          category: "",
          subcategory: "",
          description: "",
          price: 0,
          stock: 0,
          sku: "",
          status: "draft",
        })
        setImageUrls([])
        setSelectedImages([])
      }
    }
  }, [isOpen, product])

  const selectedCategory = MOCK_PRODUCT_CATEGORIES.find(
    (cat) => cat.name === formData.category,
  )

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >,
  ) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: name === "price" || name === "stock" ? Number(value) : value,
    }))
  }

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    setSelectedImages(files)

    // Create preview URLs
    const urls = files.map((file) => URL.createObjectURL(file))
    setImageUrls(urls)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      let finalImageUrls = imageUrls

      // Upload new images if any
      if (selectedImages.length > 0) {
        finalImageUrls =
          await SellerDashboardService.uploadProductImages(selectedImages)
      }

      const productData = {
        ...formData,
        images: finalImageUrls,
      }

      let savedProduct: TProduct
      if (product) {
        // Update existing product
        savedProduct = await SellerDashboardService.updateProduct(
          product.id,
          productData,
        )
      } else {
        // Create new product
        savedProduct = await SellerDashboardService.createProduct(productData)
      }

      onSave(savedProduct)
    } catch (error) {
      console.error("Error saving product:", error)
      alert("Failed to save product. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className='fixed inset-0 z-50 overflow-y-auto'>
      <div className='flex min-h-screen items-center justify-center p-4'>
        {/* Backdrop */}
        <div
          className='fixed inset-0 bg-black bg-opacity-50'
          onClick={onClose}
        />

        {/* Modal */}
        <div className='relative max-h-[90vh] w-full max-w-4xl overflow-y-auto rounded-lg bg-white shadow-xl'>
          {/* Header */}
          <div className='flex items-center justify-between border-b border-gray-200 p-6'>
            <h2 className='text-xl font-semibold text-gray-900'>
              {product ? "Edit Product" : "Add New Product"}
            </h2>
            <button
              onClick={onClose}
              className='rounded-md p-2 hover:bg-gray-100'
            >
              <CloseIcon className='h-5 w-5 text-gray-500' />
            </button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className='space-y-6 p-6'>
            {/* Basic Information */}
            <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
              <div>
                <label
                  htmlFor='name'
                  className='mb-2 block text-sm font-medium text-gray-700'
                >
                  Product Name *
                </label>
                <input
                  type='text'
                  id='name'
                  name='name'
                  required
                  value={formData.name}
                  onChange={handleInputChange}
                  className='w-full rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500'
                  placeholder='Enter product name'
                />
              </div>

              <div>
                <label
                  htmlFor='sku'
                  className='mb-2 block text-sm font-medium text-gray-700'
                >
                  SKU *
                </label>
                <input
                  type='text'
                  id='sku'
                  name='sku'
                  required
                  value={formData.sku}
                  onChange={handleInputChange}
                  className='w-full rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500'
                  placeholder='Enter SKU'
                />
              </div>
            </div>

            {/* Category Selection */}
            <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
              <div>
                <label
                  htmlFor='category'
                  className='mb-2 block text-sm font-medium text-gray-700'
                >
                  Category *
                </label>
                <select
                  id='category'
                  name='category'
                  required
                  value={formData.category}
                  onChange={(e) => {
                    handleInputChange(e)
                    setFormData((prev) => ({ ...prev, subcategory: "" }))
                  }}
                  className='w-full rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500'
                >
                  <option value=''>Select Category</option>
                  {MOCK_PRODUCT_CATEGORIES.map((category) => (
                    <option key={category.id} value={category.name}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label
                  htmlFor='subcategory'
                  className='mb-2 block text-sm font-medium text-gray-700'
                >
                  Subcategory *
                </label>
                <select
                  id='subcategory'
                  name='subcategory'
                  required
                  value={formData.subcategory}
                  onChange={handleInputChange}
                  disabled={!selectedCategory}
                  className='w-full rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100'
                >
                  <option value=''>Select Subcategory</option>
                  {selectedCategory?.subcategories.map((subcategory) => (
                    <option key={subcategory} value={subcategory}>
                      {subcategory}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Description */}
            <div>
              <label
                htmlFor='description'
                className='mb-2 block text-sm font-medium text-gray-700'
              >
                Description *
              </label>
              <textarea
                id='description'
                name='description'
                required
                rows={4}
                value={formData.description}
                onChange={handleInputChange}
                className='w-full rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500'
                placeholder='Describe your product...'
              />
            </div>

            {/* Pricing and Stock */}
            <div className='grid grid-cols-1 gap-6 md:grid-cols-3'>
              <div>
                <label
                  htmlFor='price'
                  className='mb-2 block text-sm font-medium text-gray-700'
                >
                  Price (₹) *
                </label>
                <input
                  type='number'
                  id='price'
                  name='price'
                  required
                  min='0'
                  step='0.01'
                  value={formData.price}
                  onChange={handleInputChange}
                  className='w-full rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500'
                  placeholder='0.00'
                />
              </div>

              <div>
                <label
                  htmlFor='stock'
                  className='mb-2 block text-sm font-medium text-gray-700'
                >
                  Stock Quantity *
                </label>
                <input
                  type='number'
                  id='stock'
                  name='stock'
                  required
                  min='0'
                  value={formData.stock}
                  onChange={handleInputChange}
                  className='w-full rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500'
                  placeholder='0'
                />
              </div>

              <div>
                <label
                  htmlFor='status'
                  className='mb-2 block text-sm font-medium text-gray-700'
                >
                  Status
                </label>
                <select
                  id='status'
                  name='status'
                  value={formData.status}
                  onChange={handleInputChange}
                  className='w-full rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500'
                >
                  <option value='draft'>Draft</option>
                  <option value='active'>Active</option>
                  <option value='inactive'>Inactive</option>
                </select>
              </div>
            </div>

            {/* Image Upload */}
            <div>
              <label
                htmlFor='images'
                className='mb-2 block text-sm font-medium text-gray-700'
              >
                Product Images
              </label>
              <input
                type='file'
                id='images'
                multiple
                accept='image/*'
                onChange={handleImageChange}
                className='w-full rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500'
              />

              {/* Image Preview */}
              {imageUrls.length > 0 && (
                <div className='mt-4 grid grid-cols-2 gap-4 md:grid-cols-4'>
                  {imageUrls.map((url, index) => (
                    <div key={index} className='relative'>
                      <Image
                        src={url}
                        alt={`Preview ${index + 1}`}
                        width={96}
                        height={96}
                        className='h-24 w-full rounded-lg border object-cover'
                      />
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Form Actions */}
            <div className='flex justify-end space-x-4 border-t border-gray-200 pt-6'>
              <button
                type='button'
                onClick={onClose}
                className='rounded-md border border-gray-300 px-6 py-2 text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
              >
                Cancel
              </button>
              <button
                type='submit'
                disabled={loading}
                className='rounded-md bg-blue-600 px-6 py-2 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50'
              >
                {loading
                  ? "Saving..."
                  : product
                    ? "Update Product"
                    : "Create Product"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
