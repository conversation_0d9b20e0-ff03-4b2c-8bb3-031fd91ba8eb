"use client"

import { SellerDashboardService } from "@/app/shared/services/marketplace/seller-dashboard-service"
import { TMarketplaceProduct } from "@/config/seller-dashboard"
import { PlusIcon } from "@/Icons"
import Image from "next/image"
import { useEffect, useState } from "react"
import MarketplaceProductFormModal from "./MarketplaceProductFormModal"

interface ProductsManagementProps {
  sellerId: string
}

export default function ProductsManagement({
  sellerId,
}: ProductsManagementProps) {
  const [products, setProducts] = useState<TMarketplaceProduct[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState<"all" | TMarketplaceProduct["category"]>(
    "all",
  )
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingProduct, setEditingProduct] =
    useState<TMarketplaceProduct | null>(null)

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const data = await SellerDashboardService.getMarketplaceProducts()
        setProducts(data)
      } catch (error) {
        console.error("Error fetching products:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchProducts()
  }, [sellerId])

  const handleDelete = async (productId: string) => {
    if (!confirm("Are you sure you want to delete this product?")) {
      return
    }

    try {
      await SellerDashboardService.deleteMarketplaceProduct(productId)
      setProducts((prev) => prev.filter((product) => product.id !== productId))
    } catch (error) {
      console.error("Error deleting product:", error)
      alert("Failed to delete product")
    }
  }

  const handleEdit = (product: TMarketplaceProduct) => {
    setEditingProduct(product)
    setIsModalOpen(true)
  }

  const handleAdd = () => {
    setEditingProduct(null)
    setIsModalOpen(true)
  }

  const handleProductSaved = (product: TMarketplaceProduct) => {
    if (editingProduct) {
      // Update existing product
      setProducts((prev) =>
        prev.map((p) => (p.id === product.id ? product : p)),
      )
    } else {
      // Add new product
      setProducts((prev) => [...prev, product])
    }
    setIsModalOpen(false)
    setEditingProduct(null)
  }

  const filteredProducts = products.filter((product) => {
    if (filter === "all") return true
    return product.category === filter
  })

  const getCategoryDisplayName = (
    category: TMarketplaceProduct["category"],
  ): string => {
    switch (category) {
      case "electronics-appliances":
        return "Electronics & Appliances"
      case "furniture":
        return "Furniture"
      case "vehicle":
        return "Vehicle"
      case "others":
        return "Others"
      default:
        return "Unknown"
    }
  }

  if (loading) {
    return (
      <div className='space-y-8'>
        <div className='animate-pulse'>
          {/* Header Skeleton */}
          <div className='mb-8 flex flex-col gap-6 sm:flex-row sm:items-center sm:justify-between'>
            <div className='space-y-3'>
              <div className='h-8 w-72 rounded-lg bg-gradient-to-r from-zinc-200 to-zinc-300'></div>
              <div className='h-5 w-48 rounded-lg bg-zinc-100'></div>
            </div>
            <div className='h-12 w-36 rounded-xl bg-gradient-to-r from-primary/20 to-primary/30'></div>
          </div>

          {/* Stats Cards Skeleton */}
          <div className='mb-8 grid grid-cols-2 gap-4 md:grid-cols-4'>
            {[...Array(4)].map((_, i) => (
              <div
                key={i}
                className='rounded-2xl border border-zinc-100 bg-white p-6 shadow-sm'
              >
                <div className='mb-3 h-5 w-20 rounded-lg bg-zinc-200'></div>
                <div className='h-8 w-16 rounded-lg bg-zinc-300'></div>
              </div>
            ))}
          </div>

          {/* Filters Skeleton */}
          <div className='mb-8 flex flex-wrap gap-3'>
            {[...Array(5)].map((_, i) => (
              <div key={i} className='h-11 w-24 rounded-xl bg-zinc-200'></div>
            ))}
          </div>

          {/* Products Grid Skeleton */}
          <div className='grid grid-cols-1 gap-8 md:grid-cols-2 xl:grid-cols-3'>
            {[...Array(6)].map((_, i) => (
              <div
                key={i}
                className='group overflow-hidden rounded-3xl border border-zinc-100 bg-white shadow-sm'
              >
                <div className='h-56 bg-gradient-to-br from-zinc-200 to-zinc-300'></div>
                <div className='space-y-4 p-6'>
                  <div className='flex items-start justify-between gap-3'>
                    <div className='h-6 w-3/4 rounded-lg bg-zinc-200'></div>
                    <div className='h-7 w-20 rounded-full bg-zinc-100'></div>
                  </div>
                  <div className='space-y-2'>
                    <div className='h-4 w-full rounded-lg bg-zinc-100'></div>
                    <div className='h-4 w-4/5 rounded-lg bg-zinc-100'></div>
                    <div className='h-4 w-2/3 rounded-lg bg-zinc-100'></div>
                  </div>
                  <div className='flex items-center justify-between pt-2'>
                    <div className='h-7 w-24 rounded-lg bg-zinc-200'></div>
                    <div className='h-4 w-20 rounded-lg bg-zinc-100'></div>
                  </div>
                  <div className='flex gap-3 pt-2'>
                    <div className='h-11 flex-1 rounded-xl bg-zinc-100'></div>
                    <div className='h-11 w-24 rounded-xl bg-zinc-100'></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className='space-y-8'>
      {/* Header */}
      <div className='flex flex-col gap-6 sm:flex-row sm:items-start sm:justify-between'>
        <div className='space-y-2'>
          <h1 className='text-3xl font-bold text-zinc-900'>
            Marketplace Products
          </h1>
          <p className='text-lg text-zinc-600'>
            Manage and showcase your products to potential buyers
          </p>
        </div>
        <button
          onClick={handleAdd}
          className='group inline-flex items-center gap-3 rounded-2xl bg-gradient-to-r from-primary to-primary/90 px-6 py-4 font-semibold text-white shadow-lg shadow-primary/25 transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-primary/30 focus:outline-none focus:ring-4 focus:ring-primary/20'
        >
          <PlusIcon className='h-5 w-5 transition-transform group-hover:rotate-90' />
          Add Product
        </button>
      </div>

      {/* Stats Cards */}
      <div className='grid grid-cols-2 gap-4 md:grid-cols-4'>
        <div className='rounded-2xl border border-emerald-100 bg-gradient-to-br from-emerald-50 to-emerald-100/50 p-6 shadow-sm'>
          <p className='text-sm font-medium text-emerald-700'>Total Products</p>
          <p className='text-2xl font-bold text-emerald-900'>
            {products.length}
          </p>
        </div>
        <div className='rounded-2xl border border-blue-100 bg-gradient-to-br from-blue-50 to-blue-100/50 p-6 shadow-sm'>
          <p className='text-sm font-medium text-blue-700'>Electronics</p>
          <p className='text-2xl font-bold text-blue-900'>
            {
              products.filter((p) => p.category === "electronics-appliances")
                .length
            }
          </p>
        </div>
        <div className='rounded-2xl border border-purple-100 bg-gradient-to-br from-purple-50 to-purple-100/50 p-6 shadow-sm'>
          <p className='text-sm font-medium text-purple-700'>Furniture</p>
          <p className='text-2xl font-bold text-purple-900'>
            {products.filter((p) => p.category === "furniture").length}
          </p>
        </div>
        <div className='rounded-2xl border border-amber-100 bg-gradient-to-br from-amber-50 to-amber-100/50 p-6 shadow-sm'>
          <p className='text-sm font-medium text-amber-700'>Vehicles</p>
          <p className='text-2xl font-bold text-amber-900'>
            {products.filter((p) => p.category === "vehicle").length}
          </p>
        </div>
      </div>

      {/* Filters */}
      <div className='flex flex-wrap gap-3'>
        {(
          [
            "all",
            "electronics-appliances",
            "furniture",
            "vehicle",
            "others",
          ] as const
        ).map((category) => (
          <button
            key={category}
            onClick={() => setFilter(category)}
            className={`group relative overflow-hidden rounded-2xl px-6 py-3 text-sm font-semibold transition-all duration-300 ${
              filter === category
                ? "scale-105 bg-gradient-to-r from-primary to-primary/90 text-white shadow-lg shadow-primary/25"
                : "border border-zinc-200 bg-white text-zinc-700 shadow-sm hover:scale-105 hover:border-primary/30 hover:bg-primary/5 hover:text-primary"
            }`}
          >
            <span className='relative z-10'>
              {category === "all"
                ? "All Products"
                : getCategoryDisplayName(category)}
            </span>
            <span
              className={`ml-2 inline-flex h-6 min-w-[24px] items-center justify-center rounded-full px-2 text-xs font-bold ${
                filter === category
                  ? "bg-white/20 text-white"
                  : "bg-zinc-100 text-zinc-600 group-hover:bg-primary/10 group-hover:text-primary"
              }`}
            >
              {category === "all"
                ? products.length
                : products.filter((p) => p.category === category).length}
            </span>
          </button>
        ))}
      </div>

      {/* Products Grid */}
      {filteredProducts.length === 0 ? (
        <div className='flex flex-col items-center justify-center rounded-3xl border-2 border-dashed border-zinc-200 bg-gradient-to-br from-zinc-50/50 to-white py-20'>
          <div className='mb-6 flex h-24 w-24 items-center justify-center rounded-full bg-gradient-to-br from-primary/10 to-primary/5 shadow-lg'>
            <span className='text-5xl'>📦</span>
          </div>
          <h3 className='mb-3 text-xl font-bold text-zinc-900'>
            No products found
          </h3>
          <p className='mb-8 max-w-md text-center leading-relaxed text-zinc-600'>
            {filter === "all"
              ? "Start building your product catalog! Add your first product to showcase what you're selling."
              : `No ${getCategoryDisplayName(filter)} products yet. Add some products in this category to get started.`}
          </p>
          <button
            onClick={handleAdd}
            className='group inline-flex items-center gap-3 rounded-2xl bg-gradient-to-r from-primary to-primary/90 px-8 py-4 font-semibold text-white shadow-lg shadow-primary/25 transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-primary/30'
          >
            <PlusIcon className='h-5 w-5 transition-transform group-hover:rotate-90' />
            Add Your First Product
          </button>
        </div>
      ) : (
        <div className='grid grid-cols-1 gap-8 md:grid-cols-2 xl:grid-cols-3'>
          {filteredProducts.map((product) => (
            <div
              key={product.id}
              className='group overflow-hidden rounded-3xl border border-zinc-100 bg-white shadow-sm transition-all duration-500 hover:-translate-y-2 hover:border-primary/20 hover:shadow-2xl hover:shadow-primary/10'
            >
              {/* Product Image */}
              <div className='relative overflow-hidden bg-gradient-to-br from-zinc-50 to-zinc-100'>
                <Image
                  src={product.pictures[0] || "/placeholder-product.jpg"}
                  alt={product.title}
                  width={400}
                  height={280}
                  className='h-56 w-full object-cover transition-all duration-700 group-hover:scale-110'
                />
                <div className='absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent opacity-0 transition-opacity duration-500 group-hover:opacity-100' />
                <div className='absolute right-4 top-4 opacity-0 transition-all duration-300 group-hover:opacity-100'>
                  <span className='inline-flex items-center rounded-full bg-white/90 px-3 py-1.5 text-xs font-semibold text-primary shadow-lg backdrop-blur-sm'>
                    {getCategoryDisplayName(product.category)}
                  </span>
                </div>
              </div>

              {/* Product Info */}
              <div className='space-y-4 p-6'>
                <div className='space-y-2'>
                  <h3 className='line-clamp-2 text-lg font-bold leading-tight text-zinc-900 transition-colors group-hover:text-primary'>
                    {product.title}
                  </h3>
                  <p className='line-clamp-3 text-sm leading-relaxed text-zinc-600'>
                    {product.description}
                  </p>
                </div>

                <div className='flex items-center justify-between pt-2'>
                  <div className='text-2xl font-bold text-zinc-900'>
                    ₹{product.expectingPrice.toLocaleString()}
                  </div>
                  <div className='flex items-center gap-2 text-sm text-zinc-500'>
                    <div className='h-2 w-2 rounded-full bg-emerald-400'></div>
                    <span className='font-medium'>
                      {product.pictures.length} photo
                      {product.pictures.length !== 1 ? "s" : ""}
                    </span>
                  </div>
                </div>

                {/* Actions */}
                <div className='flex gap-3 pt-4'>
                  <button
                    onClick={() => handleEdit(product)}
                    className='flex-1 rounded-2xl bg-gradient-to-r from-primary/10 to-primary/5 px-4 py-3 text-sm font-semibold text-primary transition-all duration-300 hover:scale-105 hover:from-primary/20 hover:to-primary/10 hover:shadow-lg hover:shadow-primary/10'
                  >
                    Edit Product
                  </button>
                  <button
                    onClick={() => handleDelete(product.id)}
                    className='rounded-2xl bg-gradient-to-r from-red-50 to-red-100/50 px-4 py-3 text-sm font-semibold text-red-600 transition-all duration-300 hover:scale-105 hover:from-red-100 hover:to-red-200/50 hover:shadow-lg hover:shadow-red-500/10'
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Product Form Modal */}
      <MarketplaceProductFormModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false)
          setEditingProduct(null)
        }}
        product={editingProduct}
        onSave={handleProductSaved}
      />
    </div>
  )
}
