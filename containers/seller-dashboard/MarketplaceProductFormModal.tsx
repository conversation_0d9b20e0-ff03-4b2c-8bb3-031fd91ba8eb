"use client"

import { SellerDashboardService } from "@/app/shared/services/marketplace/seller-dashboard-service"
import { TMarketplaceProduct } from "@/config/seller-dashboard"
import { useMarketplacePictureUpload } from "@/hooks/use-marketplace-picture-upload"
import { CloseIcon } from "@/Icons"
import Image from "next/image"
import { useEffect, useState } from "react"

interface MarketplaceProductFormModalProps {
  isOpen: boolean
  onClose: () => void
  product?: TMarketplaceProduct | null
  onSave: (product: TMarketplaceProduct) => void
}

const categories = [
  { value: "electronics-appliances", label: "Electronics & Appliances" },
  { value: "furniture", label: "Furniture" },
  { value: "vehicle", label: "Vehicle" },
  { value: "others", label: "Others" },
]

export default function MarketplaceProductFormModal({
  isOpen,
  onClose,
  product,
  onSave,
}: MarketplaceProductFormModalProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    expectingPrice: 0,
    category: "others" as TMarketplaceProduct["category"],
  })

  const {
    pictures,
    isUploading,
    minimumImages,
    maxImages,
    canSubmit: canSubmitPictures,
    canAddMore,
    uploadPictures,
    removePicture,
    clearPictures,
    getValidationMessage,
  } = useMarketplacePictureUpload({
    category: formData.category,
    initialPictures: product?.pictures || [],
    onUploadComplete: (_urls) => {
      // Pictures uploaded successfully
    },
    onUploadError: (error) => {
      console.error("Upload error:", error)
      alert(`Upload failed: ${error}`)
    },
  })

  useEffect(() => {
    if (isOpen) {
      if (product) {
        setFormData({
          title: product.title,
          description: product.description,
          expectingPrice: product.expectingPrice,
          category: product.category,
        })
      } else {
        setFormData({
          title: "",
          description: "",
          expectingPrice: 0,
          category: "others",
        })
        clearPictures()
      }
    }
  }, [isOpen, product, clearPictures])

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >,
  ) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: name === "expectingPrice" ? Number(value) : value,
    }))
  }

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    if (files.length > 0) {
      await uploadPictures(files)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.title.trim()) {
      alert("Please enter a product title")
      return
    }

    if (!formData.description.trim()) {
      alert("Please enter a product description")
      return
    }

    if (formData.expectingPrice <= 0) {
      alert("Please enter a valid expecting price")
      return
    }

    const pictureValidation = getValidationMessage()
    if (pictureValidation) {
      alert(pictureValidation)
      return
    }

    setLoading(true)

    try {
      let savedProduct: TMarketplaceProduct

      if (product) {
        savedProduct = await SellerDashboardService.updateMarketplaceProduct(
          product.id,
          {
            ...formData,
            pictures,
          },
        )
      } else {
        savedProduct = await SellerDashboardService.createMarketplaceProduct({
          ...formData,
          pictures,
        })
      }

      onSave(savedProduct)
      onClose()
    } catch (error) {
      console.error("Error saving product:", error)
      alert("Failed to save product. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (typeof window !== "undefined" && isOpen) {
      document.body.style.overflow = "hidden"
    } else {
      document.body.style.overflow = "auto"
    }
  }, [isOpen])

  if (!isOpen) return null

  const validationMessage = getValidationMessage()

  return (
    <div className='fixed inset-0 z-50 overflow-hidden'>
      <div className='flex min-h-screen items-center justify-center px-4 pb-20 pt-4 text-center sm:block sm:p-0'>
        <div
          className='fixed inset-0 bg-black/60 backdrop-blur-sm transition-opacity'
          onClick={onClose}
        ></div>

        <div className='inline-block h-[90vh] transform overflow-hidden rounded-3xl bg-white text-left align-bottom shadow-2xl transition-all sm:my-8 sm:w-full sm:max-w-2xl sm:align-middle'>
          <div className='sticky top-0 z-10 bg-gradient-to-r from-primary/5 to-primary/10 px-8 py-6'>
            <div className='flex items-center justify-between'>
              <div>
                <h3 className='text-2xl font-bold text-zinc-900'>
                  {product ? "Edit Product" : "Add New Product"}
                </h3>
                <p className='mt-1 text-sm text-zinc-600'>
                  {product
                    ? "Update your product details"
                    : "Create a new product listing"}
                </p>
              </div>
              <button
                onClick={onClose}
                className='rounded-full bg-white/80 p-2 text-zinc-400 transition-all duration-200 hover:scale-110 hover:bg-white hover:text-zinc-600'
              >
                <CloseIcon className='h-6 w-6' />
              </button>
            </div>
          </div>

          <div
            className='overflow-y-auto px-8'
            style={{ maxHeight: "calc(90vh - 160px)" }}
          >
            <form onSubmit={handleSubmit} className='space-y-6 py-6'>
              <div className='space-y-2'>
                <label className='block text-sm font-semibold text-zinc-800'>
                  Product Title *
                </label>
                <input
                  type='text'
                  name='title'
                  value={formData.title}
                  onChange={handleInputChange}
                  className='w-full rounded-2xl border border-zinc-200 bg-zinc-50/50 px-4 py-3 text-zinc-900 placeholder-zinc-500 transition-all duration-200 focus:border-primary focus:bg-white focus:outline-none focus:ring-4 focus:ring-primary/10'
                  placeholder='Enter a compelling product title'
                  required
                />
              </div>

              <div className='grid grid-cols-1 gap-4 sm:grid-cols-2'>
                <div className='space-y-2'>
                  <label className='block text-sm font-semibold text-zinc-800'>
                    Category *
                  </label>
                  <select
                    name='category'
                    value={formData.category}
                    onChange={handleInputChange}
                    className='w-full rounded-2xl border border-zinc-200 bg-zinc-50/50 px-4 py-3 text-zinc-900 transition-all duration-200 focus:border-primary focus:bg-white focus:outline-none focus:ring-4 focus:ring-primary/10'
                    required
                  >
                    {categories.map((category) => (
                      <option key={category.value} value={category.value}>
                        {category.label}
                      </option>
                    ))}
                  </select>
                  <div className='mt-2 flex items-center gap-2'>
                    <div className='h-2 w-2 rounded-full bg-primary/60'></div>
                    <p className='text-xs text-zinc-600'>
                      {formData.category === "electronics-appliances" ||
                      formData.category === "vehicle"
                        ? `Minimum ${minimumImages} photos required for this category`
                        : `Minimum ${minimumImages} photos required`}
                    </p>
                  </div>
                </div>

                <div className='space-y-2'>
                  <label className='block text-sm font-semibold text-zinc-800'>
                    Expected Price (₹) *
                  </label>
                  <div className='relative'>
                    <div className='absolute left-4 top-1/2 -translate-y-1/2 font-medium text-zinc-500'>
                      ₹
                    </div>
                    <input
                      type='number'
                      name='expectingPrice'
                      value={formData.expectingPrice}
                      onChange={handleInputChange}
                      min='1'
                      className='w-full rounded-2xl border border-zinc-200 bg-zinc-50/50 py-3 pl-8 pr-4 text-zinc-900 placeholder-zinc-500 transition-all duration-200 focus:border-primary focus:bg-white focus:outline-none focus:ring-4 focus:ring-primary/10'
                      placeholder='0'
                      required
                    />
                  </div>
                </div>
              </div>

              <div className='space-y-2'>
                <label className='block text-sm font-semibold text-zinc-800'>
                  Description *
                </label>
                <textarea
                  name='description'
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={4}
                  className='w-full resize-none rounded-2xl border border-zinc-200 bg-zinc-50/50 px-4 py-3 text-zinc-900 placeholder-zinc-500 transition-all duration-200 focus:border-primary focus:bg-white focus:outline-none focus:ring-4 focus:ring-primary/10'
                  placeholder='Describe your product in detail...'
                  required
                />
              </div>

              <div className='space-y-3'>
                <label className='block text-sm font-semibold text-zinc-800'>
                  Product Photos *
                </label>

                {canAddMore && (
                  <div className='relative'>
                    <input
                      type='file'
                      id='pictures'
                      multiple
                      accept='image/*'
                      onChange={handleFileSelect}
                      className='hidden'
                      disabled={isUploading}
                    />
                    <label
                      htmlFor='pictures'
                      className={`group flex cursor-pointer items-center justify-center gap-3 rounded-2xl border-2 border-dashed border-zinc-200 bg-zinc-50/50 p-8 text-center transition-all duration-200 hover:border-primary hover:bg-primary/5 ${
                        isUploading ? "cursor-not-allowed opacity-50" : ""
                      }`}
                    >
                      <div className='flex flex-col items-center gap-2'>
                        <div className='rounded-full bg-primary/10 p-3 transition-colors group-hover:bg-primary/20'>
                          <span className='text-2xl'>📸</span>
                        </div>
                        <div className='space-y-1'>
                          <p className='text-sm font-semibold text-zinc-700'>
                            {isUploading
                              ? "Uploading photos..."
                              : "Add Product Photos"}
                          </p>
                          <p className='text-xs text-zinc-500'>
                            Click to browse or drag & drop images
                          </p>
                        </div>
                      </div>
                    </label>
                  </div>
                )}

                <div className='flex items-center justify-between text-sm'>
                  <span className='text-zinc-600'>
                    {pictures.length}/{maxImages} photos uploaded
                  </span>
                  {validationMessage && (
                    <span className='font-medium text-red-600'>
                      {validationMessage}
                    </span>
                  )}
                </div>

                {pictures.length > 0 && (
                  <div className='grid grid-cols-3 gap-4'>
                    {pictures.map((url, index) => (
                      <div
                        key={index}
                        className='group relative overflow-hidden rounded-2xl'
                      >
                        <Image
                          src={url}
                          alt={`Product ${index + 1}`}
                          width={120}
                          height={120}
                          className='h-24 w-full rounded-2xl object-cover transition-transform duration-200 group-hover:scale-110'
                        />
                        <button
                          type='button'
                          onClick={() => removePicture(index)}
                          className='absolute -right-2 -top-2 rounded-full bg-red-500 p-1.5 text-white shadow-lg transition-all duration-200 hover:scale-110 hover:bg-red-600'
                        >
                          <CloseIcon className='h-3 w-3' />
                        </button>
                        <div className='absolute inset-0 rounded-2xl bg-black/0 transition-colors group-hover:bg-black/20'></div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </form>
          </div>

          <div className='sticky bottom-0 z-10 border-t border-zinc-100 bg-white px-8 py-6'>
            <div className='flex justify-end gap-4'>
              <button
                type='button'
                onClick={onClose}
                className='rounded-2xl border border-zinc-200 bg-white px-6 py-3 text-sm font-semibold text-zinc-700 transition-all duration-200 hover:border-zinc-300 hover:bg-zinc-50 focus:outline-none focus:ring-4 focus:ring-zinc-100'
              >
                Cancel
              </button>
              <button
                type='submit'
                onClick={handleSubmit}
                disabled={loading || isUploading || !canSubmitPictures}
                className='inline-flex items-center gap-2 rounded-2xl bg-gradient-to-r from-primary to-primary/90 px-8 py-3 text-sm font-semibold text-white shadow-lg shadow-primary/25 transition-all duration-200 hover:scale-105 hover:shadow-xl hover:shadow-primary/30 focus:outline-none focus:ring-4 focus:ring-primary/20 disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:scale-100'
              >
                {loading && (
                  <div className='h-4 w-4 animate-spin rounded-full border-2 border-white/30 border-t-white'></div>
                )}
                {loading
                  ? "Saving..."
                  : product
                    ? "Update Product"
                    : "Create Product"}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
