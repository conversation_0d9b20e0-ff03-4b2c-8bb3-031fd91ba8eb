"use client"

import { SellerDashboardService } from "@/app/shared/services/marketplace/seller-dashboard-service"
import { TOrder } from "@/config/seller-dashboard"
import { useEffect, useState } from "react"

export default function OrdersList() {
  const [orders, setOrders] = useState<TOrder[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState<
    "all" | "pending" | "confirmed" | "in-progress" | "completed" | "cancelled"
  >("all")

  useEffect(() => {
    const fetchOrders = async () => {
      try {
        const data = await SellerDashboardService.getOrders()
        setOrders(data)
      } catch (error) {
        console.error("Error fetching orders:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchOrders()
  }, [])

  const handleStatusChange = async (
    orderId: string,
    newStatus: TOrder["status"],
  ) => {
    try {
      await SellerDashboardService.updateOrderStatus(orderId, newStatus)
      setOrders((prev) =>
        prev.map((order) =>
          order.id === orderId ? { ...order, status: newStatus } : order,
        ),
      )
    } catch (error) {
      console.error("Error updating order status:", error)
      alert("Failed to update order status")
    }
  }

  const filteredOrders = orders.filter((order) => {
    if (filter === "all") return true
    return order.status === filter
  })

  const getStatusColor = (status: TOrder["status"]) => {
    switch (status) {
      case "pending":
        return "bg-amber-100 text-amber-800 border-amber-200"
      case "confirmed":
        return "bg-primary/10 text-primary border-primary/20"
      case "in-progress":
        return "bg-blue-100 text-blue-800 border-blue-200"
      case "completed":
        return "bg-green-100 text-green-800 border-green-200"
      case "cancelled":
        return "bg-red-100 text-red-800 border-red-200"
      default:
        return "bg-zinc-100 text-zinc-800 border-zinc-200"
    }
  }

  const getNextStatus = (
    currentStatus: TOrder["status"],
  ): TOrder["status"] | null => {
    switch (currentStatus) {
      case "pending":
        return "confirmed"
      case "confirmed":
        return "in-progress"
      case "in-progress":
        return "completed"
      default:
        return null
    }
  }

  if (loading) {
    return (
      <div className='space-y-8'>
        <div className='animate-pulse'>
          {/* Header Skeleton */}
          <div className='mb-8 space-y-3'>
            <div className='h-8 w-48 rounded-lg bg-gradient-to-r from-zinc-200 to-zinc-300'></div>
            <div className='h-5 w-72 rounded-lg bg-zinc-100'></div>
          </div>

          {/* Stats Cards Skeleton */}
          <div className='mb-8 grid grid-cols-2 gap-4 md:grid-cols-6'>
            {[...Array(6)].map((_, i) => (
              <div
                key={i}
                className='rounded-2xl border border-zinc-100 bg-white p-4 shadow-sm'
              >
                <div className='mb-2 h-4 w-16 rounded-lg bg-zinc-200'></div>
                <div className='h-6 w-10 rounded-lg bg-zinc-300'></div>
              </div>
            ))}
          </div>

          {/* Filters Skeleton */}
          <div className='mb-8 flex flex-wrap gap-3'>
            {[...Array(6)].map((_, i) => (
              <div key={i} className='h-11 w-24 rounded-xl bg-zinc-200'></div>
            ))}
          </div>

          {/* Orders Skeleton */}
          <div className='space-y-6'>
            {[...Array(4)].map((_, i) => (
              <div
                key={i}
                className='rounded-3xl border border-zinc-100 bg-white p-8 shadow-sm'
              >
                <div className='space-y-4'>
                  <div className='flex items-center justify-between'>
                    <div className='h-6 w-48 rounded-lg bg-zinc-200'></div>
                    <div className='h-7 w-20 rounded-full bg-zinc-100'></div>
                  </div>
                  <div className='grid grid-cols-2 gap-4 md:grid-cols-3'>
                    {[...Array(6)].map((_, j) => (
                      <div
                        key={j}
                        className='h-4 w-32 rounded-lg bg-zinc-100'
                      ></div>
                    ))}
                  </div>
                  <div className='h-16 w-full rounded-xl bg-zinc-50'></div>
                  <div className='flex gap-3'>
                    <div className='h-10 w-32 rounded-xl bg-zinc-100'></div>
                    <div className='h-10 w-24 rounded-xl bg-zinc-100'></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className='space-y-8'>
      {/* Header */}
      <div className='space-y-2'>
        <h1 className='text-3xl font-bold text-zinc-900'>Orders Management</h1>
        <p className='text-lg text-zinc-600'>
          Track and manage your marketplace orders efficiently
        </p>
      </div>

      {/* Stats Cards */}
      <div className='grid grid-cols-2 gap-4 md:grid-cols-6'>
        <div className='rounded-2xl border border-emerald-100 bg-gradient-to-br from-emerald-50 to-emerald-100/50 p-4 shadow-sm'>
          <p className='text-xs font-medium text-emerald-700'>Total Orders</p>
          <p className='text-xl font-bold text-emerald-900'>{orders.length}</p>
        </div>
        <div className='rounded-2xl border border-amber-100 bg-gradient-to-br from-amber-50 to-amber-100/50 p-4 shadow-sm'>
          <p className='text-xs font-medium text-amber-700'>Pending</p>
          <p className='text-xl font-bold text-amber-900'>
            {orders.filter((o) => o.status === "pending").length}
          </p>
        </div>
        <div className='rounded-2xl border border-blue-100 bg-gradient-to-br from-blue-50 to-blue-100/50 p-4 shadow-sm'>
          <p className='text-xs font-medium text-blue-700'>Confirmed</p>
          <p className='text-xl font-bold text-blue-900'>
            {orders.filter((o) => o.status === "confirmed").length}
          </p>
        </div>
        <div className='rounded-2xl border border-purple-100 bg-gradient-to-br from-purple-50 to-purple-100/50 p-4 shadow-sm'>
          <p className='text-xs font-medium text-purple-700'>In Progress</p>
          <p className='text-xl font-bold text-purple-900'>
            {orders.filter((o) => o.status === "in-progress").length}
          </p>
        </div>
        <div className='rounded-2xl border border-green-100 bg-gradient-to-br from-green-50 to-green-100/50 p-4 shadow-sm'>
          <p className='text-xs font-medium text-green-700'>Completed</p>
          <p className='text-xl font-bold text-green-900'>
            {orders.filter((o) => o.status === "completed").length}
          </p>
        </div>
        <div className='rounded-2xl border border-red-100 bg-gradient-to-br from-red-50 to-red-100/50 p-4 shadow-sm'>
          <p className='text-xs font-medium text-red-700'>Cancelled</p>
          <p className='text-xl font-bold text-red-900'>
            {orders.filter((o) => o.status === "cancelled").length}
          </p>
        </div>
      </div>

      {/* Filters */}
      <div className='flex flex-wrap gap-3'>
        {(
          [
            "all",
            "pending",
            "confirmed",
            "in-progress",
            "completed",
            "cancelled",
          ] as const
        ).map((status) => (
          <button
            key={status}
            onClick={() => setFilter(status)}
            className={`group relative overflow-hidden rounded-2xl px-6 py-3 text-sm font-semibold transition-all duration-300 ${
              filter === status
                ? "scale-105 bg-gradient-to-r from-primary to-primary/90 text-white shadow-lg shadow-primary/25"
                : "border border-zinc-200 bg-white text-zinc-700 shadow-sm hover:scale-105 hover:border-primary/30 hover:bg-primary/5 hover:text-primary"
            }`}
          >
            <span className='relative z-10'>
              {status.charAt(0).toUpperCase() +
                status.slice(1).replace("-", " ")}
            </span>
            <span
              className={`ml-2 inline-flex h-6 min-w-[24px] items-center justify-center rounded-full px-2 text-xs font-bold ${
                filter === status
                  ? "bg-white/20 text-white"
                  : "bg-zinc-100 text-zinc-600 group-hover:bg-primary/10 group-hover:text-primary"
              }`}
            >
              {status === "all"
                ? orders.length
                : orders.filter((o) => o.status === status).length}
            </span>
          </button>
        ))}
      </div>

      {/* Orders List */}
      {filteredOrders.length === 0 ? (
        <div className='flex flex-col items-center justify-center rounded-3xl border-2 border-dashed border-zinc-200 bg-gradient-to-br from-zinc-50/50 to-white py-20'>
          <div className='mb-6 flex h-24 w-24 items-center justify-center rounded-full bg-gradient-to-br from-primary/10 to-primary/5 shadow-lg'>
            <span className='text-5xl'>📋</span>
          </div>
          <h3 className='mb-3 text-xl font-bold text-zinc-900'>
            No orders found
          </h3>
          <p className='max-w-md text-center leading-relaxed text-zinc-600'>
            {filter === "all"
              ? "You haven't received any orders yet. Orders will appear here once customers start placing orders."
              : `No ${filter.replace("-", " ")} orders found. Try selecting a different filter.`}
          </p>
        </div>
      ) : (
        <div className='space-y-6'>
          {filteredOrders.map((order) => (
            <div
              key={order.id}
              className='group overflow-hidden rounded-3xl border border-zinc-100 bg-white shadow-sm transition-all duration-500 hover:-translate-y-1 hover:border-primary/20 hover:shadow-2xl hover:shadow-primary/5'
            >
              <div className='p-8'>
                <div className='flex flex-col gap-6 lg:flex-row lg:items-start lg:justify-between'>
                  {/* Order Info */}
                  <div className='flex-1 space-y-6'>
                    {/* Header */}
                    <div className='flex items-start justify-between'>
                      <div className='space-y-1'>
                        <h3 className='text-xl font-bold text-zinc-900 transition-colors group-hover:text-primary'>
                          {order.productName}
                        </h3>
                        <p className='inline-block rounded-lg bg-zinc-50 px-2 py-1 font-mono text-sm text-zinc-500'>
                          ID: {order.id}
                        </p>
                      </div>
                      <span
                        className={`inline-flex items-center rounded-2xl border px-4 py-2 text-sm font-semibold shadow-sm ${getStatusColor(order.status)}`}
                      >
                        {order.status.replace("-", " ")}
                      </span>
                    </div>

                    {/* Customer & Order Details */}
                    <div className='grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3'>
                      <div className='space-y-1 rounded-2xl bg-zinc-50/50 p-4'>
                        <p className='text-xs font-semibold uppercase tracking-wide text-zinc-500'>
                          Customer
                        </p>
                        <p className='text-sm font-semibold text-zinc-900'>
                          {order.customerName}
                        </p>
                      </div>
                      <div className='space-y-1 rounded-2xl bg-zinc-50/50 p-4'>
                        <p className='text-xs font-semibold uppercase tracking-wide text-zinc-500'>
                          Phone
                        </p>
                        <p className='text-sm font-medium text-zinc-900'>
                          {order.customerPhone}
                        </p>
                      </div>
                      <div className='space-y-1 rounded-2xl bg-zinc-50/50 p-4'>
                        <p className='text-xs font-semibold uppercase tracking-wide text-zinc-500'>
                          Email
                        </p>
                        <p className='truncate text-sm font-medium text-zinc-900'>
                          {order.customerEmail}
                        </p>
                      </div>
                      <div className='space-y-1 rounded-2xl bg-emerald-50/50 p-4'>
                        <p className='text-xs font-semibold uppercase tracking-wide text-emerald-600'>
                          Quantity
                        </p>
                        <p className='text-sm font-bold text-emerald-800'>
                          {order.quantity}
                        </p>
                      </div>
                      <div className='space-y-1 rounded-2xl bg-primary/5 p-4'>
                        <p className='text-xs font-semibold uppercase tracking-wide text-primary'>
                          Total Amount
                        </p>
                        <p className='text-lg font-bold text-primary'>
                          ₹{order.totalAmount.toLocaleString()}
                        </p>
                      </div>
                      <div className='space-y-1 rounded-2xl bg-blue-50/50 p-4'>
                        <p className='text-xs font-semibold uppercase tracking-wide text-blue-600'>
                          Order Date
                        </p>
                        <p className='text-sm font-medium text-blue-800'>
                          {new Date(order.orderDate).toLocaleDateString(
                            "en-IN",
                            {
                              day: "numeric",
                              month: "short",
                              year: "numeric",
                            },
                          )}
                        </p>
                      </div>
                      {order.deliveryDate && (
                        <div className='space-y-1 rounded-2xl bg-purple-50/50 p-4'>
                          <p className='text-xs font-semibold uppercase tracking-wide text-purple-600'>
                            Delivery Date
                          </p>
                          <p className='text-sm font-medium text-purple-800'>
                            {new Date(order.deliveryDate).toLocaleDateString(
                              "en-IN",
                              {
                                day: "numeric",
                                month: "short",
                                year: "numeric",
                              },
                            )}
                          </p>
                        </div>
                      )}
                    </div>

                    {/* Address */}
                    <div className='rounded-2xl border border-zinc-100 bg-gradient-to-br from-zinc-50/50 to-white p-6 shadow-sm'>
                      <h4 className='mb-3 flex items-center gap-2 font-semibold text-zinc-900'>
                        <span className='text-lg'>📍</span>
                        Delivery Address
                      </h4>
                      <p className='text-sm leading-relaxed text-zinc-700'>
                        {order.address.street}, {order.address.city},{" "}
                        {order.address.state} - {order.address.pincode}
                      </p>
                    </div>

                    {/* Notes */}
                    {order.notes && (
                      <div className='rounded-2xl border border-primary/10 bg-gradient-to-br from-primary/5 to-primary/10 p-6 shadow-sm'>
                        <h4 className='mb-3 flex items-center gap-2 font-semibold text-zinc-900'>
                          <span className='text-lg'>💬</span>
                          Customer Notes
                        </h4>
                        <p className='text-sm italic leading-relaxed text-zinc-700'>
                          "{order.notes}"
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Actions */}
                  <div className='flex flex-col gap-4 lg:min-w-[180px]'>
                    {getNextStatus(order.status) && (
                      <button
                        onClick={() => {
                          const nextStatus = getNextStatus(order.status)
                          if (nextStatus) {
                            handleStatusChange(order.id, nextStatus)
                          }
                        }}
                        className='group rounded-2xl bg-gradient-to-r from-primary to-primary/90 px-6 py-3 font-semibold text-white shadow-lg shadow-primary/25 transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-primary/30 focus:outline-none focus:ring-4 focus:ring-primary/20'
                      >
                        <span className='flex items-center gap-2'>
                          <span>
                            Mark as{" "}
                            {getNextStatus(order.status)?.replace("-", " ")}
                          </span>
                          <span className='transition-transform group-hover:translate-x-1'>
                            →
                          </span>
                        </span>
                      </button>
                    )}

                    {order.status !== "cancelled" &&
                      order.status !== "completed" && (
                        <button
                          onClick={() =>
                            handleStatusChange(order.id, "cancelled")
                          }
                          className='rounded-2xl bg-gradient-to-r from-red-50 to-red-100/50 px-6 py-3 font-semibold text-red-600 transition-all duration-300 hover:scale-105 hover:from-red-100 hover:to-red-200/50 focus:outline-none focus:ring-4 focus:ring-red-500/20'
                        >
                          Cancel Order
                        </button>
                      )}

                    <div className='space-y-2'>
                      <label className='block text-xs font-semibold uppercase tracking-wide text-zinc-500'>
                        Quick Status Change
                      </label>
                      <select
                        value={order.status}
                        onChange={(e) =>
                          handleStatusChange(
                            order.id,
                            e.target.value as TOrder["status"],
                          )
                        }
                        className='w-full rounded-2xl border border-zinc-200 bg-white px-4 py-3 text-sm font-medium text-zinc-700 transition-all duration-200 focus:border-primary focus:outline-none focus:ring-4 focus:ring-primary/10'
                      >
                        <option value='pending'>Pending</option>
                        <option value='confirmed'>Confirmed</option>
                        <option value='in-progress'>In Progress</option>
                        <option value='completed'>Completed</option>
                        <option value='cancelled'>Cancelled</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
