import { TextBanner } from "@/app/components/common"
import { ANTI_BRIBERY_AND_CORRUPTION_POLICY } from "@/config"
import {
  <PERSON><PERSON><PERSON><PERSON>,
  SectionTitle,
  SectionWrapper,
} from "@/app/shared/components"

const AntiBriberyAndCorruptionPolicy = () => {
  return (
    <>
      <PageHeader secondLine='ANTI-BRIBERY AND CORRUPTION POLICY' />
      <SectionWrapper className='font-inter !leading-tight text-zinc-400 ~text-sm/lg'>
        <SectionTitle
          title='ANTI-BRIBERY AND CORRUPTION POLICY'
          subtitle='LEGAL'
          className='text-black'
        />
        <p>{ANTI_BRIBERY_AND_CORRUPTION_POLICY.introduction}</p>
        <div className='~mt-5/7 ~space-y-5/7'>
          {ANTI_BRIBERY_AND_CORRUPTION_POLICY.data.map((data, index) => (
            <div key={index}>
              <p className='uppercase text-primary ~mb-3/5'>{data.title}</p>
              {data.contentList ? (
                <ul className='list-disc'>
                  {data.contentList.map((list, index) => (
                    <li className='ml-5' key={index}>
                      {list}
                    </li>
                  ))}
                </ul>
              ) : (
                <p>{data.content}</p>
              )}
            </div>
          ))}
        </div>
      </SectionWrapper>
      <TextBanner
        text='Thank you for choosing Nex Move. We appreciate your understanding and are here to assist with any questions or concerns regarding our Anti-Bribery and Corruption Policy.'
        className='mx-auto max-w-screen-xl'
      />
    </>
  )
}

export default AntiBriberyAndCorruptionPolicy
