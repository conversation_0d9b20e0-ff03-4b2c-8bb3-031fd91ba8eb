import { <PERSON><PERSON><PERSON><PERSON>, SectionW<PERSON><PERSON> } from "@/app/shared/components"
import { cn } from "@/app/shared/utils"
import { LOCATION_BENEFITS, TLocationBenefit } from "@/config"

const WhyWorkWithNex = () => {
  return (
    <SectionWrapper>
      <SectionTitle
        subtitle='Why Choose NexMove in These Locations?'
        title='Why Work with Nex Move?'
      />
      <div className='max-sm:jus mx-auto grid max-w-screen-lg ~mt-3/5 ~gap-2/4 sm:grid-cols-2'>
        {LOCATION_BENEFITS.map((benefits, idx) => (
          <BenefitCard {...benefits} key={idx} />
        ))}
      </div>
    </SectionWrapper>
  )
}

export default WhyWorkWithNex

// TODO: Move in another folder
const BenefitCard = (props: TLocationBenefit) => {
  const shouldScaleIcon = props.label === LOCATION_BENEFITS[0].label
  return (
    <div className='grid rounded-lg bg-zinc-100 ~gap-2/3 ~px-5/8 ~py-8/16 max-sm:text-center'>
      <props.icon
        className={cn(
          "~size-7/8 max-sm:mx-auto",
          shouldScaleIcon && "scale-[1.15]",
        )}
      />
      <p className='~text-lg/xl'>{props.label}</p>
    </div>
  )
}
