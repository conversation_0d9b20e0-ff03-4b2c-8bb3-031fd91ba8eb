import { TEmployeeTestimonials } from "@/config"
import Image from "next/image"

type TEmployeeTestimonialCardProps = TEmployeeTestimonials & {
  test?: boolean
}

const EmployeeTestimonialCard = (props: TEmployeeTestimonialCardProps) => {
  const { avatar, name, designation, service, testimonial } = props

  return (
    <div className='grid grid-cols-[30%_55%] items-center justify-end px-[1px] ~gap-4/8 max-sm:grid-cols-1'>
      {/* Left View */}
      <div className='relative h-full min-h-60 sm:aspect-[9/12]'>
        <Image
          loading='lazy'
          src={avatar}
          alt={name}
          className='object-cover'
          draggable={false}
          sizes='(max-width: 768px) 100vw, 50vw'
          fill
        />
      </div>
      {/* Right View */}
      <div className='grid font-manrope ~gap-3/5 sm:pr-10'>
        <div className='grid ~gap-0/0.5'>
          <h3 className='font-medium !leading-none ~text-lg/2xl'>{name}</h3>
          <span className='text-xs capitalize text-zinc-500'>
            {designation}
          </span>
          <span className='text-xs font-medium uppercase text-primary ~pt-0/0.5'>
            {service}
          </span>
        </div>
        <p className='max-w-[600px] font-inter !leading-snug ~text-sm/xl'>
          {testimonial}
        </p>
      </div>
    </div>
  )
}

export default EmployeeTestimonialCard
