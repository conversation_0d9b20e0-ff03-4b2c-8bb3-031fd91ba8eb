import { SectionTit<PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/app/shared/components"
import { Carousel } from "@/app/shared/components/carousel"
import React from "react"
import { EMPLOYEE_TESTIMONIALS } from "@/config"
import EmployeeTestimonialCard from "./EmployeeTesimonialsCard"

const EmployeeTestimonials = () => {
  return (
    <SectionWrapper className='~pt-14/20'>
      <div className='mx-auto max-w-screen-xl'>
        <SectionTitle
          subtitle='EMPLOYEE TESTIMONIAL'
          title="At Nex Move, our employees are the heart of everything we do. Here's what some of our team members have to say about their experience working with us:"
        />
        <div>
          <Carousel
            slideNodes={EMPLOYEE_TESTIMONIALS.map((testimonial) => (
              <EmployeeTestimonialCard key={testimonial.id} {...testimonial} />
            ))}
            navigationNodes={EMPLOYEE_TESTIMONIALS.map((_, index) => (
              <Navigation key={index} index={index} />
            ))}
            navigationContainerClassName='absolute left-0 top-1/2 -translate-y-1/2 flex-col max-sm:hidden'
            actionButtonsContainerClassName='left-[10%]'
          />
        </div>
      </div>
    </SectionWrapper>
  )
}

export default EmployeeTestimonials

const Navigation = ({ index }: { index: number }) => {
  return (
    <div className='text-primary'>{String(index + 1).padStart(2, "0")}</div>
  )
}
