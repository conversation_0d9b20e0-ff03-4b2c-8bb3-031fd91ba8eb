"use client"
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ie<PERSON>, StepProgress } from "@/app/shared/components"
import { Wrapper } from "@/app/shared/components/Wrapper"
import { JOB_APPLY_PROCESS } from "@/config"

const HowToApply = () => {
  return (
    <div>
      <Wrapper>
        <SectionTitle
          subtitle='Contact Us'
          title='We&rsquo;re here to assist you with all your logistics, transportation, and specialized service needs. Whether you have a question, need a quote, or require immediate assistance, our team is ready to help.'
          className='~pb-8/12'
        />
      </Wrapper>
      <SplitView
        image='/images/shipment.webp'
        labelBoldText='NEX'
        labelText='MOVE'
        className='[&>div:first-child_img]:object-[center_top]'
      >
        <div>
          <h3 className='uppercase ~text-lg/xl ~pb-5/8'>How to apply ?</h3>
          <div className='relative isolate grid ~gap-5/10'>
            {JOB_APPLY_PROCESS.map((step) => (
              <StepProgress key={step.number} {...step} />
            ))}

            {/* steps bottom line */}
            <span className='absolute bottom-[10%] top-[10%] -z-10 border-l-[1px] border-dashed border-gray-500/20 ~left-5/6 sm:border-l-2'></span>
          </div>
          <p className='text-zinc-400 ~pt-6/10'>
            For any inquiries, email <EMAIL>
          </p>
        </div>
      </SplitView>
    </div>
  )
}

export default HowToApply
