import { But<PERSON> } from "@/app/components/ui"
import { LocationIcon, PersonIcon } from "@/Icons"
import Link from "next/link"
import { JobPosting } from "../../../app/shared/services/job-application"

// The component accepts both static job data and API job data
const JobCard = (props: JobPosting) => {
  const {
    id,
    jobPostingTitle,
    jobPosition,
    jobLocations,
    jobPostingDescription,
  } = props

  return (
    <div className='grid h-full content-between overflow-hidden rounded-md bg-zinc-100 shadow-sm transition-shadow duration-300 ~w-64/96 ~gap-1/2 hover:shadow-md'>
      <div>
        <div className='bg-primary ~p-4/6'>
          <span className='ml-auto block w-fit rounded-md bg-white text-xs font-medium uppercase text-primary ~px-1.5/2 ~py-0.5/1'>
            {jobPosition?.jobPositionType?.name || "Full-Time"}
          </span>
          <PersonIcon className='text-white ~my-2/3 ~size-8/10' />
          <h3 className='font-medium uppercase !leading-tight text-white ~text-lg/2xl'>
            {jobPostingTitle || jobPosition?.positionTitle}
          </h3>
        </div>
        <div className='~px-4/6 ~py-3/4'>
          <p className='line-clamp-3 !leading-tight text-zinc-500 ~pb-4/5'>
            {jobPostingDescription || jobPosition?.jobDescription}
          </p>
          <div className='flex items-center ~gap-3/5'>
            <LocationIcon className='flex-shrink-0 text-black ~text-2xl/3xl' />
            <Location locations={jobLocations} />
          </div>
        </div>
      </div>
      <div className='~px-4/6'>
        <Link href={`/job-openings/${id}`}>
          <Button
            size='sm'
            className='mx-auto block w-full max-w-64 bg-black text-white transition-colors ~mb-5/6 hover:bg-gray-800'
          >
            View Details
          </Button>
        </Link>
      </div>
    </div>
  )
}

export default JobCard

// TODO: Move this in another folder
const Location = ({ locations }: { locations: string[] }) => {
  return (
    <span className='font-inter ~text-xs/sm'>
      {locations.join(", ").replace(/, ([^,]*)$/, " & $1")}
    </span>
  )
}
