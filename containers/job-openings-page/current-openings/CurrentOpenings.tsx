"use client"
import { useEffect, useState } from "react"

import { Loader } from "@/app/components/common"
import { SectionTitle, SectionWrapper } from "@/app/shared/components"
import { Carousel } from "@/app/shared/components/carousel"
import { getActiveJobPostings } from "@/app/shared/services/job-application/job-application.service"
import JobCard from "./JobCard"

const CurrentOpenings = () => {
  const [jobs, setJobs] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isError, setIsError] = useState(false)

  console.log(jobs)

  useEffect(() => {
    setIsLoading(true)
    getActiveJobPostings(1, 20)
      .then((res) => {
        setJobs(res.items)
        setIsError(false)
      })
      .catch(() => {
        setIsError(true)
      })
      .finally(() => setIsLoading(false))
  }, [])

  const displayJobs = isError || !jobs || jobs.length === 0 ? [] : jobs

  return (
    <SectionWrapper>
      <SectionTitle subtitle='service' title='Current Openings' />
      {isLoading ? (
        <div className='flex items-center justify-center py-10'>
          <Loader />
        </div>
      ) : displayJobs.length === 0 ? (
        <div className='py-10 text-center text-gray-500'>
          No current openings found.
        </div>
      ) : (
        <Carousel
          fitContent
          slideNodes={displayJobs.map((job, index: number) => (
            <JobCard key={index} {...job} />
          ))}
          buttonsPosition='bottom-center'
          buttionSize='large'
        />
      )}
    </SectionWrapper>
  )
}

export default CurrentOpenings
