import { RELOCATION_CHECKLIST, TRelocationChecklist } from "@/config"

type TOurProcessCard = TRelocationChecklist & {
  idx: number
}

const OurProcessCard = (props: TOurProcessCard) => {
  return (
    <div className='relative grid grid-cols-[1fr_2fr] ~py-4/5 max-md:gap-5 md:grid-cols-[4fr_1fr_4fr]'>
      <div className='grid h-fit text-right uppercase ~gap-0.5/1'>
        <span className='~text-xl/3xl'>{props.title}</span>
        <span className='min-w-max font-inter text-[10px] !leading-tight text-zinc-500'>
          {props.subtitle}
        </span>
        <span className='font-medium ~text-base/xl'>{props.heading}</span>
        <span className='font-inter text-xs text-zinc-400'>{props.timing}</span>
      </div>
      <div className='mx-auto hidden aspect-square rounded-full bg-gradient-to-br from-primary to-transparent p-[1px] ~w-12/16 md:flex'>
        <span className='grid size-full place-items-center rounded-full bg-zinc-50'>
          <props.icon className='~size-5/7' />
        </span>
      </div>
      <div className='bg-zinc-100 ~space-y-3/5 ~p-5/7'>
        {props.task.map((task, idx) => (
          <div key={idx} className='grid h-fit ~gap-0.5/1.5'>
            <span className='uppercase ~text-base/xl'>{task.title}</span>
            <span className='max-w-screen-sm font-inter !leading-tight text-zinc-400'>
              {task.description}
            </span>
          </div>
        ))}
      </div>
      {/* center line */}
      {props.idx !== RELOCATION_CHECKLIST.length - 1 && (
        <span className='absolute left-1/2 top-[5%] -z-10 hidden h-full w-[1px] -translate-x-1/2 bg-primary md:block'></span>
      )}
    </div>
  )
}

export default OurProcessCard
