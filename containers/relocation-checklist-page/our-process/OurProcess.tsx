import { But<PERSON> } from "@/app/components/ui"
import { SectionTitle, SectionWrapper } from "@/app/shared/components"
import { RELOCATION_CHECKLIST } from "@/config"
import Link from "next/link"
import OurProcessCard from "./OurProcessCard"

const OurProcess = () => {
  return (
    <SectionWrapper>
      <SectionTitle title='OUR PROCESS' subtitle='SERVICES' />
      <div>
        {RELOCATION_CHECKLIST.map((item, idx) => (
          <OurProcessCard key={idx} {...item} idx={idx} />
        ))}
      </div>
      <div className='flex justify-center ~mt-5/8 ~gap-2/3'>
        <Link href='/services'>
          <Button>Get intant quote</Button>
        </Link>
        <Link
          // href='/track-shipment'
          target='_blank'
          rel='noopener noreferrer'
          href='https://development9p.trackingmore.org/'
        >
          <Button className='bg-zinc-100 text-primary'>Track way bill</Button>
        </Link>
      </div>
    </SectionWrapper>
  )
}

export default OurProcess
