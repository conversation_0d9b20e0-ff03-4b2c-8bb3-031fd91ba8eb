import { WHAT_WE_OFFER } from "@/config"
import { SectionTitle, SplitView } from "@/app/shared/components"

const PackerAndMovers = () => {
  return (
    <>
      <SectionTitle
        className='~px-4/16'
        subtitle='Our Values'
        title='WHY CHOOSE NEX PACKERS & MOVERS?'
      />
      <div>
        <SplitView
          image='/images/packing.webp'
          labelBoldText='Packers &'
          labelText='Movers'
        >
          <div className='grid ~gap-8/10'>
            {WHAT_WE_OFFER.map((item) => (
              <div key={item.title} className='flex ~gap-2/3'>
                <item.icon className='flex-shrink-0 ~w-7/8' />
                <div>
                  <h3 className='~text-lg/xl'>{item.title}</h3>
                  <p className='max-w-lg pt-1 leading-tight text-zinc-400'>
                    {item.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </SplitView>
      </div>
    </>
  )
}

export default PackerAndMovers
