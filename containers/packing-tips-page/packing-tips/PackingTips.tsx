"use client"

import { SectionT<PERSON><PERSON>, SectionWrapper } from "@/app/shared/components"
import { cn } from "@/app/shared/utils"
import { PACKING_TIPS } from "@/config"
import { ArrowUpIcon } from "@/Icons"
import { useState } from "react"

const PackingTips = () => {
  const [expandedIndex, setExpandedIndex] = useState<number | null>(null)

  const handleAccordionClick = (index: number) => {
    setExpandedIndex(expandedIndex === index ? null : index)
  }
  return (
    <SectionWrapper>
      <div className='justify-between ~gap-8/10 sm:flex'>
        <SectionTitle subtitle='SERVICES' title='PACKING TIPS' />
        <div>
          <ul className='grid ~gap-2/3'>
            {PACKING_TIPS.map((tip, idx) => (
              <Accordion
                key={idx}
                {...tip}
                isExpanded={expandedIndex === idx}
                onClick={() => handleAccordionClick(idx)}
              />
            ))}
          </ul>
        </div>
      </div>
    </SectionWrapper>
  )
}

export default PackingTips

const Accordion = ({
  title,
  steps,
  isExpanded,
  onClick,
}: {
  title: string
  steps: string[]
  isExpanded: boolean
  onClick: () => void
}) => {
  return (
    <li className='bg-zinc-100 ~p-3/4'>
      <h3
        onClick={onClick}
        className='flex cursor-pointer select-none items-center justify-between uppercase ~text-base/xl'
      >
        {title}
        <ArrowUpIcon
          className={cn("transition-transform", isExpanded && "rotate-180")}
        />
      </h3>
      <div
        className={cn(
          "grid grid-rows-[0fr] transition-all duration-300",
          isExpanded && "grid-rows-[1fr] ~mt-2/3",
        )}
      >
        <ul className='overflow-hidden ~space-y-1/1.5'>
          {steps.map((step, idx) => (
            <li key={idx} className='text-zinc-400 ~text-sm/lg'>
              {idx + 1}. {step}
            </li>
          ))}
        </ul>
      </div>
    </li>
  )
}
