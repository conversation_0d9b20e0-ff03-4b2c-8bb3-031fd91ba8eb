import { But<PERSON> } from "@/app/components/ui"
import { InfoCard, SectionTitle, SectionWrapper } from "@/app/shared/components"
import { Carousel } from "@/app/shared/components/carousel"
import { NEX_MOVE_SERVICES } from "@/config"
import Link from "next/link"

const OurServices = () => {
  return (
    <SectionWrapper>
      <SectionTitle
        subtitle='Services'
        title='Our Services'
        titleElement='h3'
      />
      <Carousel
        fitContent
        dragFree
        slideNodes={NEX_MOVE_SERVICES.map((service) => (
          <InfoCard {...service} key={service.title} />
        ))}
        actionButtonsContainerClassName='justify-end gap-5 translate-x-0 translate-y-0 top-[108%] [&_button]:~size-14/16'
      />
      <div className='flex ~mt-8/14 ~gap-2/3'>
        <Link
          href={"/services"}
          className='whitespace-nowrap max-sm:basis-full'
        >
          <Button
            size='lg'
            aria-label='Get Instant Quote Button'
            className='w-full whitespace-nowrap max-sm:basis-full'
          >
            Get instant quote
          </Button>
        </Link>
        <Link
          // href={"/track-shipment"}
          className='whitespace-nowrap max-sm:basis-full'
          target='_blank'
          rel='noopener noreferrer'
          href='https://development9p.trackingmore.org/'
        >
          <Button
            size='lg'
            aria-label='Track Way Bill Button'
            className='w-full whitespace-nowrap bg-zinc-100 text-black max-sm:basis-full'
          >
            Track way bill
          </Button>
        </Link>
      </div>
    </SectionWrapper>
  )
}

export default OurServices
