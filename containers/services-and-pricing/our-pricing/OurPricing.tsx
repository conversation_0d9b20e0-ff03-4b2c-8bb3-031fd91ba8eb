"use client"

import { SectionTit<PERSON>, SectionWrapper } from "@/app/shared/components"
import { OUR_PRICING } from "@/config"
import PricingCard from "./PricingCard"
import PricingCardMobile from "./PricingCardMobile"

const OurPricing = () => {
  return (
    <SectionWrapper className=''>
      <SectionTitle subtitle='Pricing' title='Our Pricing' titleElement='h2' />
      {/* Desktop view */}
      <div className='hidden grid-cols-1 gap-0 lg:grid'>
        {OUR_PRICING.map((item, index) => (
          <PricingCard {...item} key={index} index={index} />
        ))}
      </div>
      {/* Mobile/Tablet view */}
      <div className='grid grid-cols-1 gap-3 lg:hidden'>
        {OUR_PRICING.map((item, index) => (
          <PricingCardMobile {...item} key={index} index={index} />
        ))}
      </div>
    </SectionWrapper>
  )
}

export default OurPricing
