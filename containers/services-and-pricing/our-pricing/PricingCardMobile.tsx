import { ButtonLink } from "@/app/components/ui"
import { cn } from "@/app/shared/utils"
import { TOurPricing } from "@/config"

type TPricingCardMobile = TOurPricing & {
  index: number
}

const PricingCardMobile = (props: TPricingCardMobile) => {
  const { name, pricing, details, href } = props

  return (
    <div className='grid'>
      <div className={cn("bg-[#ffdecc] ~p-5/8")}>
        <props.icon className='size-7 flex-shrink-0' />
        <h3 className='mt-1 ~text-lg/xl'>{name}</h3>
      </div>
      <div className={cn("bg-[#ffdecc]/40 ~p-5/8")}>
        <div>
          <div className='space-y-0.5'>
            <p className='text-xs uppercase'>Starting AT</p>
            <div className='flex items-center font-semibold ~text-2xl/3xl'>
              <p className=''>
                {pricing.currency}
                {pricing.amount}
              </p>
              {pricing.unit && (
                <span className='pl-0.5 pt-2 text-xs'>/ {pricing.unit}</span>
              )}
            </div>
            {details.note && (
              <p className='text-xs capitalize text-zinc-400'>{details.note}</p>
            )}
          </div>
          <span className='mx-auto block h-[1px] w-[80%] bg-zinc-200 ~my-4/5' />
          <ul className='list-disc text-sm ~pl-5/8'>
            {details.additionalInfo.map((item, idx) => (
              <li key={idx}>{item}</li>
            ))}
          </ul>
        </div>
        <ButtonLink
          // href='/quote-calculator'
          href={`/services/${href}`}
          className='w-full bg-white text-black outline outline-1 outline-black ~mt-6/7'
          size='sm'
        >
          Get Instant Quote
        </ButtonLink>
      </div>
    </div>
  )
}

export default PricingCardMobile
