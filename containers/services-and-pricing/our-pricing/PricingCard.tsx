import { ButtonLink } from "@/app/components/ui"
import { cn } from "@/app/shared/utils"
import { TOurPricing } from "@/config"

type TPricingCard = TOurPricing & {
  index: number
}

const PricingCard = (props: TPricingCard) => {
  const { name, pricing, details, index, href } = props

  return (
    <div
      className={cn(
        "grid grid-cols-[2.5fr_1.5fr_2fr_1fr] items-center ~gap-5/8 ~px-5/8 ~py-3/5",
        index % 2 !== 0 ? "bg-[#F9F9F9]" : "bg-[#E8E8E8]",
      )}
    >
      <div className='flex items-center gap-3'>
        <props.icon className='flex-shrink-0' />
        <h3 className='~text-lg/xl'>{name}</h3>
      </div>

      <ol className={`list-decimal text-sm`}>
        {details.additionalInfo.map((item, idx) => (
          <li key={idx}>{item}</li>
        ))}
      </ol>

      <div className='flex flex-col space-y-0.5'>
        <p className='text-xs uppercase'>Starting AT</p>
        <div className='flex items-center font-semibold ~text-xl/3xl'>
          <p className=''>
            {pricing.currency}
            {pricing.amount}
          </p>
          {pricing.unit && (
            <span className='pl-0.5 pt-2 text-xs'>/ {pricing.unit}</span>
          )}
        </div>
        {details.note && (
          <p className='text-xs capitalize text-zinc-400'>{details.note}</p>
        )}
      </div>
      <ButtonLink
        href={`/services/${href}`}
        className='h-fit rounded-[4px] border border-transparent bg-white text-black'
        style={{
          borderImage: "linear-gradient(90deg, #FF5B00 0%, #FF5B00 100%) 1",
          borderImageSlice: 1,
          borderStyle: "solid",
        }}
        size='sm'
      >
        Get instant quote
      </ButtonLink>
    </div>
  )
}

export default PricingCard
