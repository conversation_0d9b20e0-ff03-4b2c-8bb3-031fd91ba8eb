import { cn } from "@/app/shared/utils"
import { motion } from "framer-motion"

type DeliveryStatusProps = {
  deliveryDate: string
  status: string
  location: string
}

const DeliveryStatus = ({
  deliveryDate,
  status,
  location,
}: DeliveryStatusProps) => {
  // Parse the date
  const date = new Date(deliveryDate)
  const day = date.getDate()
  const month = date.toLocaleString("default", { month: "long" })
  const year = date.getFullYear()
  const weekday = date.toLocaleString("default", { weekday: "long" })

  // Determine status color and icon based on status
  const getStatusInfo = (status: string) => {
    const statusLower = status.toLowerCase()
    if (statusLower.includes("delivered")) {
      return { color: "text-green-500", bgColor: "bg-green-500" }
    }
    if (statusLower.includes("out for delivery")) {
      return { color: "text-blue-500", bgColor: "bg-blue-500" }
    }
    if (statusLower.includes("transit")) {
      return { color: "text-yellow-500", bgColor: "bg-yellow-500" }
    }
    return { color: "text-primary", bgColor: "bg-primary" }
  }

  const { color: statusColor, bgColor } = getStatusInfo(status)

  return (
    <div className='rounded-lg bg-white p-6 shadow-sm'>
      <div className='mb-4 flex items-start justify-between'>
        <div>
          <h3 className='font-medium text-gray-500'>Delivered On</h3>
          <h2 className='text-2xl font-bold'>{weekday}</h2>
          <h3 className='text-gray-500'>{month}</h3>
        </div>
        <div
          className={cn(
            "rounded-full px-3 py-1 text-sm font-medium text-white",
            bgColor,
          )}
        >
          {status}
        </div>
      </div>

      <div className='flex items-center justify-center rounded-lg bg-gray-50 py-6'>
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5 }}
          className={cn("text-[120px] font-bold leading-none", statusColor)}
        >
          {day}
        </motion.div>
      </div>

      <div className='mt-2 text-center'>
        <span className='text-sm text-gray-500'>{year}</span>
      </div>

      <div className='mt-6 flex items-center justify-between rounded-lg bg-gray-50 p-3'>
        <div className='flex items-center'>
          <div
            className={cn("mr-2 h-4 w-4 flex-shrink-0 rounded-full", bgColor)}
          >
            <motion.div
              className='h-full w-full rounded-full opacity-50'
              animate={{ scale: [1, 1.5, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
              style={{ backgroundColor: statusColor.replace("text-", "bg-") }}
            />
          </div>
          <span className={cn("font-medium", statusColor)}>Current Status</span>
        </div>
        <div className='text-sm font-medium text-gray-700'>{location}</div>
      </div>
    </div>
  )
}

export default DeliveryStatus
