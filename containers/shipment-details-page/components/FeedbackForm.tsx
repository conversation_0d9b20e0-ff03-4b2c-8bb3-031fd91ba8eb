import { Button } from "@/app/components/ui"
import { useState } from "react"

type FeedbackFormProps = {
  trackingId: string
}

const FeedbackForm = ({ trackingId }: FeedbackFormProps) => {
  const [feedbackSubmitted, setFeedbackSubmitted] = useState(false)
  const [recommendationValue, setRecommendationValue] = useState<number | null>(
    null,
  )
  const [remarks, setRemarks] = useState("")

  const handleSubmitFeedback = () => {
    // Here you would typically send the feedback to your backend
    console.info({
      recommendationValue,
      remarks,
      trackingId,
    })
    setFeedbackSubmitted(true)
  }

  return (
    <div className='rounded-lg bg-white p-4 shadow-sm sm:p-6'>
      <div className='mb-4 flex items-center'>
        <div className='mr-2'>
          <svg
            xmlns='http://www.w3.org/2000/svg'
            width='24'
            height='24'
            viewBox='0 0 24 24'
            fill='none'
            stroke='currentColor'
            strokeWidth='2'
            strokeLinecap='round'
            strokeLinejoin='round'
          >
            <path d='M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3'></path>
          </svg>
        </div>
        <h2 className='text-lg font-semibold'>Delivery Feedback</h2>
      </div>

      {!feedbackSubmitted ? (
        <div className='rounded-lg bg-gray-50 p-3 sm:p-4 lg:p-10'>
          <h3 className='mb-4 font-medium'>
            How likely are you to recommend NexMove to friends & family?
          </h3>

          <div className='my-6 grid grid-cols-11 gap-1 sm:flex sm:justify-between'>
            {[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((value) => (
              <button
                key={value}
                onClick={() => setRecommendationValue(value)}
                className={`flex h-7 w-7 items-center justify-center rounded-full border sm:h-8 sm:w-8 ${
                  recommendationValue === value
                    ? "border-primary bg-primary text-white"
                    : "border-gray-300 hover:border-primary"
                }`}
              >
                {value}
              </button>
            ))}
          </div>

          <div className='mb-6 flex justify-between text-xs sm:text-sm'>
            <span className='text-gray-500'>Not at all likely</span>
            <span className='text-gray-500'>Extremely likely</span>
          </div>

          <div className='mb-4'>
            <label className='mb-2 block font-medium text-gray-700'>
              Share your experience
            </label>
            <textarea
              className='w-full rounded-md border border-gray-300 bg-white p-3 focus:outline-none focus:ring-1 focus:ring-primary'
              placeholder='Please enter your remarks (Max. 250 characters)'
              maxLength={250}
              rows={3}
              value={remarks}
              onChange={(e) => setRemarks(e.target.value)}
            ></textarea>
          </div>

          <Button
            onClick={handleSubmitFeedback}
            disabled={recommendationValue === null}
            className='w-full'
          >
            Submit Feedback
          </Button>
        </div>
      ) : (
        <div className='rounded-lg bg-green-50 p-4 text-center sm:p-6'>
          <svg
            className='mx-auto mb-3 h-10 w-10 text-green-500 sm:mb-4 sm:h-12 sm:w-12'
            xmlns='http://www.w3.org/2000/svg'
            fill='none'
            viewBox='0 0 24 24'
            stroke='currentColor'
          >
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth={2}
              d='M5 13l4 4L19 7'
            />
          </svg>
          <h2 className='mb-2 text-lg font-semibold'>
            Thank You for Your Feedback!
          </h2>
          <p className='text-gray-600'>
            Your feedback helps us improve our services.
          </p>
        </div>
      )}
    </div>
  )
}

export default FeedbackForm
