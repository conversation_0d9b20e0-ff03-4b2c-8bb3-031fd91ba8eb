type TpropsType = {
  user: {
    "Full Name": string
    Address: string
    Country: string
    City: string
    "State/Province": string
    "Postal Code": string
    "Telephone Number": string
    "Email Address": string
  }
  cardTitle: string
}

const UserInfoCard = ({ user, cardTitle }: TpropsType) => {
  return (
    <div className='overflow-hidden rounded-2xl border border-primary bg-white'>
      <div className='bg-primary/30 ~px-6/8 ~py-3/4'>
        <span className='font-medium ~text-base/lg'>{cardTitle}</span>
      </div>
      <div className='grid ~gap-2.5/4 ~px-6/8 ~py-5/7'>
        {Object.keys(user).map((key, index) => (
          <RenderField
            key={index}
            label={key}
            value={user[key as keyof typeof user]}
          />
        ))}
      </div>
    </div>
  )
}

export default UserInfoCard

const RenderField = ({ label, value }: { label: string; value: string }) => {
  return (
    <div className='font-inter text-sm font-medium'>
      <span>{label}:</span> <span className='text-zinc-400'>{value}</span>
    </div>
  )
}
