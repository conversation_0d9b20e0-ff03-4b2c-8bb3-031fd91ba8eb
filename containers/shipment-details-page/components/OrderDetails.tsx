type OrderDetailsProps = {
  orderId: string
  orderDate: string
  trackingId: string
  from: string
  to: string
}

const OrderDetails = ({
  orderId,
  orderDate,
  trackingId,
  from,
  to,
}: OrderDetailsProps) => {
  return (
    <div className='rounded-lg bg-white p-6 shadow-sm'>
      <div className='mb-4 flex items-center'>
        <div className='mr-2'>
          <svg
            xmlns='http://www.w3.org/2000/svg'
            width='24'
            height='24'
            viewBox='0 0 24 24'
            fill='none'
            stroke='currentColor'
            strokeWidth='2'
            strokeLinecap='round'
            strokeLinejoin='round'
          >
            <path d='M21 8a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8z'></path>
            <path d='M21 12H3'></path>
            <path d='M7 8v8'></path>
          </svg>
        </div>
        <h2 className='text-lg font-semibold'>Order Details</h2>
      </div>

      <div className='rounded-lg bg-gray-50 p-4'>
        <div className='space-y-3 font-inter'>
          <div className='flex justify-between border-b border-gray-200 py-2'>
            <span className='text-gray-500'>Order ID</span>
            <span className='font-medium'>{orderId}</span>
          </div>
          <div className='flex justify-between border-b border-gray-200 py-2'>
            <span className='text-gray-500'>Order Placed On</span>
            <span className='font-medium'>{orderDate}</span>
          </div>
          <div className='flex justify-between border-b border-gray-200 py-2'>
            <span className='text-gray-500'>Tracking ID</span>
            <span className='font-medium text-primary'>{trackingId}</span>
          </div>
          <div className='flex justify-between border-b border-gray-200 py-2'>
            <span className='text-gray-500'>From</span>
            <span className='font-medium'>{from}</span>
          </div>
          <div className='flex justify-between py-2'>
            <span className='text-gray-500'>To</span>
            <span className='font-medium'>{to}</span>
          </div>
        </div>
      </div>

      <div className='mt-4 rounded-lg border border-yellow-200 bg-yellow-50 p-3 text-sm'>
        <div className='flex items-start'>
          <div className='mr-2 mt-0.5 text-yellow-500'>
            <svg
              xmlns='http://www.w3.org/2000/svg'
              width='16'
              height='16'
              viewBox='0 0 24 24'
              fill='none'
              stroke='currentColor'
              strokeWidth='2'
              strokeLinecap='round'
              strokeLinejoin='round'
            >
              <circle cx='12' cy='12' r='10'></circle>
              <line x1='12' y1='8' x2='12' y2='12'></line>
              <line x1='12' y1='16' x2='12.01' y2='16'></line>
            </svg>
          </div>
          <p className='text-gray-700'>
            For any changes to your order, please contact our customer support
            team with your tracking ID.
          </p>
        </div>
      </div>
    </div>
  )
}

export default OrderDetails
