import "./Stepper.css"
import { useState } from "react"
import { AnimatePresence, motion } from "framer-motion"
import { cn } from "@/app/shared/utils"

type TpropsType = {
  STEPS: {
    title: string
    desc: string
    status: string
  }[]
  progressCount: number
}

const Stepper = ({ STEPS, progressCount }: TpropsType) => {
  const [currentStep, setCurrentStep] = useState(progressCount)

  //   const handleNext = () => {
  //     if (currentStep !== STEPS.length) {
  //       setCurrentStep((prevStep) => prevStep + 1);
  //     }
  //   };

  //   const handlePrev = () => {
  //     if (currentStep !== 1) {
  //       setCurrentStep((prevStep) => prevStep - 1);
  //     }
  //   };

  return (
    <div>
      <div className='step-container max-lg:flex-col'>
        {STEPS?.map((step, index) => (
          <div
            key={index}
            className={cn(
              "step-item max-lg:flex-row max-lg:justify-start max-lg:gap-10 max-sm:gap-5",
              index + 1 <= currentStep && "complete",
            )}
          >
            <div
              className={cn(
                "flex-1 text-right text-xs text-gray-400",
                index + 1 <= currentStep && "text-green-500",
              )}
            >
              {step.status}
            </div>
            <div className='step p-1'>
              <AnimatePresence initial={false} mode='popLayout'>
                {index + 1 <= currentStep && (
                  <motion.span
                    animate={{ opacity: [0, 1], scale: [0, 1.1, 1] }}
                    exit={{ opacity: 0, scale: 0 }}
                    transition={{ delay: 0.5 }}
                    className='size-full rounded-full bg-green-500'
                  >
                    {index + 1 === currentStep && (
                      <span className='block size-full animate-ping rounded-full bg-green-500' />
                    )}
                  </motion.span>
                )}
              </AnimatePresence>
            </div>
            <div className='flex-1 text-xs text-gray-400 lg:text-center'>
              <span>{step.desc}</span>
            </div>
            {index !== 0 && (
              <p className='bar'>
                <span
                  className={`progress ${
                    currentStep === index + 1 && "complete"
                  } ${index + 1 <= currentStep && "complete"}`}
                ></span>
              </p>
            )}
          </div>
        ))}
      </div>
      {/* <div className="flex justify-center gap-3 mt-10">
        <button
          className="px-5 py-1 rounded-md bg-purple-500 text-white hover:bg-purple-600 transition-colors"
          onClick={handlePrev}
        >
          Prev
        </button>
        <button
          className="relative px-5 py-1 rounded-md bg-purple-500 text-white hover:bg-purple-600 transition-colors"
          onClick={handleNext}
        >
          Next
          <span className="absolute pointer-events-none -right-2/3 text-gray-300 bottom-0 text-sm font-medium">
            Demo
          </span>
        </button>
      </div> */}
    </div>
  )
}

export default Stepper
