.step-container {
  @apply flex items-start justify-center;
}

.step-item {
  @apply relative flex w-full flex-1 flex-col items-center justify-center gap-2 max-lg:flex-row max-lg:py-5;
}

.bar {
  @apply absolute -top-1/2 h-[2px] w-full overflow-hidden bg-zinc-200 max-lg:h-full max-lg:w-[1px] lg:right-1/2 lg:top-[35px];
}

.bar .progress {
  @apply absolute inset-0 inline-block origin-top scale-y-0 bg-green-500 transition-transform duration-700 lg:origin-left lg:scale-x-0;
}

.bar .progress.complete {
  @apply scale-y-100 lg:scale-x-100;
}

.step {
  @apply relative z-10 flex size-6 flex-shrink-0 items-center justify-center rounded-full bg-zinc-200 font-medium text-white transition-colors duration-500;
}

.complete .step {
  @apply border border-green-500 bg-green-100;
}

.complete p {
  @apply text-black;
}

.complete p:last-child {
  @apply text-gray-400;
}
