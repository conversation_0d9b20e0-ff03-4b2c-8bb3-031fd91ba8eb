import Image from "next/image"
import { useState } from "react"

type CarrierInfoProps = {
  carrierName: string
  trackingId: string
  activity?: string
  location?: string
}

const CarrierInfo = ({
  carrierName,
  trackingId,
  activity,
  location,
}: CarrierInfoProps) => {
  const [imageError, setImageError] = useState(false)

  // Map carrier names to their logo paths
  const carrierLogos: Record<string, string> = {
    bluedart: "/images/carriers/bluedart.png",
    dhl: "/images/carriers/dhl.png",
    fedex: "/images/carriers/fedex.png",
    shiprocket: "/images/carriers/shiprocket.png",
    // Add more carriers as needed
    default: "/images/logo.png", // Default logo
  }

  // Get the logo path, defaulting to the default logo if not found
  const logoPath =
    carrierLogos[carrierName.toLowerCase()] || carrierLogos.default

  return (
    <div className='rounded-lg bg-white p-6 shadow-sm'>
      <div className='mb-4 flex items-center'>
        <div className='mr-2'>
          <svg
            xmlns='http://www.w3.org/2000/svg'
            width='24'
            height='24'
            viewBox='0 0 24 24'
            fill='none'
            stroke='currentColor'
            strokeWidth='2'
            strokeLinecap='round'
            strokeLinejoin='round'
          >
            <rect x='2' y='7' width='20' height='14' rx='2' ry='2'></rect>
            <path d='M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16'></path>
          </svg>
        </div>
        <h2 className='text-lg font-semibold'>Carrier Information</h2>
      </div>

      <div className='flex items-center rounded-lg bg-gray-50 p-4'>
        <div className='relative flex h-16 w-16 flex-shrink-0 items-center justify-center overflow-hidden rounded-full border border-gray-200 bg-white'>
          {!imageError ? (
            <Image
              src={logoPath}
              alt={`${carrierName} logo`}
              fill
              className='object-contain p-2'
              onError={() => setImageError(true)}
            />
          ) : (
            <div className='text-2xl font-bold text-primary'>
              {carrierName.charAt(0).toUpperCase()}
            </div>
          )}
        </div>
        <div className='ml-4'>
          <h3 className='text-lg font-medium'>{carrierName}</h3>
          <div className='mt-1 flex items-center'>
            <span className='mr-2 text-gray-500'>Tracking ID:</span>
            <span className='font-medium text-primary'>{trackingId}</span>
          </div>
        </div>
      </div>

      {activity && location && (
        <div className='mt-4 space-y-2 border-t pt-4'>
          <div className='flex'>
            <span className='w-24 flex-shrink-0 text-gray-500'>Activity:</span>
            <span className='font-medium'>{activity}</span>
          </div>
          <div className='flex'>
            <span className='w-24 flex-shrink-0 text-gray-500'>Location:</span>
            <span className='font-medium'>{location}</span>
          </div>
        </div>
      )}
    </div>
  )
}

export default CarrierInfo
