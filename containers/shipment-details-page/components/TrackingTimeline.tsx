import { cn } from "@/app/shared/utils"
import { motion } from "framer-motion"

type TrackingEvent = {
  checkpoint_date: string
  tracking_detail: string
  location: string
  checkpoint_delivery_status: string
  raw_status?: string
}

type TrackingTimelineProps = {
  events: TrackingEvent[]
}

const TrackingTimeline = ({ events = [] }: TrackingTimelineProps) => {
  // If no events, show a message
  if (!events || events.length === 0) {
    return (
      <div className='rounded-lg bg-white p-6 text-center shadow-sm'>
        <p className='text-gray-500'>No tracking information available.</p>
      </div>
    )
  }

  // Sort events by date (newest first)
  const sortedEvents = [...events].sort(
    (a, b) =>
      new Date(b.checkpoint_date).getTime() -
      new Date(a.checkpoint_date).getTime(),
  )

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return {
      day: date.getDate(),
      month: date.toLocaleString("default", { month: "short" }).toUpperCase(),
      time: date.toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      }),
    }
  }

  const getStatusColor = (status: string, isFirst: boolean) => {
    if (isFirst) return "bg-green-500"
    const statusLower = status.toLowerCase()
    if (statusLower.includes("delivered")) return "bg-green-500"
    if (statusLower.includes("out for delivery")) return "bg-blue-500"
    if (statusLower.includes("transit")) return "bg-gray-400"
    return "bg-gray-400"
  }

  return (
    <div className='rounded-lg bg-white p-6 shadow-sm'>
      <h2 className='mb-4 text-lg font-semibold'>Tracking History</h2>
      <div className='max-h-[500px] overflow-y-auto pr-2'>
        <div className='relative'>
          {sortedEvents.map((event, index) => {
            const { day, month, time } = formatDate(event.checkpoint_date)
            const isFirst = index === 0
            const statusColor = getStatusColor(
              event.checkpoint_delivery_status,
              isFirst,
            )
            const isLast = index === sortedEvents.length - 1

            return (
              <div key={index} className='relative mb-6 flex'>
                {/* Date column */}
                <div className='w-24 flex-shrink-0 font-medium'>
                  <div>
                    {day} {month}
                  </div>
                  <div className='text-xs text-gray-500'>{time}</div>
                </div>

                {/* Timeline dot and line column */}
                <div className='relative mx-4 flex w-4 items-start'>
                  <div
                    className={cn(
                      "z-10 flex h-4 w-4 items-center justify-center rounded-full",
                      statusColor,
                    )}
                  >
                    {/* {isFirst && (
                      <motion.div
                        className="absolute inset-0 rounded-full bg-green-500 opacity-50"
                        animate={{ scale: [1, 1.5, 1] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                    )} */}
                  </div>

                  {/* Vertical line - positioned absolutely to span between dots */}
                  {!isLast && (
                    <div className='absolute left-1/2 top-4 h-[calc(100%+1.5rem)] w-0.5 -translate-x-1/2 transform border-l-2 border-dashed border-gray-300'></div>
                  )}
                </div>

                {/* Event details column */}
                <div className='flex-1'>
                  <div className='font-medium'>
                    Activity:{" "}
                    <span className='font-normal text-darkGray/70'>
                      {event.tracking_detail}
                    </span>
                  </div>
                  <div className='mt-1 text-sm text-gray-600'>
                    Location:{" "}
                    <span className='text-gray-500'>{event.location} </span>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}

export default TrackingTimeline
