"use client"

import { <PERSON>rapper } from "@/app/shared/components/Wrapper"
import { trackingService } from "@/app/shared/services/tracking"
import { useQuery } from "@tanstack/react-query"
import Link from "next/link"
import { useParams } from "next/navigation"
import CarrierInfo from "./components/CarrierInfo"
import DeliveryStatus from "./components/DeliveryStatus"
import FeedbackForm from "./components/FeedbackForm"
import OrderDetails from "./components/OrderDetails"
import TrackingTimeline from "./components/TrackingTimeline"

// Sample data structure for reference
/*
const trackingDataSample = {
  data: {
    tracking_number: "80753879345",
    courier_code: "shiprocket",
    delivery_status: "delivered",
    origin_info: {
      trackinfo: [
        {
          checkpoint_date: "2025-03-25T16:35:00",
          checkpoint_delivery_status: "delivered",
          tracking_detail: "SIGNATURE IMAGE",
          location: "KATIHAR OFFICE",
          raw_status: "DELIVERED"
        },
        // More tracking events...
      ],
      milestone_date: {
        delivery_date: "2025-03-25T16:35:00"
      }
    },
    origin_city: "Pune",
    destination_city: "Katihar"
  }
};
*/

const ShipmentDetails = () => {
  const params = useParams()
  const id = params?.id

  const {
    data: trackingData,
    error,
    isLoading,
  } = useQuery({
    queryKey: ["tracking", id],
    queryFn: () => trackingService({ tracking_id: id?.toString() ?? "" }),
    enabled: !!id, // Only run the query if we have an ID
  })

  if (isLoading) {
    return (
      <div className='flex min-h-screen items-center justify-center'>
        <div className='h-12 w-12 animate-spin rounded-full border-b-2 border-t-2 border-primary'></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className='flex min-h-screen flex-col items-center justify-center'>
        <div className='mb-4 text-xl text-red-500'>
          Error loading tracking information
        </div>
        <div className='text-gray-600'>{error.message}</div>
        <Link
          href='/track-shipment'
          className='mt-6 text-primary hover:underline'
        >
          Try another tracking number
        </Link>
      </div>
    )
  }

  // Extract data from the API response
  const shipmentData = trackingData?.data || {}
  const trackingInfo = shipmentData.origin_info?.trackinfo || []

  // Get the latest checkpoint for delivery status
  const latestCheckpoint = trackingInfo[0] || {}

  // Get location for map
  const deliveryLocation = latestCheckpoint.location || "Unknown Location"

  // Format order date
  const orderDate = shipmentData.order_date
    ? new Date(shipmentData.order_date).toLocaleDateString("en-US", {
        day: "numeric",
        month: "short",
        year: "numeric",
      })
    : "N/A"

  // Get delivery date from milestone_date if available
  const deliveryDate =
    shipmentData.origin_info?.milestone_date?.delivery_date ||
    latestCheckpoint.checkpoint_date ||
    new Date().toISOString()

  // Get tracking number
  const trackingNumber = shipmentData.tracking_number || id?.toString() || ""

  // Get courier information
  const courierCode = shipmentData.courier_code || "default"
  const courierName = courierCode.charAt(0).toUpperCase() + courierCode.slice(1)

  // Get origin and destination
  const fromLocation = shipmentData.origin_city || "Unknown"
  const toLocation = shipmentData.destination_city || "Unknown"

  return (
    <div>
      <Wrapper>
        <div className='uppercase ~text-xs/sm ~pb-1/2'>
          <Link
            // href={"/track-shipment"}
            target='_blank'
            rel='noopener noreferrer'
            href='https://development9p.trackingmore.org/'
            className='hover:underline'
            aria-label='Track Shipment Button'
          >
            Track Shipment
          </Link>
          {" / "}
          <span className='text-primary'>Shipment Details</span>
        </div>

        <div className='mt-6 grid w-full gap-6 lg:grid-cols-2'>
          {/* Left Column */}
          <div className='w-full space-y-6'>
            {/* Delivery Status */}
            <DeliveryStatus
              deliveryDate={deliveryDate}
              status={latestCheckpoint.raw_status || "In Transit"}
              location={deliveryLocation}
            />

            {/* Map */}
            <div className='h-64 w-full overflow-hidden rounded-lg bg-white p-2 shadow-sm md:h-80'>
              <iframe
                width='100%'
                height='100%'
                style={{ border: "0" }}
                loading='lazy'
                src={`https://www.google.com/maps/embed/v1/place?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&q=${encodeURIComponent(deliveryLocation)}`}
                title='Delivery Location Map'
              ></iframe>
            </div>

            {/* Carrier Info */}
            <CarrierInfo
              carrierName={courierName}
              trackingId={trackingNumber}
              activity={latestCheckpoint.tracking_detail}
              location={latestCheckpoint.location}
            />
          </div>

          {/* Right Column */}
          <div className='space-y-6'>
            {/* Order Details */}
            <OrderDetails
              orderId={trackingNumber}
              orderDate={orderDate}
              trackingId={trackingNumber}
              from={fromLocation}
              to={toLocation}
            />

            {/* Tracking Timeline */}
            <TrackingTimeline events={trackingInfo} />
          </div>
        </div>

        {/* Feedback Form */}
        <FeedbackForm trackingId={trackingNumber} />
      </Wrapper>

      <div className='bg-primary/10 ~mt-10/12 ~mb-10/12 ~py-6/8'>
        <p className='text-center font-inter text-sm'>
          Need help with your shipment?{" "}
          <Link href='/contact' className='text-primary underline'>
            Get in touch
          </Link>
        </p>
      </div>
    </div>
  )
}

export default ShipmentDetails
