import { formatDate } from "@/app/shared/utils"
import { LogoIcon } from "@/Icons"
import { PostSummary, urlFor } from "@/lib/sanity"
import Image from "next/image"
import Link from "next/link"

const BlogCard = (props: PostSummary) => {
  const {
    title,
    slug,
    mainImage,
    publishedAt,
    isTrending,
    readingTime,
    small_description,
    author: { name, profile },
  } = props

  return (
    <Link
      scroll={true}
      href={`/blog/${slug.current}`}
      className='bg-zinc-200 ~p-3.5/5'
    >
      <div className='relative aspect-[16/10] w-full'>
        <Image
          priority
          src={urlFor(mainImage).url()}
          alt={title}
          fill
          sizes='(max-width: 768px) 100vw, 33vw'
          className='object-cover'
        />
      </div>
      <div className='~py-3/4'>
        <h3 className='line-clamp-1 font-medium ~text-lg/3xl'>{title}</h3>
        {small_description && (
          <p className='line-clamp-2 font-inter text-zinc-500 ~mb-2/5 ~pt-1/2'>
            {small_description}
          </p>
        )}
      </div>
      <div className='flex items-end justify-between gap-3'>
        <div className='flex items-center ~gap-1/2'>
          <div className='relative aspect-square overflow-hidden rounded-full bg-white ~w-10/14'>
            {profile ? (
              <Image
                priority
                src={urlFor(profile).url()}
                alt={title}
                fill
                sizes='(max-width: 768px) 100vw, 33vw'
                className='object-contain'
              />
            ) : (
              <LogoIcon className='size-full object-contain' />
            )}
          </div>
          <div className='grid'>
            <span className='uppercase text-primary'>{name}</span>
            <span className='text-zinc-500'>{formatDate(publishedAt)}</span>
          </div>
        </div>
        <span className='line-clamp-1 max-w-28 pb-0.5 text-zinc-500 max-md:hidden'>
          {readingTime}
        </span>
      </div>
    </Link>
  )
}

export default BlogCard
