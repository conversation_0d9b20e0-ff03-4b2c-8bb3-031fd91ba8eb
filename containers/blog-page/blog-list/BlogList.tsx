import { PostSummary } from "@/lib/sanity"
import { BlogCard } from "./index"
import { SectionWrapper } from "@/app/shared/components"

const BlogList = ({ posts }: { posts: PostSummary[] }) => {
  return (
    <SectionWrapper>
      <div className='grid grid-cols-2 ~gap-3/4 sm:grid-cols-3 lg:grid-cols-4'>
        {posts.map((post) => (
          <BlogCard {...post} key={post._id} />
        ))}
        {posts.map((post) => (
          <BlogCard {...post} key={post._id} />
        ))}
        {posts.map((post) => (
          <BlogCard {...post} key={post._id} />
        ))}
        {posts.map((post) => (
          <BlogCard {...post} key={post._id} />
        ))}
      </div>
    </SectionWrapper>
  )
}

export default BlogList
