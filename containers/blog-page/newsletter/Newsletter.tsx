"use client"

import { <PERSON><PERSON> } from "@/app/components/ui"
import { <PERSON>rap<PERSON> } from "@/app/shared/components/Wrapper"
import { WavePatternIcon } from "@/Icons"
import { useForm } from "react-hook-form"

const Newsletter = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm()

  const onSubmit = (data: any) => {
    alert("Thank you")
  }

  // validate email
  const validateEmail = (value: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(value)) return "Invalid email format"
  }

  return (
    <Wrapper className='relative isolate min-h-60 overflow-hidden bg-darkGray ~py-16/24'>
      <span className='absolute inset-0 -z-10'>
        <WavePatternIcon className='w-full scale-y-75' />
      </span>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className='mx-auto max-w-screen-sm text-white ~space-y-8/10'
      >
        <h1 className='text-center !leading-tight ~text-2xl/4xl'>
          Subscribe For the <br /> lastest updates
        </h1>
        <div className='grid font-inter'>
          <label htmlFor='email' className='font-medium ~mb-1/1.5'>
            Email Address
          </label>
          <input
            type='text'
            id='email'
            placeholder='Enter your email here...'
            className='rounded-md border-2 border-zinc-600 bg-black outline-none ~px-3/3.5 ~py-2/2.5 hover:border-zinc-300 focus-visible:border-blue-500'
            {...register("email", {
              required: "Email is required",
              validate: validateEmail,
            })}
          />
          {errors.email && (
            <small className='text-red-500 ~mt-0/0.5 ~pl-3.5/4'>
              {errors.email.message as string}
            </small>
          )}
        </div>
        <Button size='sm' className='mx-auto block w-[60%] max-w-sm font-inter'>
          SUBSCRIBE
        </Button>
      </form>
    </Wrapper>
  )
}

export default Newsletter
