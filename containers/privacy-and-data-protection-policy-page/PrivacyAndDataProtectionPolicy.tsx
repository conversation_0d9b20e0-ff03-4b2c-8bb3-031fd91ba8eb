import { TextBanner } from "@/app/components/common"
import {
  <PERSON><PERSON><PERSON>er,
  SectionTitle,
  SectionWrapper,
} from "@/app/shared/components"
import { PRIVACY_AND_DATA_PROTECTION_POLICY } from "@/config"

const PrivacyAndDataProtectionPolicy = () => {
  return (
    <>
      <PageHeader secondLine='Terms of Use' />
      <SectionWrapper className='font-inter !leading-tight text-zinc-400 ~text-sm/lg'>
        <SectionTitle
          title='Terms of Use - Third-Party Authentication Consent '
          subtitle='LEGAL'
          className='text-black'
        />
        <p>{PRIVACY_AND_DATA_PROTECTION_POLICY.introduction}</p>
        <div className='~mt-5/7 ~space-y-5/7'>
          {PRIVACY_AND_DATA_PROTECTION_POLICY.data.map((data, index) => (
            <div key={index}>
              <p className='uppercase text-primary ~mb-3/5'>{data.title}</p>
              {data.contentList ? (
                <ul className='list-disc'>
                  {data.contentList.map((list, index) => (
                    <li className='ml-5' key={index}>
                      {list}
                    </li>
                  ))}
                </ul>
              ) : (
                <p>{data.content}</p>
              )}
            </div>
          ))}
        </div>
      </SectionWrapper>
      <TextBanner
        text='Thank you for choosing Nex Move. We appreciate your understanding and are here to assist with any questions or concerns regarding our Privacy and Data Protection Policy.'
        className='mx-auto max-w-screen-xl'
      />
    </>
  )
}

export default PrivacyAndDataProtectionPolicy
