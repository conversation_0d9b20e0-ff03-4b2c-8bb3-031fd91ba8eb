import { SectionTitle } from "@/app/shared/components"
import { Wrapper } from "@/app/shared/components/Wrapper"
import { CEO_NAM<PERSON>, MESSAGE_FROM_CEO } from "@/config"
import { QuoteOpenIcon } from "@/Icons"
import { Fragment } from "react"

const MessageFromCeo = () => {
  return (
    <div
      aria-label='Message from the CEO of NexMove Section'
      className='bg-black text-white'
    >
      <Wrapper className='~py-14/20'>
        <SectionTitle
          subtitle=''
          title='MESSAGE FROM THE CEO'
          titleElement={"h2"}
        />
        <div className='font-inter ~space-y-5/10'>
          <div>
            <QuoteOpenIcon className='~w-10/12' />
            <p className='italic ~text-base/xl'>Welcome to NexMove!</p>
          </div>
          <div aria-hidden className='font-thin ~text-base/xl ~space-y-5/8'>
            {MESSAGE_FROM_CEO.split("//").map((paragraph, index) => (
              <p key={index} className='sm:leading-snug'>
                {paragraph.split("/").map((line, idx) => (
                  <Fragment key={idx}>
                    {line}
                    {idx !== paragraph.split("/").length - 1 && <br />}
                  </Fragment>
                ))}
              </p>
            ))}
          </div>
          <p aria-label='Message from the CEO' className='sr-only hidden'>
            {MESSAGE_FROM_CEO}
          </p>
          <div className='!mt-16 font-manrope max-sm:!mt-10'>
            <p className='~text-xl/2xl'>{CEO_NAME}</p>
            <span className='uppercase text-primary ~text-xs/sm'>
              ceo, next-move
            </span>
          </div>
        </div>
      </Wrapper>
    </div>
  )
}

export default MessageFromCeo
