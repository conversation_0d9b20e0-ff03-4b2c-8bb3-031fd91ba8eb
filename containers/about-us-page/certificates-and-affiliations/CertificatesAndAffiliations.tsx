import { SectionTitle, SectionWrapper } from "@/app/shared/components"
import Image from "next/image"

const CertificatesAndAffiliations = () => {
  const LOGO_LIST = [
    "/images/certificates-and-affiliations/IAM.webp",
    "/images/certificates-and-affiliations/IMA.webp",
    "/images/certificates-and-affiliations/ISO.webp",
    "/images/certificates-and-affiliations/ISO_2.webp",
    "/images/certificates-and-affiliations/IAF.webp",
    "/images/certificates-and-affiliations/ISO_3.webp",
    "/images/certificates-and-affiliations/MSME.webp",
  ]

  return (
    <SectionWrapper className='~pb-4/5'>
      <SectionTitle
        subtitle='Our Values'
        title='Certificates and Affiliations'
        titleElement={"h6"}
      />
      <div className='grid grid-cols-7 gap-y-2 max-sm:grid-cols-5'>
        {LOGO_LIST.map((logo, index) => (
          <div key={index} className='relative aspect-video w-full'>
            <Image
              src={logo}
              alt=''
              fill
              className='object-contain'
              draggable={false}
            />
          </div>
        ))}
      </div>
    </SectionWrapper>
  )
}

export default CertificatesAndAffiliations
