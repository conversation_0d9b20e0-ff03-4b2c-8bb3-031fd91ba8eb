import { SectionTitle, SplitView } from "@/app/shared/components"
import { WHAT_WE_OFFER } from "@/config"

const WhatWeOffer = () => {
  return (
    <>
      <SectionTitle
        className='~px-4/16'
        subtitle='Our Values'
        title='Why Choose Nex Mov ?'
        titleElement='h4'
      />
      <div>
        <SplitView
          image='/images/cargo.webp'
          labelBoldText='NEX'
          labelText='MOVE'
        >
          <div className='grid ~gap-8/10'>
            {WHAT_WE_OFFER.map((item) => (
              <div key={item.title} className='flex ~gap-2/3'>
                <item.icon className='flex-shrink-0 ~w-7/8' />
                <div>
                  <h3 className='~text-lg/xl'>{item.title}</h3>
                  <p className='max-w-lg pt-1 leading-tight text-zinc-400'>
                    {item.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </SplitView>
      </div>
    </>
  )
}

export default WhatWeOffer
