import { Info<PERSON>ard, SectionTitle, SectionWrapper } from "@/app/shared/components"
import { Carousel } from "@/app/shared/components/carousel"
import { INDUSTRIES_SERVED } from "@/config"

const IndustriesServed = () => {
  return (
    <SectionWrapper aria-label='Industries Served' className='!px-0 ~pt-14/20'>
      <SectionTitle
        subtitle='INDUSTRIES SERVED'
        title='At NEX Move, we pride ourselves on providing comprehensive moving and logistics solutions tailored to a wide range of industries. Our expertise and specialized services ensure seamless operations, no matter the scale or complexity of your needs. Here are the key industries we serve:'
        className='~px-4/16'
        titleElement='h5'
      />
      <div className='~mt-5/10'>
        <Carousel
          slideNodes={INDUSTRIES_SERVED.map((card) => (
            <InfoCard key={card.title} {...card} />
          ))}
          showActionButtons={false}
          fitContent
          dragFree
        />
      </div>
    </SectionWrapper>
  )
}

export default IndustriesServed
