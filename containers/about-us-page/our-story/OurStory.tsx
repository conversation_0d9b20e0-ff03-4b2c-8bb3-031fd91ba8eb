import { SectionTitle, SplitView } from "@/app/shared/components"
import { Wrapper } from "@/app/shared/components/Wrapper"

const OurStory = () => {
  return (
    <div>
      <Wrapper className='~pb-8/16'>
        <SectionTitle
          subtitle='Our Something'
          title='Our Story'
          titleElement={"h2"}
        />
        <p className='!leading-tight text-zinc-500 ~text-sm/xl'>
          Founded with a vision to simplify the logistics and relocation
          experience, NexMove began as a local service provider and has since
          expanded its reach to become a global leader. We understand that every
          move, whether it's personal or business-related, requires attention to
          detail, reliability, and a personal touch. <br aria-hidden /> From
          packing and moving to warehousing, courier, and ambulance services, we
          offer a wide range of services that cater to the diverse needs of our
          clients. We have built a reputation for excellence; driven by our
          customer-first approach and the trust we&apos;ve earned over the
          years. <br aria-hidden /> We pride ourselves on delivering dependable
          and efficient cargo solutions, tailoring our services to foster
          strong, lasting customer relationships. Backed by experienced
          professionals, industry expertise, and adherence to global quality
          standards, we guarantee top-tier service in the logistics industry.
        </p>
      </Wrapper>
      <Wrapper className='bg-gradient-to-r from-primary to-secondary ~py-5/7'>
        <p className='mx-auto max-w-screen-xl font-semibold !leading-tight text-white ~text-base/xl'>
          &ldquo;As we move forward, we remain dedicated to innovation,
          technology, and sustainable growth, constantly adapting to the
          evolving logistics landscape to serve you better.&rdquo;
        </p>
      </Wrapper>
      <SplitView
        image='/images/our-vision.webp'
        labelBoldText='OUR'
        labelText='VISION'
      >
        <span className='~text-xs/sm'>OUR VISION</span>
        <p className='!leading-tight text-zinc-400 ~text-base/xl ~pt-1/2'>
          <span className='text-black'>To be the leading logistics</span> and
          moving services provider worldwide, recognized for innovation,
          reliability, and customer-centric solutions that empower individuals
          and businesses to move forward seamlessly and with confidence.
        </p>
      </SplitView>
      <SplitView
        image='/images/boxes.webp'
        labelBoldText='OUR'
        labelText='MISSION'
        rtl
      >
        <span className='~text-xs/sm'>OUR MISSION</span>
        <p
          aria-label='Our Mission'
          aria-description="At NexMove, our mission is to provide comprehensive, reliable, and cost-effective logistics and relocation solutions that exceed our customers' expectations. We strive to simplify complex moving and transportation challenges through personalized service, industry expertise, and cutting-edge technology. We are committed to fostering long-term relationships with our clients by delivering exceptional service, ensuring their satisfaction at every step of the way. Whether you're relocating your home, managing a business move, or handling cargo, our team is here"
          className='!leading-tight text-zinc-400 ~text-base/xl ~space-y-3/5 ~pt-1/2'
        >
          At NexMove, our mission is{" "}
          <span className='text-black'>
            to provide comprehensive, reliable, and cost-effective logistics and
            relocation solutions that exceed our customers&apos; expectations.
          </span>{" "}
          We strive to simplify complex moving and transportation challenges
          through personalized service, industry expertise, and cutting-edge
          technology.
          <span className='inline-block'>
            We are committed to fostering long-term relationships with our
            clients by delivering exceptional service, ensuring their
            satisfaction at every step of the way. Whether you're relocating
            your home, managing a business move, or handling cargo, our team is
            here to ensure a smooth, efficient, and worry-free experience.
          </span>
        </p>
      </SplitView>
    </div>
  )
}

export default OurStory
