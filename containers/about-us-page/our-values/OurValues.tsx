import { SectionTitle, SectionWrapper } from "@/app/shared/components"
import { OUR_VALUES } from "@/config"

const OurValues = () => {
  return (
    <SectionWrapper className='~pt-14/20'>
      <SectionTitle
        subtitle='Our Values'
        title='Together, our mission, vision, and values drive our dedication to making every move a seamless and rewarding experience for our customers.'
        titleElement='h3'
      />
      <div className='grid ~gap-6/10 ~pt-3/8 sm:grid-cols-2 md:grid-cols-3'>
        {OUR_VALUES.map((item) => (
          <div
            key={item.title}
            className='grid ~gap-0.5/1 max-sm:justify-items-center'
          >
            <item.icon className='~w-7/8' />
            <h3 className='~text-lg/xl'>{item.title}</h3>
            <p className='max-w-sm leading-tight text-zinc-400 max-sm:text-center'>
              {item.description}
            </p>
          </div>
        ))}
      </div>
    </SectionWrapper>
  )
}

export default OurValues
