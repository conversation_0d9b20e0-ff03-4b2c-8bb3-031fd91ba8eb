import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>W<PERSON>per } from "@/app/shared/components"
import { ChatOutlineIcon, EmailIcon, PhoneIcon } from "@/Icons"

const NeedAssistance = () => {
  return (
    <SectionWrapper>
      <SectionTitle
        subtitle='Need Assistance?'
        title="If you have any questions about the payment process or need help in completing your transaction, please don't hesitate to contact us:"
      />
      <ReachOut />
    </SectionWrapper>
  )
}

export default NeedAssistance

// TODO: Move in another folder
const ReachOut = () => {
  return (
    <div className='bg-zinc-100 ~px-5/20 ~py-5/8'>
      <h3 className='font-semibold uppercase ~text-sm/base'>Reach us out</h3>
      <div className='mt-4 grid font-inter ~gap-4/8 sm:grid-cols-2 md:grid-cols-3'>
        <div className='flex h-fit text-sm ~gap-2/3'>
          <PhoneIcon className='flex-shrink-0' />
          <div className='grid ~gap-0/1'>
            <span className='text-primary'>Call Us</span>
            <span className='text-zinc-400'>
              TOLL FREE 1800 419 5949 <br />
              LANDLINE : 080-4202-4367/ 2991 5864
            </span>
          </div>
        </div>
        <div className='flex h-fit text-sm ~gap-2/3'>
          <EmailIcon className='flex-shrink-0' />
          <div className='grid ~gap-0/1'>
            <span className='text-primary'>Email Us</span>
            <span className='text-zinc-400'><EMAIL></span>
          </div>
        </div>
        <div className='flex ~gap-2/3'>
          <ChatOutlineIcon className='flex-shrink-0' />
          <div className='grid h-fit text-sm ~gap-0/1'>
            <span className='text-primary'>Live Chat</span>
            <span className='text-zinc-400'>
              Click the chat icon below to connect <br />
              with our support team.
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}
