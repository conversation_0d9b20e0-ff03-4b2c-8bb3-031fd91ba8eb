"use client"
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  StepProgress,
} from "@/app/shared/components"
import { PAYMENT_INSTRUCTIONS } from "@/config"

const PaymentInstructions = () => {
  return (
    <SectionWrapper className='!px-0'>
      <SplitView
        image='/images/pay-now.webp'
        labelBoldText='Pay'
        labelText='Now'
      >
        <div>
          <h3 className='uppercase ~text-lg/xl ~pb-5/8'>How to apply ?</h3>
          <div className='relative isolate grid ~gap-5/10'>
            {PAYMENT_INSTRUCTIONS.map((step) => (
              <StepProgress key={step.number} {...step} />
            ))}

            {/* steps bottom line */}
            <span className='absolute bottom-[10%] top-[10%] -z-10 border-l-[1px] border-dashed border-gray-500/20 ~left-5/6 sm:border-l-2'></span>
          </div>
        </div>
      </SplitView>
    </SectionWrapper>
  )
}

export default PaymentInstructions
