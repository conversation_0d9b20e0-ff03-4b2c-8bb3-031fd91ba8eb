import { SectionTitle, SectionWrapper } from "@/app/shared/components"
import { PAYMENT_OPTIONS } from "@/config"
import Image from "next/image"

const PaymentOptions = () => {
  return (
    <SectionWrapper>
      <SectionTitle
        title='We offer multiple payment methods to cater to your convenience:'
        subtitle='SECURE PAYMENT OPTIONS'
      />
      <div className='grid ~gap-2/4 sm:grid-cols-2 md:grid-cols-3'>
        {PAYMENT_OPTIONS.map((card, index) => (
          <div
            key={index}
            className='grid content-between text-white'
            style={{ backgroundColor: card.color }}
          >
            <div className='~space-y-4/6 ~px-4/6 ~py-6/7'>
              <h2 className='font-medium !leading-tight ~text-lg/2xl'>
                {card.title}
              </h2>
              <p className='font-inter !leading-tight ~text-base/xl'>
                {card.description}
              </p>
            </div>
            <div className='relative aspect-square w-full bg-white/20'>
              <Image
                src={card.image}
                alt={card.title}
                fill
                sizes='(max-width: 768px) 100vw, 33vw'
                draggable={false}
                className='object-cover'
              />
            </div>
          </div>
        ))}
      </div>
    </SectionWrapper>
  )
}

export default PaymentOptions
