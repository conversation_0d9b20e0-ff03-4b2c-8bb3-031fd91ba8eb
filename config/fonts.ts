import { <PERSON><PERSON>st, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "next/font/google"

export const lato = Lato({
  subsets: ["latin"],
  variable: "--font-lato",
  weight: ["400"],
})

export const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
})

export const geist = Geist({
  subsets: ["latin"],
  variable: "--font-geist",
})

export const manrope = Manrope({
  subsets: ["latin"],
  variable: "--font-manrope",
})
