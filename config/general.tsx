import { JSX } from "react"
import {
  FacebookIcon,
  InstagramIcon,
  LinkedInIcon,
  TwitterIcon,
  YouTubeIcon,
} from "../Icons"

// -----------------------------
// Home Page: Hero Slides

export const HERO_SLIDES: JSX.Element[] = [
  <>
    Welcome to <span className='font-bold'>NexMove</span>, your{" "}
    <span className='font-bold'> partner</span> in moving forward!
  </>,
  <>
    We <span className='font-bold'>specialize</span> in seamless, stress-free
    packing, moving, and logistics.
  </>,
  <>
    Focus on what matters—<span className='font-bold'>let us handle</span> the
    details!
  </>,
  <>
    Make your <span className='font-bold'>next move effortless</span> with
    NexMove!
  </>,
]

export type TPackingTips = {
  title: string
  steps: string[]
}

export const PACKING_TIPS: TPackingTips[] = [
  {
    title: "1. plan your packing early",
    steps: [
      "Start packing as soon as your moving date is confirmed to avoid last-minute stress.",
      "Create a room-by-room checklist to ensure nothing gets left behind.",
    ],
  },
  {
    title: "2. declutter before packing",
    steps: [
      "Sort your belongings and decide what to keep, donate, sell, or discard.",
    ],
  },
  {
    title: "3. Pack smartly and securely",
    steps: [
      "Label Everything: Clearly mark each box with its contents and the destination room or department.",
      "Heavy Items in Small Boxes: Avoid overloading large boxes to prevent breakage or injury.",
      "Fragile Items: Wrap each item individually with bubble wrap or paper and fill empty spaces to prevent shifting.",
      "Electronics: Use original boxes if available, and pack cables and accessories together.",

      "Furniture: Disassemble large pieces, keep screws in labeled bags, and wrap parts to prevent damage.",
    ],
  },
  {
    title: "4. Create an essentials box",
    steps: [
      "Pack a box with daily essentials like toiletries, clothes, chargers, snacks, important documents, and basic tools.",
      "For office moves, include backup devices, necessary files, and stationery for immediate use.",
    ],
  },
  {
    title: "5. Use color-coding or numbering",
    steps: [
      "Assign a color or number to each room or department and label boxes accordingly.",
      "This makes unpacking easier and faster at your new location.",
    ],
  },

  {
    title: "6. Keep valuables with you",
    steps: [
      "Pack jewelry, passports, cash, and important documents in a separate bag to keep with you during the move.",
    ],
  },

  {
    title: "7. Take inventory",
    steps: [
      "Create a detailed inventory list to track your belongings and ensure nothing is misplaced during the move.",
      "Share the inventory with your movers for better organization.",
    ],
  },
  {
    title: "8. Let the experts handle it",
    steps: [
      "For complex or fragile items, trust NEX Move's professional packing services.",
      "Our team uses high-quality materials and proven techniques to pack and protect your belongings.",
    ],
  },
]

export type TSocialLink = {
  url: string
  icon: React.ElementType
}

export const SOCIAL_LINKS: TSocialLink[] = [
  {
    url: "https://www.linkedin.com/company/nexmove-hallef/",
    icon: LinkedInIcon,
  },
  {
    url: "https://www.instagram.com/nexmove2024/",
    icon: InstagramIcon,
  },
  {
    url: "https://www.youtube.com/@nex-move",
    icon: YouTubeIcon,
  },
  {
    url: "https://www.facebook.com/nexmove2024/",
    icon: FacebookIcon,
  },
  {
    url: "https://x.com/nex_move",
    icon: TwitterIcon,
  },
]

// ---------------------------
// Home Page: Value Added Services

export type TValueAddedServices = {
  title: string
  img: string
  cardColor: string
}

export const VALUE_ADDED_SERVICES: TValueAddedServices[] = [
  {
    title: "Dismantling & Reassembling",
    img: "/images/dismantling.webp",
    cardColor: "#9568f7",
  },
  {
    title: "Unpacking & Setup",
    img: "/images/unpacking.webp",
    cardColor: "#50a3f8",
  },
  {
    title: "Specialized Handling",
    img: "/images/spealized-handling.webp",
    cardColor: "#ec6249",
  },
]

export type TFooterLinks = {
  label: string
  url: string
}

export const FOOTER_LINKS: TFooterLinks[] = [
  {
    label: "Anti Bribery and Corruption Policy",
    url: "/anti-bribery-and-corruption-policy",
  },
  {
    label: "Corporate Social Responsibility Policy",
    url: "/corporate-social-responsibility-policy",
  },
  { label: "Parent Organization", url: "/parent-organization" },

  {
    label: "Terms and Conditions",
    url: "/terms-and-conditions",
  },
  {
    label: "Shipping and Delivery Policy",
    url: "/shipping-and-delivery-policy",
  },
  {
    label: "Refund and Cancellation Policy",
    url: "/refund-and-cancellation-policy",
  },
  {
    label: "Terms of Use",
    url: "/terms-of-use",
  },
]
