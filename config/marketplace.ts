import {
  BoxesIcon,
  DeliveryBoxIcon,
  SpeedoMeterIcon,
  WorldwideIcon,
} from "@/Icons"

export type TMarketplaceBenefit = {
  title: string
  description: string
  icon: React.ElementType
}

export type TServiceCategory = {
  title: string
  description: string
  image: string
  services: string[]
}

export type THowItWorksStep = {
  step: number
  title: string
  description: string
}

export const MARKETPLACE_BENEFITS: TMarketplaceBenefit[] = [
  {
    title: "Expand Your Reach",
    description:
      "Connect with thousands of customers actively seeking logistics and moving services across the country.",
    icon: WorldwideIcon,
  },
  {
    title: "Streamlined Operations",
    description:
      "Manage bookings, track deliveries, and handle payments through our integrated platform.",
    icon: SpeedoMeterIcon,
  },
  {
    title: "Reliable Support",
    description:
      "Get 24/7 customer support and marketing assistance to help grow your business.",
    icon: DeliveryBoxIcon,
  },
  {
    title: "Flexible Services",
    description:
      "Offer various services from household moving to specialized logistics as per your expertise.",
    icon: BoxesIcon,
  },
]

export const SERVICE_CATEGORIES: TServiceCategory[] = [
  {
    title: "Packing & Moving",
    description:
      "Household relocations, office moves, and specialized packing services",
    image: "/services-image/household-moving.webp",
    services: [
      "Household Moving",
      "Office Relocation",
      "Industrial Moving",
      "International Moving",
    ],
  },
  {
    title: "Transportation & Logistics",
    description: "Freight services, trucking, and last-mile delivery solutions",
    image: "/services-image/trucking-service.webp",
    services: [
      "Freight Transport",
      "Local Delivery",
      "Long Distance Hauling",
      "Express Services",
    ],
  },
  {
    title: "Storage Solutions",
    description: "Warehousing, temporary storage, and inventory management",
    image: "/services-image/storage-service.webp",
    services: [
      "Warehouse Storage",
      "Inventory Management",
      "Climate Control",
      "Document Storage",
    ],
  },
  {
    title: "Specialized Services",
    description:
      "Air ambulance, courier services, and emergency transportation",
    image: "/services-image/ambulance-service.webp",
    services: [
      "Air Ambulance",
      "Medical Transport",
      "Express Courier",
      "Emergency Services",
    ],
  },
]

export const HOW_IT_WORKS_STEPS: THowItWorksStep[] = [
  {
    step: 1,
    title: "Register Your Business",
    description:
      "Complete our simple registration form with your business details, certifications, and service offerings.",
  },
  {
    step: 2,
    title: "Verification Process",
    description:
      "Our team reviews your application, verifies credentials, and ensures quality standards are met.",
  },
  {
    step: 3,
    title: "Start Receiving Orders",
    description:
      "Once approved, start receiving customer requests and grow your business with our platform.",
  },
]
