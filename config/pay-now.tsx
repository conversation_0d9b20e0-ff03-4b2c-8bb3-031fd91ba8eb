import { CalculatorIcon, ChecklistIcon, EmailIcon, PaymentIcon } from "@/Icons"

export type TPaymentInstructions = {
  number: number
  title: string
  color: string
  icon: React.ElementType
}

export const PAYMENT_INSTRUCTIONS: TPaymentInstructions[] = [
  {
    number: 1,
    title: "Review your invoice or quote to confirm the total amount due.",
    color: "#ff5b00",
    icon: CalculatorIcon,
  },
  {
    number: 2,
    title: "Select your preferred payment method from the options provided.",
    color: "#9568f7",
    icon: PaymentIcon,
  },
  {
    number: 3,
    title: "Follow the instructions to complete your transaction.",
    color: "#50a3f8",
    icon: ChecklistIcon,
  },
  {
    number: 4,
    title:
      "Once payment is complete, you will receive a confirmation email or receipt.",
    color: "#439288",
    icon: EmailIcon,
  },
]

// -------------------------
// Payment options

export type TPaymentOptions = {
  title: string
  description: string
  image: string
  color: string
}

export const PAYMENT_OPTIONS: TPaymentOptions[] = [
  {
    title: "CREDIT/DEBIT CARD:",
    description:
      "Make a secure payment online using your Visa, Mastercard, or other major cards.",
    color: "#9568f7",
    image: "/images/credit-card.webp",
  },
  {
    title: "NET BANKING:",
    description:
      "Transfer funds directly from your bank account using our secure net banking option.",
    color: "#50a3f8",
    image: "/images/net-banking.webp",
  },
  {
    title: "WALLETS:",
    description: "Use popular digital wallets for fast and easy transactions.",
    color: "#ec6249",
    image: "/images/wallets.webp",
  },
]
