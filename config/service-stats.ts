export type TServiceStats = {
  value: number
  label: string
  prefix?: string
}

export const SERVICE_STATS: TServiceStats[] = [
  {
    value: 10,
    label: "Years of Reliable Experience",
  },
  {
    value: 45,
    label: "Leading Countries Reached",
  },
  {
    value: 100,
    label: "Corporates Trust",
  },
  {
    value: 100000,
    label: "Household Moves",
  },
  {
    value: 1000,
    label: "Cross Border Moves",
  },
  {
    value: 100,
    label: "Trained & Experienced Crew Members",
  },
  {
    value: 500000,
    label: "Assets Delivered for Work From Home",
    prefix: "+",
  },
  {
    value: 100,
    label: "Pets Relocated",
  },
  {
    value: 1000,
    label: "Cars Moved",
  },
  {
    value: 100000,
    label: "SQFT Storage Space",
  },
]
