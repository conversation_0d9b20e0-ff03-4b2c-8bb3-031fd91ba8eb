import {
  GlobeIcon,
  PriceTagIcon,
  SmilyRatingIcon,
  WebServiceIcon,
  WorldwideIcon,
} from "@/Icons"

export type TWhatWeOffer = {
  title: string
  description: string
  icon: React.ElementType
}

export const WHAT_WE_OFFER: TWhatWeOffer[] = [
  {
    title: "GLOBAL REACH:",
    description:
      "With a network of partners worldwide, we can handle your logistics needs no matter where you are.",
    icon: GlobeIcon,
  },
  {
    title: "EXPERIENCED TEAM:",
    description:
      "Our professionals are skilled, trained, and equipped to manage all aspects of your move or logistics requirements.",
    icon: SmilyRatingIcon,
  },
  {
    title: "TAILORED SOLUTIONS:",
    description:
      "From small personal moves to large-scale corporate relocations, we offer customized services that align with your specific needs.",
    icon: WebServiceIcon,
  },
  {
    title: "AFFORDABLE PRICING:",
    description:
      "Get the best value for your money with transparent, competitive pricing and no hidden fees.",
    icon: PriceTagIcon,
  },
  {
    title: "CUTTING-EDGE TECHNOLOGY:",
    description:
      "Real-time tracking, easy booking, and automated updates ensure efficiency and peace of mind.",
    icon: WorldwideIcon,
  },
]
