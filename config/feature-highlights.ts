import { DeliveryBoxIcon, SpeedoMeterIcon, WorldwideIcon } from "@/Icons"

export type TFeatureHighlight = {
  title: string
  description: string
  icon: React.ElementType
}

export const FEATURE_HIGHLIGHTS: TFeatureHighlight[] = [
  {
    title: "Speed.",
    description:
      "Our competitive edge is moving quicker than the competition. We have the most precise deliveries on the market.",
    icon: SpeedoMeterIcon,
  },
  {
    title: "End To End Services.",
    description:
      "Nexmove excels at offering its clients full end-to-end support for all of its services.",
    icon: DeliveryBoxIcon,
  },
  {
    title: "Tech.",
    description:
      "With our proprietary SmartLogistics Platform we drive tech forward to outperform ourselves and our customers expectations.",
    icon: WorldwideIcon,
  },
]
