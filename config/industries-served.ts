export type TIndustriesServed = {
  title: string
  image: string
  cardColor: string
}

export const INDUSTRIES_SERVED: TIndustriesServed[] = [
  {
    title: "Residential Relocation",
    image: "/images/industries-served/residental-relocation.webp",
    cardColor: "#9568f7",
  },
  {
    title: "Commercial and Office Relocation",
    image: "/images/industries-served/commercial-and-office-relocation.webp",
    cardColor: "#50a3f8",
  },
  {
    title: "Industrial and Manufacturing",
    image: "/images/industries-served/industrial-and-manufacturing.webp",
    cardColor: "#ec6249",
  },
  {
    title: "Healthcare and Medical",
    image: "/images/industries-served/healthcare-and-medical.webp",
    cardColor: "#9568f7",
  },
  {
    title: "Automotive",
    image: "/images/industries-served/automotive.webp",
    cardColor: "#50a3f8",
  },
  {
    title: "Retail and E-commerce",
    image: "/images/industries-served/retail-and-e-commerce.webp",
    cardColor: "#ec6249",
  },
  {
    title: "Technology and IT",
    image: "/images/industries-served/technology-and-it.webp",
    cardColor: "#9568f7",
  },
  {
    title: "Pet Relocation",
    image: "/images/industries-served/pet-relocation.webp",
    cardColor: "#50a3f8",
  },
  {
    title: "Education and Libraries",
    image: "/images/industries-served/education-and-libraries.webp",
    cardColor: "#ec6249",
  },
  {
    title: "Hospitality",
    image: "/images/industries-served/hospitality.webp",
    cardColor: "#9568f7",
  },
  {
    title: "Government and Public Sector",
    image: "/images/industries-served/government-and-public-sector.webp",
    cardColor: "#50a3f8",
  },
]
