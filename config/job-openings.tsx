import {
  ArcheryIcon,
  CalendarIcon,
  ChecklistIcon,
  CustomerSupportIcon,
  DeliveryVehicleIcon,
  DollarIcon,
  LightBulbIcon,
  PersonIcon,
  TeamIcon,
} from "@/Icons"

export type Jobs = {
  id: number
  title: string
  icon: React.ElementType
  location: string[]
  type: string
  description: string
  requirements: string[]
}

export const JOBS: Jobs[] = [
  {
    id: 1,
    title: "Logistics Coordinator",
    icon: PersonIcon,
    location: [
      "Bangalore",
      "Chennai",
      "Delhi NCR",
      "Hyderabad",
      "Kolkata",
      "Mumbai",
      "Pune",
    ],
    type: "Full-Time",
    description:
      "Coordinate and oversee the logistics of relocations and shipments. Ensure timely and efficient operations while maintaining high customer satisfaction.",
    requirements: [
      "Bachelor's degree in Logistics, Business, or related field",
      "Strong organizational and problem-solving skills",
      "Excellent communication skills",
    ],
  },
  {
    id: 2,
    title: "Customer Service Representative",
    icon: CustomerSupportIcon,
    location: [
      "Bangalore",
      "Chennai",
      "Delhi NCR",
      "Hyderabad",
      "Kolkata",
      "Mumbai",
      "Pune",
    ],
    type: "Full-Time/Part-Time",
    description:
      "Provide support to customers via phone, email, and chat. Handle inquiries, resolve issues, and ensure a positive experience.",
    requirements: [
      "Intermediate, diploma or equivalent",
      "Exceptional interpersonal skills",
      "Ability to multitask and work in a fast-paced environment",
    ],
  },
  {
    id: 3,
    title: "Truck Driver",
    icon: DeliveryVehicleIcon,
    location: [
      "Bangalore",
      "Chennai",
      "Delhi NCR",
      "Hyderabad",
      "Kolkata",
      "Mumbai",
      "Pune",
    ],
    type: "Full-Time",
    description:
      "Safely transport goods to various locations. Ensure compliance with road and safety regulations.",
    requirements: [
      "Valid driver's license with a clean driving record",
      "Experience in long-haul or local trucking",
      "Good physical stamina",
    ],
  },
  {
    id: 4,
    title: "Warehouse Associate",
    icon: PersonIcon,
    location: [
      "Bangalore",
      "Chennai",
      "Delhi NCR",
      "Hyderabad",
      "Kolkata",
      "Mumbai",
      "Pune",
    ],
    type: "Full-Time/Part-Time",
    description:
      "Manage inventory, organize storage spaces, and ensure the timely movement of goods in and out of the warehouse.",
    requirements: [
      "Ability to manage heavy equipment's",
      "Attention to detail",
      "Previous warehouse experience is a plus",
    ],
  },
]

// ======================

export type TLocationBenefit2 = {
  label: string
  icon: React.ElementType
}

export const LOCATION_BENEFITS2: TLocationBenefit2[] = [
  {
    label: "Growth Opportunities",
    icon: LightBulbIcon,
  },
  {
    label: "Collaborative Environment",
    icon: TeamIcon,
  },
  {
    label: "Competitive Benefits",
    icon: DollarIcon,
  },
  {
    label: "Impactful Work",
    icon: ArcheryIcon,
  },
]

// ========================

export type THowToApply = {
  number: number
  title: string
  description: string
  color: string
  icon: React.ElementType
}

export const JOB_APPLY_PROCESS: THowToApply[] = [
  {
    number: 1,
    title: "Browse:",
    description: "Browse the job openings listed above.",
    color: "#ff5b00",
    icon: LightBulbIcon,
  },
  {
    number: 2,
    title: "Prepare:",
    description:
      "Prepare your updated resume and a cover letter tailored to the role.",
    color: "#9568f7",
    icon: CalendarIcon,
  },
  {
    number: 3,
    title: "Apply:",
    description:
      "Send your <NAME_EMAIL> or apply directly through our online portal.",
    color: "#50a3f8",
    icon: ChecklistIcon,
  },
]
