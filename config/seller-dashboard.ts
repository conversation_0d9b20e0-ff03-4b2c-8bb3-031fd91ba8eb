export type TProduct = {
  id: string
  name: string
  category: string
  subcategory: string
  description: string
  price: number
  images: string[]
  status: "active" | "inactive" | "draft"
  stock: number
  sku: string
  createdAt: string
  updatedAt: string
  views: number
  sales: number
}

// Marketplace Product Type
export type TMarketplaceProduct = {
  id: string
  title: string
  description: string
  expectingPrice: number
  pictures: string[]
  category: "electronics-appliances" | "furniture" | "vehicle" | "others"
}

export type TOrder = {
  id: string
  productId: string
  productName: string
  customerName: string
  customerEmail: string
  customerPhone: string
  quantity: number
  totalAmount: number
  status: "pending" | "confirmed" | "in-progress" | "completed" | "cancelled"
  orderDate: string
  deliveryDate?: string
  address: {
    street: string
    city: string
    state: string
    pincode: string
  }
  notes?: string
}

export type TSellerStats = {
  totalProducts: number
  activeProducts: number
  totalOrders: number
  monthlyRevenue: number
  pendingOrders: number
  completedOrders: number
  averageRating: number
  totalViews: number
}

export type TProductCategory = {
  id: string
  name: string
  subcategories: string[]
}

// Marketplace Product Category Type
export type TMarketplaceCategory = {
  id: "electronics-appliances" | "furniture" | "vehicle" | "others"
  name: string
  minimumImages: number
}

// Mock data
export const MOCK_SELLER_STATS: TSellerStats = {
  totalProducts: 24,
  activeProducts: 18,
  totalOrders: 156,
  monthlyRevenue: 125000,
  pendingOrders: 8,
  completedOrders: 142,
  averageRating: 4.6,
  totalViews: 2847,
}

export const MOCK_PRODUCT_CATEGORIES: TProductCategory[] = [
  {
    id: "packing-moving",
    name: "Packing & Moving",
    subcategories: [
      "Household Moving",
      "Office Relocation",
      "Industrial Moving",
      "International Moving",
      "Packing Materials",
      "Storage Solutions",
    ],
  },
  {
    id: "transportation",
    name: "Transportation & Logistics",
    subcategories: [
      "Freight Transport",
      "Local Delivery",
      "Long Distance Hauling",
      "Express Services",
      "Vehicle Transport",
      "Bulk Transport",
    ],
  },
  {
    id: "storage",
    name: "Storage Solutions",
    subcategories: [
      "Warehouse Storage",
      "Inventory Management",
      "Climate Control",
      "Document Storage",
      "Self Storage",
      "Cold Storage",
    ],
  },
  {
    id: "specialized",
    name: "Specialized Services",
    subcategories: [
      "Air Ambulance",
      "Medical Transport",
      "Express Courier",
      "Emergency Services",
      "Pet Relocation",
      "Art & Antique Moving",
    ],
  },
]

export const MOCK_PRODUCTS: TProduct[] = [
  {
    id: "1",
    name: "Complete Household Moving Service",
    category: "Packing & Moving",
    subcategory: "Household Moving",
    description:
      "Professional household moving service with packing, loading, transportation, and unpacking. Includes insurance coverage up to ₹1 lakh.",
    price: 15000,
    images: [
      "/services-image/household-moving.webp",
      "/services-image/packing-service.webp",
    ],
    status: "active",
    stock: 10,
    sku: "HM-001",
    createdAt: "2025-06-20T10:00:00Z",
    updatedAt: "2025-06-25T15:30:00Z",
    views: 245,
    sales: 12,
  },
  {
    id: "2",
    name: "Express Courier Service",
    category: "Specialized Services",
    subcategory: "Express Courier",
    description:
      "Same-day and next-day courier service for documents and small packages. Real-time tracking included.",
    price: 500,
    images: ["/services-image/courier-service.webp"],
    status: "active",
    stock: 50,
    sku: "EC-002",
    createdAt: "2025-06-18T14:20:00Z",
    updatedAt: "2025-06-24T09:15:00Z",
    views: 189,
    sales: 28,
  },
  {
    id: "3",
    name: "Climate Controlled Storage",
    category: "Storage Solutions",
    subcategory: "Climate Control",
    description:
      "Temperature and humidity controlled storage facility for sensitive items. 24/7 security monitoring.",
    price: 2000,
    images: ["/services-image/storage-service.webp"],
    status: "active",
    stock: 25,
    sku: "CS-003",
    createdAt: "2025-06-15T11:45:00Z",
    updatedAt: "2025-06-22T16:20:00Z",
    views: 156,
    sales: 8,
  },
  {
    id: "4",
    name: "Office Relocation Package",
    category: "Packing & Moving",
    subcategory: "Office Relocation",
    description:
      "Complete office moving solution with minimal downtime. Includes IT equipment handling and setup.",
    price: 45000,
    images: ["/services-image/office-moving.webp"],
    status: "draft",
    stock: 5,
    sku: "OR-004",
    createdAt: "2025-06-22T08:30:00Z",
    updatedAt: "2025-06-25T12:45:00Z",
    views: 67,
    sales: 2,
  },
  {
    id: "5",
    name: "Long Distance Trucking",
    category: "Transportation & Logistics",
    subcategory: "Long Distance Hauling",
    description:
      "Heavy-duty trucking service for long distance transportation. GPS tracking and insurance included.",
    price: 25000,
    images: ["/services-image/trucking-service.webp"],
    status: "active",
    stock: 8,
    sku: "LT-005",
    createdAt: "2025-06-19T13:15:00Z",
    updatedAt: "2025-06-23T10:30:00Z",
    views: 198,
    sales: 15,
  },
]

export const MOCK_ORDERS: TOrder[] = [
  {
    id: "ORD-001",
    productId: "1",
    productName: "Complete Household Moving Service",
    customerName: "Rahul Sharma",
    customerEmail: "<EMAIL>",
    customerPhone: "+91 9876543210",
    quantity: 1,
    totalAmount: 15000,
    status: "confirmed",
    orderDate: "2025-06-25T09:30:00Z",
    deliveryDate: "2025-06-28T10:00:00Z",
    address: {
      street: "123 MG Road",
      city: "Bangalore",
      state: "Karnataka",
      pincode: "560001",
    },
    notes: "Please handle electronics carefully",
  },
  {
    id: "ORD-002",
    productId: "2",
    productName: "Express Courier Service",
    customerName: "Priya Patel",
    customerEmail: "<EMAIL>",
    customerPhone: "+91 9876543211",
    quantity: 3,
    totalAmount: 1500,
    status: "in-progress",
    orderDate: "2025-06-24T14:15:00Z",
    address: {
      street: "456 Park Street",
      city: "Mumbai",
      state: "Maharashtra",
      pincode: "400001",
    },
  },
  {
    id: "ORD-003",
    productId: "5",
    productName: "Long Distance Trucking",
    customerName: "Amit Kumar",
    customerEmail: "<EMAIL>",
    customerPhone: "+91 9876543212",
    quantity: 1,
    totalAmount: 25000,
    status: "pending",
    orderDate: "2025-06-25T16:20:00Z",
    deliveryDate: "2025-06-30T08:00:00Z",
    address: {
      street: "789 Industrial Area",
      city: "Delhi",
      state: "Delhi",
      pincode: "110001",
    },
    notes: "Fragile machinery - handle with care",
  },
  {
    id: "ORD-004",
    productId: "3",
    productName: "Climate Controlled Storage",
    customerName: "Sunita Reddy",
    customerEmail: "<EMAIL>",
    customerPhone: "+91 9876543213",
    quantity: 2,
    totalAmount: 4000,
    status: "completed",
    orderDate: "2025-06-20T11:45:00Z",
    deliveryDate: "2025-06-22T14:00:00Z",
    address: {
      street: "321 Tech Park",
      city: "Hyderabad",
      state: "Telangana",
      pincode: "500001",
    },
  },
]

// Marketplace Categories Configuration
export const MARKETPLACE_CATEGORIES: TMarketplaceCategory[] = [
  {
    id: "electronics-appliances",
    name: "Electronics & Appliances",
    minimumImages: 6,
  },
  {
    id: "furniture",
    name: "Furniture",
    minimumImages: 4,
  },
  {
    id: "vehicle",
    name: "Vehicle",
    minimumImages: 6,
  },
  {
    id: "others",
    name: "Others",
    minimumImages: 4,
  },
]

// Mock Marketplace Products
export const MOCK_MARKETPLACE_PRODUCTS: TMarketplaceProduct[] = [
  {
    id: "mp-1",
    title: "Samsung 55-inch 4K Smart TV",
    description:
      "Excellent condition Samsung 55-inch 4K Smart TV. Barely used, original box and accessories included. Moving out sale.",
    expectingPrice: 45000,
    pictures: [
      "/marketplace/tv-1.jpg",
      "/marketplace/tv-2.jpg",
      "/marketplace/tv-3.jpg",
      "/marketplace/tv-4.jpg",
      "/marketplace/tv-5.jpg",
      "/marketplace/tv-6.jpg",
    ],
    category: "electronics-appliances",
  },
  {
    id: "mp-2",
    title: "Wooden Dining Table Set (6 Seater)",
    description:
      "Beautiful teak wood dining table with 6 chairs. Well maintained, perfect for family dining. Selling due to relocation.",
    expectingPrice: 25000,
    pictures: [
      "/marketplace/dining-1.jpg",
      "/marketplace/dining-2.jpg",
      "/marketplace/dining-3.jpg",
      "/marketplace/dining-4.jpg",
    ],
    category: "furniture",
  },
  {
    id: "mp-3",
    title: "Honda City 2019 Model",
    description:
      "Well maintained Honda City 2019 model. Single owner, all papers clear. Excellent mileage and performance.",
    expectingPrice: 850000,
    pictures: [
      "/marketplace/car-1.jpg",
      "/marketplace/car-2.jpg",
      "/marketplace/car-3.jpg",
      "/marketplace/car-4.jpg",
      "/marketplace/car-5.jpg",
      "/marketplace/car-6.jpg",
    ],
    category: "vehicle",
  },
]
