import {
  ArcheryIcon,
  LightBulbIcon,
  SmilyRatingIcon,
  SustainabilityIcon,
  TeamIcon,
} from "@/Icons"

export type TOurValues = {
  title: string
  description: string
  icon: React.ElementType
}

export const OUR_VALUES: TOurValues[] = [
  {
    title: "CUSTOMER FOCUS:",
    description:
      "We prioritize the needs of our customers, striving to exceed their expectations in every interaction.",
    icon: ArcheryIcon,
  },
  {
    title: "INTEGRITY:",
    description:
      "Honesty and transparency guide everything we do, building trust with our clients and partners.",
    icon: ArcheryIcon,
  },
  {
    title: "EXCELLENCE:",
    description:
      "We are committed to delivering the highest standards of service and continuously improving our processes.",
    icon: SmilyRatingIcon,
  },
  {
    title: "INNOVATION:",
    description:
      "Embracing creativity and technology to offer cutting-edge solutions tailored to our customers' needs.",
    icon: LightBulbIcon,
  },
  {
    title: "SUSTAINABILITY:",
    description:
      "We take responsibility for our environmental impact and promote eco-friendly practices in our operations.",
    icon: SustainabilityIcon,
  },
  {
    title: "TEAMWORK:",
    description:
      "Collaboration and respect are at the core of our company culture, ensuring a positive and productive environment.",
    icon: TeamIcon,
  },
]
