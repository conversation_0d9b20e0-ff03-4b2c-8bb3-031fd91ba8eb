import {
  CustomerSupportIcon,
  ShareSessionIcon,
  CalculatorIcon,
  SmilyRatingIcon,
  DeliveryBoxIcon,
  LightBulbIcon,
  TeamIcon,
} from "@/Icons"

export type TSupportOption = {
  number: number
  title: string
  description: string
  color: string
  icon: React.ElementType
}

export const SUPPORT_OPTIONS: TSupportOption[] = [
  {
    number: 1,
    title: "Request a Quote:",
    description:
      "Get an estimate for any of our services, from packing and moving to transportation and storage.",
    color: "#ff5b00",
    icon: CalculatorIcon,
  },
  {
    number: 2,
    title: "Customer Support:",
    description:
      "Reach out for support or any questions you may have about our services.",
    color: "#9568f7",
    icon: CustomerSupportIcon,
  },
  {
    number: 3,
    title: "Consultation: ",
    description:
      "Need advice on your logistics requirements? We'll provide a personalized solution tailored to your needs.",
    color: "#50a3f8",
    icon: ShareSessionIcon,
  },
]

// Location Benefits

export type TLocationBenefit = {
  label: string
  icon: React.ElementType
}

export const LOCATION_BENEFITS: TLocationBenefit[] = [
  {
    label: "Pan-India Coverage",
    icon: LightBulbIcon,
  },
  {
    label: "Expert Knowledge",
    icon: TeamIcon,
  },
  {
    label: "Seamless Service",
    icon: DeliveryBoxIcon,
  },
  {
    label: "Customer-Focused Approach",
    icon: SmilyRatingIcon,
  },
]
