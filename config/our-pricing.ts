import {
  AmbulanceIcon,
  BoxesIcon,
  CarIcon,
  CoffinIcon,
  GlobeIcon,
  HomeIcon,
  OfficeIcon,
  PlaneIcon,
  RabbitIcon,
  SafeDeliveryIcon,
  WarehouseIcon,
} from "@/Icons"

export type TOurPricing = {
  name: string
  icon: React.ElementType
  pricing: {
    amount: number
    unit?: string
    currency: string
  }
  details: {
    note?: string
    additionalInfo: string[]
  }
  href: string
}

export const OUR_PRICING: TOurPricing[] = [
  {
    name: "HOUSE RELOCATION/SHIFTING",
    icon: HomeIcon,
    pricing: {
      amount: 5000,
      currency: "₹",
    },
    details: {
      note: "For Intercity",
      additionalInfo: ["Pricing varies based on distance and volume of items"],
    },
    href: "packing-and-moving",
  },
  {
    name: "WORKSPACE OR OFFICE RELOCATIONS",
    icon: OfficeIcon,
    pricing: {
      amount: 7500,
      currency: "₹",
    },
    details: {
      additionalInfo: [
        "Customized quotes based on the size and scope of the move",
      ],
    },
    href: "packing-and-moving",
  },
  {
    name: "PET RELOCATION",
    icon: RabbitIcon,
    pricing: {
      amount: 3000,
      unit: "day",
      currency: "₹",
    },
    details: {
      note: "For Intercity",
      additionalInfo: [
        "Customized quotes based on location, country and clearance process",
      ],
    },
    href: "packing-and-moving",
  },
  {
    name: "CAR RELOCATION",
    icon: CarIcon,
    pricing: {
      amount: 10000,
      unit: "day",
      currency: "₹",
    },
    details: {
      note: "For up-to 1500 kms",
      additionalInfo: [
        "Pricing varies based on distance and size/segment of vehicle",
      ],
    },
    href: "packing-and-moving",
  },
  {
    name: "TRANSIT WAREHOUSE FACILITIES",
    icon: WarehouseIcon,
    pricing: {
      amount: 75,
      unit: "day",
      currency: "₹",
    },
    details: {
      note: "For 100sqft Storage Units",
      additionalInfo: ["Pricing depends on size and duration"],
    },
    href: "storage",
  },
  {
    name: "NEX TRUCKING SERVICES",
    icon: WarehouseIcon,
    pricing: {
      amount: 2500,
      unit: "8 Hrs / 100 kms",
      currency: "₹",
    },
    details: {
      note: "For TATA ACE",
      additionalInfo: [
        "Up to 8000/8 Hrs/100 kms for 20 Feet Vehicles",
        "Specialized hauling rates available upon request for all types of vehicles",
      ],
    },
    href: "trucking",
  },
  {
    name: "NEX PART LOAD SERVICES",
    icon: BoxesIcon,
    pricing: {
      amount: 1500,
      unit: "100 CTF",
      currency: "₹",
    },
    details: {
      note: "Up to 500 kms radius",
      additionalInfo: [
        "Customized quotes based on location, country and clearance process",
      ],
    },
    href: "trucking",
  },
  {
    name: "DOMESTIC COURIER & PARCEL SERVICES - LOCAL DELIVERIES",
    icon: SafeDeliveryIcon,
    pricing: {
      amount: 35,
      unit: "Kg",
      currency: "₹",
    },
    details: {
      note: "Within 40 kms radius",
      additionalInfo: ["Pricing varies based on distance and volume of items"],
    },
    href: "courier-and-parcel",
  },
  {
    name: "DOMESTIC COURIER & PARCEL SERVICES - SURFACE MODE",
    icon: SafeDeliveryIcon,
    pricing: {
      amount: 65,
      unit: "Kg",
      currency: "₹",
    },
    details: {
      note: "For surface mode",
      additionalInfo: ["Pricing varies based on distance and volume of items"],
    },
    href: "courier-and-parcel",
  },
  {
    name: "DOMESTIC COURIER & PARCEL SERVICES - AIR MODE",
    icon: PlaneIcon,
    pricing: {
      amount: 150,
      unit: "Kg",
      currency: "₹",
    },
    details: {
      note: "For air mode",
      additionalInfo: ["Pricing varies based on distance and volume of items"],
    },
    href: "courier-and-parcel",
  },
  {
    name: "INTERNATIONAL COURIER & PARCEL SERVICES",
    icon: GlobeIcon,
    pricing: {
      amount: 6000,
      unit: "Kg",
      currency: "₹",
    },
    details: {
      note: "For International deliveries",
      additionalInfo: [
        "International delivery rates vary depending on destination",
        "Additional Clearance & other charges applicable",
      ],
    },
    href: "courier-and-parcel",
  },
  {
    name: "EMERGENCY PATIENT TRANSFER SERVICES",
    icon: AmbulanceIcon,
    pricing: {
      amount: 3500,
      unit: "100 kms",
      currency: "₹",
    },
    details: {
      note: "For compact ambulance vehicle",
      additionalInfo: ["Pricing varies based on distance and volume of items"],
    },
    href: "air-ambulance",
  },
  {
    name: "EMERGENCY PATIENT TRANSFER SERVICES",
    icon: AmbulanceIcon,
    pricing: {
      amount: 5500,
      unit: "100 kms",
      currency: "₹",
    },
    details: {
      note: "For traveller ambulance starting",
      additionalInfo: ["Pricing varies based on distance and volume of items"],
    },
    href: "air-ambulance",
  },
  {
    name: "HUMAN REMAINS TRANSFER SERVICES",
    icon: CoffinIcon,
    pricing: {
      amount: 20000,
      // unit: "100 ctf",
      currency: "₹",
    },
    details: {
      note: "Specialised air cargo for human remains",
      additionalInfo: ["Customised rates as per need available upon request"],
    },
    href: "air-ambulance",
  },
]
