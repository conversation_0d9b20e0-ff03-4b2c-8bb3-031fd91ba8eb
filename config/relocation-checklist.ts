import {
  BoxesIcon,
  MagnifyingGlassIcon,
  StarVerifyIcon,
  TaskIcon,
} from "@/Icons"

export type TRelocationChecklist = {
  title: string
  subtitle: string
  heading: string
  timing?: string
  icon: React.ElementType
  task: {
    title: string
    description: string
  }[]
}

export const RELOCATION_CHECKLIST: TRelocationChecklist[] = [
  {
    title: "step 1:",
    subtitle: "before the move",
    heading: "plan",
    timing: "2-3 weeks before moving",
    icon: TaskIcon,
    task: [
      {
        title: "1. schedule your move",
        description:
          "Book your move with NEX Packers & Movers early to secure your preferred date.",
      },
      {
        title: "2. declutter:",
        description:
          "Sort your items—decide what to keep, donate, sell, or discard.",
      },
      {
        title: "3. inventory management:",
        description:
          "Create a detailed list of your belongings for tracking and insurance purposes.",
      },
      {
        title: "office moves:",
        description:
          "Backup important data, secure sensitive documents, and label equipment by department.",
      },
    ],
  },
  {
    title: "step 2:",
    subtitle: "before the move",
    heading: "prepare",
    timing: "2-3 days before moving",
    icon: BoxesIcon,

    task: [
      {
        title: "1. prepare furniture:",
        description:
          "Disassemble large furniture if needed, or let NEX handle it.",
      },
      {
        title: "2. utility coordination:",
        description:
          "Arrange disconnection and reconnection of utilities (electricity, internet, water).",
      },
      {
        title: "3. essential box:",
        description:
          "Pack a 'First Day' box with toiletries, chargers, snacks, and important documents.",
      },
    ],
  },
  {
    title: "step 3:",
    subtitle: "moving day",
    heading: "supervise",
    timing: "1 day",
    icon: MagnifyingGlassIcon,
    task: [
      {
        title: "1. supervise the process:",
        description: "Be available to guide movers and answer questions.",
      },
      {
        title: "2. wrap up packing:",
        description: "Finalize packing, leaving only essentials for daily use.",
      },
      {
        title: "3. final checks:",
        description: "Inspect your old space to ensure nothing is left behind.",
      },
      {
        title: "4. office moves:",
        description:
          "Ensure equipment and files are handled with care during loading and unloading.",
      },
    ],
  },
  {
    title: "step 4:",
    subtitle: "after move",
    heading: "Inspect",
    icon: StarVerifyIcon,
    task: [
      {
        title: "1. INSPECT DELIVERED ITEMS:",
        description:
          "Check all boxes and furniture for damage and report any issues immediately.",
      },
      {
        title: "2. UNPACK ESSENTIALS:",
        description:
          "Settle into your new space by unpacking priority items first.",
      },
      {
        title: "3.SET UP UTILITIES:",
        description:
          "Ensure all utilities and internet services are operational.",
      },
    ],
  },
]
