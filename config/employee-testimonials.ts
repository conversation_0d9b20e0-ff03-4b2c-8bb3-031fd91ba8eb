export type TEmployeeTestimonials = {
  id: number
  name: string
  designation: string
  service: string
  testimonial: string
  avatar: string
}

export const EMPLOYEE_TESTIMONIALS: TEmployeeTestimonials[] = [
  {
    id: 1,
    name: "<PERSON><PERSON>",
    designation: "Logistics Coordinator",
    service: "A Culture of Growth and Support",
    testimonial:
      "Working at Nex Move has been an incredible journey. The company's commitment to professional growth and employee support has helped me advance my career while feeling valued every step of the way. The collaborative environment makes even the busiest days enjoyable",
    avatar: "/images/avatar.webp",
  },
  {
    id: 2,
    name: "<PERSON> Pani",
    designation: "Customer Service Representative",
    service: "Making a Difference Every Day",
    testimonial:
      "Helping customers during their relocation process is so rewarding. Nex Move provides all the tools and training we need to succeed, and the team's dedication to customer satisfaction inspires me every day.",
    avatar: "/images/avatar.webp",
  },
  {
    id: 3,
    name: "<PERSON> T",
    designation: "Truck Driver",
    service: "Teamwork at Its Best",
    testimonial:
      "The team at Nex Move truly feels like a family. From the support I get on the road to the relationships I've built with my colleagues; this is the best company I've worked for in my career.",
    avatar: "/images/avatar.webp",
  },
  {
    id: 4,
    name: "<PERSON><PERSON> <PERSON>",
    designation: "Warehouse Associate",
    service: "Innovation and Excellence",
    testimonial:
      "What sets Nex Move apart is its focus on innovation and efficiency. Every day, I feel motivated to bring my best because I know my contributions matter. Plus, the management genuinely listens to employee feedback.",
    avatar: "/images/avatar.webp",
  },
  {
    id: 5,
    name: "Prashanth U",
    designation: "Operations Manager",
    service: "Opportunities to Thrive",
    testimonial:
      "Nex Move has given me the platform to grow both personally and professionally. The leadership encourages creative solutions, and the emphasis on teamwork ensures that we're always learning from each other.",
    avatar: "/images/avatar.webp",
  },
]
