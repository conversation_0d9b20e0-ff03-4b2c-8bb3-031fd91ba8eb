export type TGalleryEntry = {
  description: string
  image: string
}

export type TGalleryCategories = {
  option_1: TGalleryEntry[]
  option_2: TGalleryEntry[]
  option_3: TGalleryEntry[]
  option_4: TGalleryEntry[]
  option_5: TGalleryEntry[]
}

export const media = [
  {
    type: "image",
    src: "/images/gallery/customers/C1.webp",
    alt: "Happy Customer 1",
  },
  {
    type: "image",
    src: "/images/gallery/customers/C2.webp",
    alt: "Customer Experience",
  },
  {
    type: "image",
    src: "/images/gallery/customers/C3.webp",
    alt: "Customer Experience",
  },
  {
    type: "image",
    src: "/images/gallery/customers/C4.webp",
    alt: "Customer Event",
  },
  {
    type: "image",
    src: "/images/gallery/customers/C5.webp",
    alt: "Service Feedback",
  },
  {
    type: "image",
    src: "/images/gallery/customers/C6.webp",
    alt: "Smiling Customer",
  },
  {
    type: "image",
    src: "/images/gallery/customers/C7.webp",
    alt: "Smiling Customer",
  },
  {
    type: "image",
    src: "/images/gallery/customers/C8.webp",
    alt: "Smiling Customer",
  },
  {
    type: "image",
    src: "/images/gallery/customers/C3.webp",
    alt: "Smiling Customer",
  },
  {
    type: "image",
    src: "/images/gallery/customers/C4.webp",
    alt: "Smiling Customer",
  },
  {
    type: "image",
    src: "/images/gallery/customers/C2.webp",
    alt: "Smiling Customer",
  },
  {
    type: "image",
    src: "/images/gallery/customers/C6.webp",
    alt: "Smiling Customer",
  },
]

export const photos = [
  { src: "/images/gallery/T1.webp", alt: "Team Photo" },
  {
    src: "/images/gallery/T2.webp",
    alt: "Office Space",
  },
  {
    src: "/images/gallery/T3.webp",
    alt: "Workspace",
  },
  {
    src: "/images/gallery/T4.webp",
    alt: "Meeting Room",
  },
  {
    src: "/images/gallery/T5.webp",
    alt: "Company Event",
  },
  {
    src: "/images/gallery/T6.webp",
    alt: "Employees",
  },
]

export const imageGroups: string[] = [
  "/images/gallery/others/O1.webp",
  "/images/gallery/others/O2.webp",
  "/images/gallery/others/O3.webp",
  "/images/gallery/others/O4.webp",
  "/images/gallery/others/O5.webp",
  "/images/gallery/others/O6.webp",
  "/images/gallery/others/O7.webp",
  "/images/gallery/others/O8.webp",
  "/images/gallery/others/O9.webp",
  "/images/gallery/others/O10.webp",
  "/images/gallery/others/O11.webp",
  "/images/gallery/others/O12.webp",
]

export const GALLERY_DATA: TGalleryCategories = {
  option_1: [
    {
      description: "option 1 Gallery Image 1",
      image: "/images/gallery/fleet/F1.webp",
    },
    {
      description: "option 1 Gallery Image 2",
      image: "/images/gallery/fleet/F2.webp",
    },
    {
      description: "option 1 Gallery Image 1",
      image: "/images/gallery/fleet/F3.webp",
    },
    {
      description: "option 1 Gallery Image 2",
      image: "/images/gallery/fleet/F4.webp",
    },
    {
      description: "option 1 Gallery Image 2",
      image: "/images/gallery/fleet/F5.webp",
    },
    {
      description: "option 1 Gallery Image 2",
      image: "/images/gallery/fleet/F6.webp",
    },
  ],
  option_2: [
    {
      description: "option 2 Gallery Image 1",
      image: "/images/cargo.webp",
    },
    {
      description: "option 2 Gallery Image 2",
      image: "/images/cargo.webp",
    },
    {
      description: "option 2 Gallery Image 3",
      image: "/images/cargo.webp",
    },
  ],
  option_3: [
    {
      description: "option 3 Gallery Image 1",
      image: "/images/cargo.webp",
    },
    {
      description: "option 3 Gallery Image 2",
      image: "/images/cargo.webp",
    },
  ],
  option_4: [
    {
      description: "option 4 Gallery Image 1",
      image: "/images/cargo.webp",
    },
    {
      description: "option 4 Gallery Image 2",
      image: "/images/cargo.webp",
    },
  ],
  option_5: [
    {
      description: "option 5 Gallery Image 1",
      image: "/images/cargo.webp",
    },
    {
      description: "option 5 Gallery Image 2",
      image: "/images/cargo.webp",
    },
  ],
}
