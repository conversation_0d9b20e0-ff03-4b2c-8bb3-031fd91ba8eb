import { uploadFile } from "@/app/shared/services/file-upload"
import { useCallback, useState } from "react"

type CategoryType =
  | "electronics-appliances"
  | "furniture"
  | "vehicle"
  | "others"

export type UploadStatus = "idle" | "uploading" | "success" | "error"

export interface UseMarketplacePictureUploadProps {
  category: CategoryType
  initialPictures?: string[]
  onUploadComplete?: (urls: string[]) => void
  onUploadError?: (error: string) => void
}

export interface UseMarketplacePictureUploadReturn {
  pictures: string[]
  uploadStatus: UploadStatus
  uploadProgress: number
  isUploading: boolean
  minimumImages: number
  maxImages: number
  canSubmit: boolean
  canAddMore: boolean
  uploadPictures: (files: File[]) => Promise<void>
  removePicture: (index: number) => void
  clearPictures: () => void
  getValidationMessage: () => string | null
}

// Define minimum and maximum picture requirements based on category
const getCategoryPictureRequirements = (category: CategoryType) => {
  switch (category) {
    case "electronics-appliances":
    case "vehicle":
      return { min: 6, max: 10 }
    case "furniture":
    case "others":
    default:
      return { min: 4, max: 8 }
  }
}

const getCategoryDisplayName = (category: CategoryType): string => {
  switch (category) {
    case "electronics-appliances":
      return "Electronics & Appliances"
    case "furniture":
      return "Furniture"
    case "vehicle":
      return "Vehicle"
    case "others":
      return "Others"
    default:
      return "Unknown"
  }
}

export const useMarketplacePictureUpload = ({
  category,
  initialPictures = [],
  onUploadComplete,
  onUploadError,
}: UseMarketplacePictureUploadProps): UseMarketplacePictureUploadReturn => {
  const [pictures, setPictures] = useState<string[]>(initialPictures)
  const [uploadStatus, setUploadStatus] = useState<UploadStatus>("idle")
  const [uploadProgress, setUploadProgress] = useState(0)

  // Get minimum and maximum images required for the selected category
  const { min: minimumImages, max: maxImages } =
    getCategoryPictureRequirements(category)

  const isUploading = uploadStatus === "uploading"
  const canSubmit = pictures.length >= minimumImages
  const canAddMore = pictures.length < maxImages

  const uploadPictures = useCallback(
    async (files: File[]) => {
      if (files.length === 0) return

      // Check if adding these files would exceed the maximum
      if (pictures.length + files.length > maxImages) {
        const errorMessage = `Cannot upload ${files.length} more pictures. Maximum ${maxImages} pictures allowed for ${getCategoryDisplayName(category)}.`
        onUploadError?.(errorMessage)
        return
      }

      setUploadStatus("uploading")
      setUploadProgress(0)

      try {
        const totalFiles = files.length
        const uploadedUrls: string[] = []

        for (let i = 0; i < totalFiles; i++) {
          const file = files[i]

          // Validate file type
          if (!file.type.startsWith("image/")) {
            throw new Error(`File ${file.name} is not a valid image`)
          }

          // Validate file size (max 5MB)
          if (file.size > 5 * 1024 * 1024) {
            throw new Error(
              `File ${file.name} is too large. Maximum size is 5MB`,
            )
          }

          try {
            const url = await uploadFile(file, "marketplace")
            uploadedUrls.push(url)

            // Update progress
            setUploadProgress(((i + 1) / totalFiles) * 100)
          } catch (error) {
            console.error(`Error uploading file ${file.name}:`, error)
            throw new Error(`Failed to upload ${file.name}`)
          }
        }

        // Update pictures state
        const newPictures = [...pictures, ...uploadedUrls]
        setPictures(newPictures)
        setUploadStatus("success")
        onUploadComplete?.(newPictures)
      } catch (error) {
        setUploadStatus("error")
        const errorMessage =
          error instanceof Error ? error.message : "Upload failed"
        onUploadError?.(errorMessage)
        console.error("Upload error:", error)
      } finally {
        setUploadProgress(0)
      }
    },
    [pictures, maxImages, category, onUploadComplete, onUploadError],
  )

  const removePicture = useCallback((index: number) => {
    setPictures((prev) => prev.filter((_, i) => i !== index))
  }, [])

  const clearPictures = useCallback(() => {
    setPictures([])
    setUploadStatus("idle")
    setUploadProgress(0)
  }, [])

  const getValidationMessage = useCallback((): string | null => {
    if (pictures.length === 0) {
      return `Please upload at least ${minimumImages} pictures for ${getCategoryDisplayName(category)}`
    }

    if (pictures.length < minimumImages) {
      const remaining = minimumImages - pictures.length
      return `Please upload ${remaining} more picture${remaining > 1 ? "s" : ""} (minimum ${minimumImages} required for ${getCategoryDisplayName(category)})`
    }

    return null
  }, [pictures.length, minimumImages, category])

  return {
    pictures,
    uploadStatus,
    uploadProgress,
    isUploading,
    minimumImages,
    maxImages,
    canSubmit,
    canAddMore,
    uploadPictures,
    removePicture,
    clearPictures,
    getValidationMessage,
  }
}

// Helper function to get minimum images for a category
export const getMinimumImagesForCategory = (category: CategoryType): number => {
  return getCategoryPictureRequirements(category).min
}

// Helper function to validate if enough images are uploaded
export const validatePictureCount = (
  pictures: string[],
  category: CategoryType,
): { isValid: boolean; message?: string } => {
  const minimumRequired = getMinimumImagesForCategory(category)
  const categoryName = getCategoryDisplayName(category)

  if (pictures.length < minimumRequired) {
    return {
      isValid: false,
      message: `At least ${minimumRequired} pictures are required for ${categoryName}`,
    }
  }

  return { isValid: true }
}
