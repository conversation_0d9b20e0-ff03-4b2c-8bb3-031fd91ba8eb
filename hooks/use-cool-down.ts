import { useState, useEffect, useCallback } from "react"

export const useCooldown = (duration = 60) => {
  const [cooldown, setCooldown] = useState(0)

  useEffect(() => {
    if (cooldown > 0) {
      const timer = setInterval(() => {
        setCooldown((prev) => (prev > 0 ? prev - 1 : 0))
      }, 1000)
      return () => clearInterval(timer)
    }
  }, [cooldown])

  const startCooldown = useCallback(() => {
    if (cooldown === 0) {
      setCooldown(duration)
    }
  }, [cooldown, duration])

  const resetCooldown = useCallback(() => {
    setCooldown(0)
  }, [])

  return { cooldown, startCooldown, isCooling: cooldown > 0, resetCooldown }
}
