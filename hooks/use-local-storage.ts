import { useState, useEffect } from "react"

export const useLocalStorage = <T>(
  key: string,
  defaultValue: T,
): [T, (value: T | ((val: T) => T)) => void] => {
  // Initialize state from localStorage or default value
  const [value, setValue] = useState<T>(() => {
    try {
      const storedValue = localStorage.getItem(key)
      // Return parsed value if available, otherwise use defaultValue
      return storedValue ? (JSON.parse(storedValue) as T) : defaultValue
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error)
      return defaultValue
    }
  })

  // Sync state with localStorage changes in other tabs/windows
  useEffect(() => {
    const listener = (e: StorageEvent) => {
      if (e.storageArea === localStorage && e.key === key) {
        try {
          // Handle case when storage is cleared
          setValue(e.newValue ? (JSON.parse(e.newValue) as T) : defaultValue)
        } catch (error) {
          console.error(
            `Error parsing localStorage change for key "${key}":`,
            error,
          )
          setValue(defaultValue)
        }
      }
    }

    window.addEventListener("storage", listener)

    return () => {
      window.removeEventListener("storage", listener)
    }
  }, [key, defaultValue])

  // Update both state and localStorage
  const setValueInLocalStorage = (newValue: T | ((val: T) => T)) => {
    setValue((currentValue) => {
      try {
        const result =
          typeof newValue === "function"
            ? (newValue as (val: T) => T)(currentValue)
            : newValue
        localStorage.setItem(key, JSON.stringify(result))
        return result
      } catch (error) {
        console.error(`Error saving to localStorage key "${key}":`, error)
        return currentValue
      }
    })
  }

  return [value, setValueInLocalStorage]
}
