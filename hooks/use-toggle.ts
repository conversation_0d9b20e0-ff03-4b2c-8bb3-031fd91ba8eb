import { useState, useCallback } from "react"

export const useToggle = (initialValue: boolean = false) => {
  const [value, setValue] = useState<boolean>(initialValue)

  // Toggle function
  const toggle = useCallback(() => {
    setValue((prev) => !prev)
  }, [])

  // Set function
  const set = useCallback((newValue: boolean) => {
    setValue(newValue)
  }, [])

  const onClose = useCallback(() => {
    setValue(false)
  }, [])

  const onOpen = useCallback(() => {
    setValue(true)
  }, [])

  return { value, toggle, set, onClose, onOpen }
}
