import { useEffect, useState } from "react"
import { UseEmblaCarouselType } from "embla-carousel-react"

export const useCanScroll = (emblaApi: UseEmblaCarouselType[1]) => {
  const [canScrollPrev, setCanScrollPrev] = useState(false)
  const [canScrollNext, setCanScrollNext] = useState(false)

  // Disable buttons when carousel is at the start or end
  useEffect(() => {
    if (!emblaApi) return

    const onSelect = () => {
      setCanScrollPrev(emblaApi.canScrollPrev())
      setCanScrollNext(emblaApi.canScrollNext())
    }
    onSelect()
    emblaApi.on("select", onSelect)
    emblaApi.on("scroll", onSelect)

    return () => {
      emblaApi.off("select", onSelect)
      emblaApi.off("scroll", onSelect)
    }
  }, [emblaApi])

  return { canScrollPrev, canScrollNext }
}
