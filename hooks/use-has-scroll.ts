import { useEffect, useState, useCallback } from "react"

export const useHasScroll = () => {
  const [hasScroll, setHasScroll] = useState(() =>
    typeof window !== "undefined" ? window.scrollY > 0 : false,
  )

  const handleScroll = useCallback(() => {
    setHasScroll(window.scrollY > 0)
  }, [])

  useEffect(() => {
    window.addEventListener("scroll", handleScroll, { passive: true })

    return () => {
      window.removeEventListener("scroll", handleScroll)
    }
  }, [handleScroll])

  return hasScroll
}
