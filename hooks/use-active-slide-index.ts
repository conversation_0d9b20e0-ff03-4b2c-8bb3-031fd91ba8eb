import { useEffect, useState } from "react"
import { UseEmblaCarouselType } from "embla-carousel-react"

export const useActiveSlideIndex = (emblaApi: UseEmblaCarouselType[1]) => {
  const [activeIndex, setActiveIndex] = useState(0)

  useEffect(() => {
    if (!emblaApi) return

    const onSelect = () => {
      setActiveIndex(emblaApi.selectedScrollSnap())
    }
    onSelect()
    emblaApi.on("select", onSelect)
    emblaApi.on("scroll", onSelect)

    return () => {
      emblaApi.off("select", onSelect)
      emblaApi.off("scroll", onSelect)
    }
  }, [emblaApi])

  return { activeIndex }
}
