import { SEOComponent } from "@/app/shared/components"
import { generateSEOMetadata, PAGE_SEO } from "@/app/shared/utils/seo"
import { BREADCRUMB_PATHS } from "@/app/shared/utils/seo-breadcrumbs"
import FAQScreen from "@/screens/FAQScreen"
import { FAQ_DATA } from "../../config"

export const metadata = generateSEOMetadata({
  ...PAGE_SEO.FAQ,
  url: "https://nex-move.com/faq",
})

const Page = () => {
  const breadcrumbs = [BREADCRUMB_PATHS.HOME, BREADCRUMB_PATHS.FAQ]

  const faqSchema = {
    "@context": "https://schema.org",
    "@type": "FAQPage",

    mainEntity: FAQ_DATA.general.map((faq) => ({
      "@type": "Question",
      name: faq.question,
      acceptedAnswer: {
        "@type": "Answer",
        text: faq.answer,
      },
    })),
  }
  return (
    <>
      <SEOComponent breadcrumbs={breadcrumbs} structuredData={[faqSchema]} />
      <FAQScreen />
    </>
  )
}

export default Page
