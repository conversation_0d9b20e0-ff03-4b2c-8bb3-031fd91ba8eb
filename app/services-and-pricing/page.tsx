import { SEOComponent } from "@/app/shared/components"
import {
  generateSEOMetadata,
  generateServiceSchema,
  PAGE_SEO,
} from "@/app/shared/utils/seo"
import { BREADCRUMB_PATHS } from "@/app/shared/utils/seo-breadcrumbs"
import { ServicesAndPricingScreen } from "@/screens"

export const metadata = generateSEOMetadata({
  ...PAGE_SEO.SERVICES,
  url: "https://nex-move.com/services-and-pricing",
})

const Page = () => {
  const breadcrumbs = [BREADCRUMB_PATHS.HOME, BREADCRUMB_PATHS.SERVICES]

  const servicesSchema = [
    generateServiceSchema({
      name: "Packing & Moving Services",
      description:
        "Professional packing and moving services for homes and offices",
      url: "/nexmove-packing-and-moving",
      area: ["India"],
    }),
    generateServiceSchema({
      name: "Trucking Services",
      description: "Reliable trucking and freight services",
      url: "/nexmove-trucking",
      area: ["India"],
    }),
    generateServiceSchema({
      name: "Storage Solutions",
      description: "Secure storage and warehousing services",
      url: "/nexmove-storage",
      area: ["India"],
    }),
  ]

  return (
    <>
      <SEOComponent breadcrumbs={breadcrumbs} structuredData={servicesSchema} />
      <ServicesAndPricingScreen />
    </>
  )
}

export default Page
