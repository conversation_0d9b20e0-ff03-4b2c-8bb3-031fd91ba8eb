import { OrdersScreen } from "@/screens"
import { Metadata } from "next"
import { notFound } from "next/navigation"

export const metadata: Metadata = {
  title: "Orders - Seller Dashboard",
  description: "Manage your marketplace orders",
}

interface OrdersPageProps {
  params: Promise<{
    sellerId: string
  }>
}

const OrdersPage = async ({ params }: OrdersPageProps) => {
  const { sellerId } = await params
  if (!sellerId) {
    notFound()
  }
  return <OrdersScreen sellerId={sellerId} />
}

export default OrdersPage
