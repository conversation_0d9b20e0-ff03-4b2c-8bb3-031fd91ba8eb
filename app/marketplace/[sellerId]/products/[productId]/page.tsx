import { ProductsScreen } from "@/screens"
import { Metadata } from "next"
import { notFound } from "next/navigation"

export const metadata: Metadata = {
  title: "Product Form - Seller Dashboard",
  description: "Add or edit your marketplace product",
}

interface ProductFormPageProps {
  params: Promise<{
    sellerId: string
    productId: string
  }>
}

const ProductFormPage = async ({ params }: ProductFormPageProps) => {
  const { sellerId, productId } = await params
  if (!sellerId || !productId) {
    notFound()
  }
  return <ProductsScreen sellerId={sellerId} />
}

export default ProductFormPage
