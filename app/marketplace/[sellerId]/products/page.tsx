import { ProductsScreen } from "@/screens"
import { Metadata } from "next"
import { notFound } from "next/navigation"

export const metadata: Metadata = {
  title: "Products - Seller Dashboard",
  description: "Manage your marketplace products",
}

interface ProductsPageProps {
  params: Promise<{
    sellerId: string
  }>
}

const ProductsPage = async ({ params }: ProductsPageProps) => {
  const { sellerId } = await params
  if (!sellerId) {
    notFound()
  }
  return <ProductsScreen sellerId={sellerId} />
}

export default ProductsPage
