import { SellerDashboardScreen } from "@/screens"
import { Metadata } from "next"

export const metadata: Metadata = {
  title: "Seller Dashboard - NexMove Marketplace",
  description:
    "Manage your products, orders, and grow your business on NexMove marketplace",
}

interface SellerDashboardPageProps {
  params: Promise<{
    sellerId: string
  }>
}

const SellerDashboardPage = async ({ params }: SellerDashboardPageProps) => {
  const { sellerId } = await params
  return <SellerDashboardScreen sellerId={sellerId} />
}

export default SellerDashboardPage
