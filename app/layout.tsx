import { generateSEOMetadata } from "@/app/shared/utils/seo"
import { AuthProvider } from "@/context"
import { QueryProvider } from "@/lib/react-query"
import type { Metadata } from "next"
import Image from "next/image"
import Script from "next/script"
import <PERSON>Top<PERSON>oader from "nextjs-toploader"
import { Toaster } from "sonner"
import { geist, inter, lato, manrope } from "../config"
import { <PERSON>er, Header } from "./components/global"
import "./globals.css"

export const metadata: Metadata = {
  ...generateSEOMetadata({
    title: "Global Moving & Logistics Services | Nex‑Move",
    description:
      "Nex‑Move delivers seamless moving, storage, transportation & relocation services across India and globally. Over a decade of trusted logistics expertise.",
    keywords: [
      "moving services india",
      "professional movers",
      "logistics company",
      "packing moving services",
      "transport services",
      "global moving services",
      "relocation services",
      "storage solutions",
      "international moving",
      "domestic relocation",
    ],
    url: "https://nex-move.com",
  }),
  icons: {
    icon: "/favicon.ico",
    apple: "/favicon.ico",
  },
  manifest: "/manifest.json",
  verification: {
    google: "your-google-verification-code",
    other: {
      "msvalidate.01": "your-bing-verification-code",
    },
  },
  robots: {
    index: true,
    follow: true,
  },
  viewport: {
    width: "device-width",
    initialScale: 1,
  },
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang='en' suppressHydrationWarning>
      <body
        className={`${inter.variable} ${lato.variable} ${geist.variable} ${manrope.variable}`}
      >
        <Script
          strategy='lazyOnload'
          src='https://embed.tawk.to/689319c07327e0192775683a/1j1v9p6ub'
        />

        <NextTopLoader
          color={"#ff5b00"}
          initialPosition={0.8}
          crawlSpeed={200}
          height={3}
          crawl={true}
          showSpinner={false}
          easing='ease'
          speed={200}
          shadow='0 0 10px #ff5b00,0 0 5px #ff5b00'
        />
        <QueryProvider>
          <Script
            src={`https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&libraries=places,geometry`}
            strategy='beforeInteractive'
            // strategy='lazyOnload' // this is breaking the pages where google maps is used , and opens directly.
          />

          {/* Meta Pixel Code */}
          <Script id='meta-pixel' strategy='lazyOnload'>
            {`
              !function(f,b,e,v,n,t,s)
              {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
              n.callMethod.apply(n,arguments):n.queue.push(arguments)};
              if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
              n.queue=[];t=b.createElement(e);t.async=!0;
              t.src=v;s=b.getElementsByTagName(e)[0];
              s.parentNode.insertBefore(t,s)}(window, document,'script',
              'https://connect.facebook.net/en_US/fbevents.js');
              fbq('init', '${process.env.NEXT_PUBLIC_META_PIXEL_ID}');
              fbq('track', 'PageView');
            `}
          </Script>
          <noscript>
            <Image
              height={1}
              width={1}
              style={{ display: "none" }}
              alt=''
              src={`https://www.facebook.com/tr?id=${process.env.NEXT_PUBLIC_META_PIXEL_ID}&ev=PageView&noscript=1`}
              unoptimized={true}
            />
          </noscript>

          {/* <GoogleScriptProvider> */}
          <AuthProvider>
            <Header />
            {children}
            <Footer />
            <Toaster position='top-right' richColors />
          </AuthProvider>
          {/* </GoogleScriptProvider> */}
        </QueryProvider>
      </body>
    </html>
  )
}
