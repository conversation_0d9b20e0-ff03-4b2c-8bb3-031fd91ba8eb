import { SEOComponent } from "@/app/shared/components"
import {
  generateSEOMetadata,
  generateServiceSchema,
  PAGE_SEO,
} from "@/app/shared/utils/seo"
import { BREADCRUMB_PATHS } from "@/app/shared/utils/seo-breadcrumbs"
import NexMoveTruckingContainer from "../../containers/services/nexmove-trucking/NexMoveTruckingContainer"

export const metadata = generateSEOMetadata({
  ...PAGE_SEO.TRUCKING,
  url: "https://nex-move.com/nexmove-trucking",
})

const Page = () => {
  const breadcrumbs = [
    BREADCRUMB_PATHS.HOME,
    BREADCRUMB_PATHS.SERVICES,
    BREADCRUMB_PATHS.TRUCKING,
  ]

  const serviceSchema = generateServiceSchema({
    name: "Trucking & Part-Load Services",
    description:
      "Reliable trucking services for full and part loads. Vehicle booking, flexible hire options, and efficient goods transportation across India.",
    url: "/nexmove-trucking",
    area: [
      "India",
      "Delhi",
      "Mumbai",
      "Bangalore",
      "Chennai",
      "Kolkata",
      "Hyderabad",
      "Pune",
    ],
  })

  return (
    <>
      <SEOComponent
        breadcrumbs={breadcrumbs}
        structuredData={[serviceSchema]}
      />
      <NexMoveTruckingContainer />
    </>
  )
}

export default Page
