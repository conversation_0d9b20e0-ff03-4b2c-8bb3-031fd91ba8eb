import { Box, Typography } from "@mui/material"
import { styled } from "@mui/material/styles"

export const CardContainer = styled(Box)(({ theme }) => ({
  display: "flex",
  flexDirection: "column",
  alignItems: "flex-start",
  padding: "23px 25px",
  gap: "63px",
  flexShrink: 0,
  backgroundColor: "#F2F5F6",
  height: "100%", // Ensure card always fills the parent height
  minHeight: "480px", // Set a minimum height for visual consistency
  boxSizing: "border-box",
  [theme.breakpoints.down("sm")]: {
    width: "100%",
    height: "auto",
    padding: "16px",
    gap: "40px",
    minHeight: "unset",
  },
}))

export const TextSection = styled(Box)(({ theme }) => ({
  display: "flex",
  width: "391px",
  flexDirection: "column",
  alignItems: "flex-start",
  gap: "12px",
  flex: 1, // Make TextSection grow to fill available space
  [theme.breakpoints.down("sm")]: {
    width: "100%",
  },
}))

export const IconWrapper = styled(Box)(() => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "flex-start",
}))

export const Title = styled(Typography)(({ theme }) => ({
  fontSize: "20px",
  fontWeight: 700,
  color: "#000",
  [theme.breakpoints.down("sm")]: {
    fontSize: "18px",
  },
}))

export const Description = styled(Typography)(({ theme }) => ({
  fontSize: "14px",
  fontWeight: 400,
  color: "#6C6C6C",
  lineHeight: "22px",
  [theme.breakpoints.down("sm")]: {
    fontSize: "13px",
  },
}))

export const ImageSection = styled(Box)(({ theme }) => ({
  position: "relative",
  width: "391px",
  height: "248px",
  flexShrink: 0,

  [theme.breakpoints.down("sm")]: {
    width: "100%",
    height: "200px",
  },
}))
