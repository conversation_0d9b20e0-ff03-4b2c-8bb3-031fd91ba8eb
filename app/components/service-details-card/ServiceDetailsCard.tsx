import Image from "next/image"
import React from "react"
import {
  CardContainer,
  Description,
  IconWrapper,
  ImageSection,
  TextSection,
  Title,
} from "./ServiceDetailsCard.style"

export interface ServiceDetailsCardProps {
  title: string
  description: string
  icon: React.JSX.Element | null
  imageUrl: string
}

const ServiceDetailsCard: React.FC<ServiceDetailsCardProps> = ({
  title,
  description,
  icon,
  imageUrl,
}: ServiceDetailsCardProps) => {
  return (
    <CardContainer>
      <TextSection>
        <IconWrapper>{icon}</IconWrapper>
        <Title>{title}</Title>
        <Description>{description}</Description>
      </TextSection>

      {imageUrl ? (
        <ImageSection>
          <Image src={imageUrl} alt={title} layout='fill' objectFit='cover' />
        </ImageSection>
      ) : null}
    </CardContainer>
  )
}

export default ServiceDetailsCard
