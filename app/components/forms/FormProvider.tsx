import { type ComponentProps, type ReactNode } from "react"
import type { FieldValues, UseFormReturn } from "react-hook-form"
import { FormProvider as RHFormProvider } from "react-hook-form"

type LocalFormProviderProps<T extends FieldValues> = {
  methods: UseFormReturn<T>
  onSubmit: (data: T) => void
  children?: ReactNode
}

export const FormProvider = <T extends FieldValues>({
  methods,
  onSubmit,
  children,
}: LocalFormProviderProps<T>) => {
  const providerProps: ComponentProps<typeof RHFormProvider> = {
    ...(methods as unknown as ComponentProps<typeof RHFormProvider>),
    children: <form onSubmit={methods.handleSubmit(onSubmit)}>{children}</form>,
  }

  return <RHFormProvider {...providerProps} />
}
