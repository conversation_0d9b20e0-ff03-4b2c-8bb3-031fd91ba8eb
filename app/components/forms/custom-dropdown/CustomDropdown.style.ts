import { MenuItem, styled, TextField } from "@mui/material"

export const StyledSelectField = styled(TextField)(({}) => ({
  width: "100%",
  "& input[type=number]": {
    MozAppearance: "textfield",
  },
  "& input[type=number]::-webkit-outer-spin-button": {
    WebkitAppearance: "none",
    margin: 0,
  },
  "& input[type=number]::-webkit-inner-spin-button": {
    WebkitAppearance: "none",
    margin: 0,
  },
}))

export const StyledSelectItem = styled(MenuItem)(() => ({
  textTransform: "capitalize",
}))
