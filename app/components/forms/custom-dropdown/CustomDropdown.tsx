import { DropDownValue } from "@/app/shared/types/DropDownValue"
import { TextFieldVariants } from "@mui/material"
import { StyledSelectField, StyledSelectItem } from "./CustomDropdown.style"

interface ICustomSelectProps {
  label?: string
  variant?: TextFieldVariants
  options: DropDownValue[]
  name: string
  id: string
  value: string
  error?: boolean
  helperText?: string
  required?: boolean
  disabled?: boolean
  size?: "small" | "medium"
  handleChange: (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => void
  handleBlur?: (event: React.FocusEvent<HTMLInputElement>) => void
}

const CustomDropdown = ({
  label,
  options,
  variant,
  name,
  id,
  value,
  error,
  helperText,
  disabled,
  size = "small",
  required = true,
  handleChange,
  handleBlur,
}: ICustomSelectProps) => {
  return (
    <StyledSelectField
      variant={variant}
      id={id}
      name={name}
      label={label}
      size={size}
      helperText={helperText}
      error={error}
      disabled={disabled || options?.length === 0} // Disable the select if there are no options
      aria-readonly={disabled}
      aria-required='true'
      value={value ?? ""}
      onChange={(e) => {
        handleChange(e)
      }}
      onBlur={(e) => {
        if (handleBlur) handleBlur(e as React.FocusEvent<HTMLInputElement>)
      }}
      required={required}
      fullWidth
      select
      autoComplete='off'
    >
      {/* Fallback view when there are no options */}
      {options?.length === 0 ? (
        <StyledSelectItem
          disabled
          value=''
          aria-valuetext='No options available'
        >
          No options available
        </StyledSelectItem>
      ) : (
        options?.map((item) => (
          <StyledSelectItem
            key={item?.id}
            value={item?.id}
            aria-valuetext={item?.name}
            disabled={item?.isDisabled}
          >
            {item?.name}
          </StyledSelectItem>
        ))
      )}
    </StyledSelectField>
  )
}

export default CustomDropdown
