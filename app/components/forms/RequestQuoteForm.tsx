/* eslint-disable @typescript-eslint/no-explicit-any */
"use client"

import { TRequestQuoteFormValues, requestQuoteFormSchema } from "./schemas"

import { InputLabel } from "@/app/shared/components"
import { getContactMethods } from "@/app/shared/services/contact-request/contact-request.service"
import { sendEmail } from "@/app/shared/services/email.service"
import { sendOtp, verifyOtp } from "@/app/shared/services/otp/otp.service"
import {
  formatServicesForDropdown,
  getServicesWithSubcategories,
  submitQuoteRequest,
} from "@/app/shared/services/request-quote/request-quote.service"
import { RequestQuote } from "@/app/shared/services/request-quote/request-quote.type"
import { DropDownValue } from "@/app/shared/types/DropDownValue"
import { getRequestQuoteEmailHtml } from "@/app/shared/utils/email-templates"
import { ChecklistIcon, ClockIcon } from "@/Icons"
import { calculateDistance } from "@/lib/google-map-api"
import { zodResolver } from "@hookform/resolvers/zod"
import { useEffect, useMemo, useState } from "react"
import { Controller, useForm } from "react-hook-form"
import { toast } from "sonner"
import { InputAutoComplete, Loader, OtpVerificationModal } from "../common"
import { Button } from "../ui"
import {
  EmailInputController,
  NumberOnlyInputController,
  TextAreaInputController,
  TextOnlyInputController,
} from "./controllers/text-field"
import CustomDropdown from "./custom-dropdown/CustomDropdown"
import { FormProvider } from "./FormProvider"

// Enhanced Select Input component that will be created in the RequestQuoteForm component

const defaultValues: Partial<TRequestQuoteFormValues> = {
  name: "",
  phone: "",
  emailId: "",
  serviceId: null as any, // Cast to any to avoid type error
  serviceSubCategoryId: null as any, // Cast to any to avoid type error
  message: "",
  preferredContactMethodId: null as any, // Cast to any to avoid type error
  origin: "",
  destination: "",
  distanceInKm: 0,
  isPhoneVerified: false,
}

// Service types and contact methods will be fetched from API

// Define interfaces for API responses
interface SubCategory {
  name: string
  id: number
  lastModified: string | null
}

interface Service {
  id: number
  name: string
  code: string
  lastModified: string | null
  subCategories: SubCategory[]
}

const RequestQuoteForm = () => {
  const [isOtpModalOpen, setIsOtpModalOpen] = useState(false)
  const [isVerifying, setIsVerifying] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // State for API data
  const [services, setServices] = useState<DropDownValue[]>([])
  const [servicesData, setServicesData] = useState<Service[]>([])
  const [contactMethods, setContactMethods] = useState<DropDownValue[]>([])
  // const [isLoading, setIsLoading] = useState(true)

  // States for Google Maps integration
  type TGooglePlaceResult = google.maps.places.PlaceResult
  const [selectedOriginLocation, setSelectedOriginLocation] =
    useState<TGooglePlaceResult | null>(null)
  const [selectedDestinationLocation, setSelectedDestinationLocation] =
    useState<TGooglePlaceResult | null>(null)
  const [distance, setDistance] = useState<string | null>(null)

  const methods = useForm<TRequestQuoteFormValues>({
    defaultValues,
    resolver: zodResolver(requestQuoteFormSchema),
    mode: "onChange",
    criteriaMode: "all", // Show all validation errors
  })

  const { watch, setValue, reset, formState } = methods
  const values = watch()
  const { isValid } = formState

  // Fetch services and contact methods from API
  useEffect(() => {
    const fetchData = async () => {
      // setIsLoading(true)
      try {
        // Fetch services with subcategories
        const servicesData = await getServicesWithSubcategories()
        setServicesData(servicesData)

        // Transform services data for dropdown
        const servicesForDropdown = formatServicesForDropdown(servicesData)
        setServices(servicesForDropdown)

        // Fetch contact methods
        const contactMethodsData = await getContactMethods()
        setContactMethods(contactMethodsData)
      } catch (error) {
        console.error("Error fetching data:", error)
        toast.error("Failed to load form data. Please try again.")
      } finally {
        // setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  // Calculate distance when both origin and destination are selected
  useEffect(() => {
    if (selectedOriginLocation && selectedDestinationLocation) {
      const getDistance = async () => {
        try {
          toast.loading("Calculating distance...", { id: "calculate-distance" })
          const distanceText = await calculateDistance(
            selectedOriginLocation,
            selectedDestinationLocation,
          )
          toast.dismiss("calculate-distance")

          setDistance(distanceText)

          // Extract numeric value from distance text (e.g., "10 km" -> 10)
          if (distanceText) {
            const numericDistance = parseFloat(
              distanceText.replace(/[^0-9.]/g, ""),
            )

            // Update the form value
            setValue("distanceInKm", numericDistance, {
              shouldValidate: true,
            })
          }
        } catch (error) {
          console.error("Error calculating distance:", error)
          toast.error("Failed to calculate distance. Please try again.", {
            id: "calculate-distance",
          })
        }
      }

      getDistance()
    } else if (!selectedOriginLocation || !selectedDestinationLocation) {
      // Reset distance when either origin or destination is cleared
      setDistance(null)
      setValue("distanceInKm", 0, { shouldValidate: true })
    }
  }, [selectedOriginLocation, selectedDestinationLocation, setValue])

  const serviceId = watch("serviceId")

  // Reset distance when origin or destination form values are cleared
  useEffect(() => {
    const origin = values.origin
    const destination = values.destination

    if (!origin || !destination) {
      setDistance(null)
      setValue("distanceInKm", 0, { shouldValidate: true })
    }
  }, [values.origin, values.destination, setValue])

  // For backward compatibility, we need to handle both serviceType and serviceId
  const serviceType = useMemo(() => {
    if (serviceId) {
      const service = servicesData.find((s) => s.id === serviceId)
      return service?.name || ""
    }
    return ""
  }, [serviceId, servicesData])

  // Get subcategories for the selected service
  const serviceSubCategoryOptions = useMemo(() => {
    if (!serviceType || !servicesData.length) return []

    const selectedService = servicesData.find(
      (service) => service.name === serviceType,
    )

    if (!selectedService || !selectedService.subCategories) return []

    return selectedService.subCategories.map((subCategory) => ({
      id: subCategory.id,
      name: subCategory.name,
    }))
  }, [serviceType, servicesData])

  useEffect(() => {
    // Reset subcategory when service type changes
    setValue("serviceSubCategoryId", null as any, { shouldValidate: false })
  }, [serviceType, setValue])

  const handleVerifyPhone = async () => {
    let phoneNumber = values.phone
    if (!phoneNumber) {
      toast.error("Please enter a phone number")
      return
    }
    phoneNumber = phoneNumber.replace(/\D/g, "")
    if (phoneNumber.length !== 10) {
      toast.error("Please enter a valid 10-digit phone number")
      return
    }
    setValue("phone", phoneNumber, { shouldValidate: true })
    try {
      setIsVerifying(true)
      toast.loading("Sending OTP...", { id: "send-otp" })
      const result = await sendOtp(phoneNumber)
      toast.dismiss("send-otp")
      if (result.success) {
        setIsOtpModalOpen(true)
      } else {
        toast.error(result.message || "Failed to send OTP.", { id: "send-otp" })
      }
    } catch (error) {
      console.error("Error sending OTP:", error)
      toast.error("Failed to send OTP. Please try again.", { id: "send-otp" })
    } finally {
      setIsVerifying(false)
    }
  }

  const handleVerifyOtp = async (otp: string) => {
    let phoneNumber = values.phone
    if (!phoneNumber) {
      toast.error("Phone number is missing")
      throw new Error("Phone number is missing")
    }
    phoneNumber = phoneNumber.replace(/\D/g, "")
    if (!otp || otp.length !== 6 || !/^\d+$/.test(otp)) {
      toast.error("Please enter a valid 6-digit OTP")
      throw new Error("Invalid OTP format")
    }
    try {
      const result = await verifyOtp(phoneNumber, otp)
      if (result.isVerified) {
        setValue("isPhoneVerified", true, { shouldValidate: true })
        toast.success("Phone number verified successfully", {
          id: "verify-otp",
        })
        setIsOtpModalOpen(false)
      } else {
        toast.error(result.message || "Invalid OTP. Please try again.", {
          id: "verify-otp",
        })
        throw new Error(result.message || "Invalid OTP. Please try again.")
      }
    } catch (error) {
      console.error("Error verifying OTP:", error)
      toast.error("Failed to verify OTP. Please try again.", {
        id: "verify-otp",
      })
      throw new Error("Failed to verify OTP. Please try again.")
    }
  }

  const handleResendOtp = async () => {
    let phoneNumber = values.phone
    if (!phoneNumber) {
      toast.error("Phone number is missing")
      throw new Error("Phone number is missing")
    }
    phoneNumber = phoneNumber.replace(/\D/g, "")
    try {
      toast.loading("Sending OTP...", { id: "resend-otp" })
      const result = await sendOtp(phoneNumber)
      toast.dismiss("resend-otp")
      if (result.success) {
        toast.success("OTP resent successfully", { id: "resend-otp" })
      } else {
        toast.error(result.message || "Failed to resend OTP.", {
          id: "resend-otp",
        })
        throw new Error(result.message || "Failed to resend OTP.")
      }
    } catch (error) {
      console.error("Error resending OTP:", error)
      toast.error("Failed to resend OTP. Please try again.", {
        id: "resend-otp",
      })
      throw new Error("Failed to resend OTP. Please try again.")
    }
  }

  const onSubmit = async (data: TRequestQuoteFormValues) => {
    // Validate phone verification
    if (!data.isPhoneVerified) {
      toast.error("Please verify your phone number before submitting")
      return
    }

    // if (!user) {
    //   toast.error("You need to be logged in to submit a quote request");
    //   return;
    // }

    // Validate preferred contact method
    if (!data.preferredContactMethodId) {
      toast.error("Please select a preferred contact method")
      return
    }

    // Clean phone number
    const cleanPhone = data.phone.replace(/\D/g, "")

    try {
      setIsSubmitting(true)
      toast.loading("Submitting your quote request...", {
        id: "quoteRequest",
      })

      const quoteRequestData: RequestQuote = {
        name: data.name.trim(),
        emailId: data.emailId.trim(),
        phone: cleanPhone,
        preferredContactMethodId: data.preferredContactMethodId,
        serviceId: data.serviceId,
        serviceSubCategoryId: data.serviceSubCategoryId,
        message: data.message ? data.message.trim() : "",
        origin: data.origin ? data.origin.trim() : "",
        destination: data.destination ? data.destination.trim() : "",
        distanceInKm: data.distanceInKm,
        statusId: 1, // New request
      }

      await submitQuoteRequest(quoteRequestData)

      // Send email with quote details via backend API
      await sendEmail({
        subject: `New Quote Request from ${quoteRequestData.name}`,
        html: getRequestQuoteEmailHtml({
          name: quoteRequestData.name,
          emailId: quoteRequestData.emailId,
          phone: quoteRequestData.phone,
          service:
            services.find(
              (s) => Number(s.id) === Number(quoteRequestData.serviceId),
            )?.name || "",
          subCategory:
            serviceSubCategoryOptions.find(
              (sc) =>
                Number(sc.id) === Number(quoteRequestData.serviceSubCategoryId),
            )?.name || "",
          origin: quoteRequestData.origin || "",
          destination: quoteRequestData.destination || "",
          distanceInKm: quoteRequestData.distanceInKm ?? 0,
          preferredContactMethod:
            contactMethods.find(
              (m) =>
                Number(m.id) ===
                Number(quoteRequestData.preferredContactMethodId),
            )?.name || "",
          message: quoteRequestData.message || "",
        }),
      })

      reset(defaultValues)
      setDistance(null)
      toast.success("Your quote request has been submitted successfully!", {
        id: "quoteRequest",
      })
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to submit quote request. Please try again."
      toast.error(errorMessage)
    } finally {
      setIsSubmitting(false)
      toast.dismiss("quoteRequest")
    }
  }

  return (
    <FormProvider methods={methods} onSubmit={onSubmit}>
      <>
        <>
          <div className='mx-auto grid max-w-screen-lg ~mt-10/12 ~gap-x-3/5 ~gap-y-3/6 sm:grid-cols-2'>
            <div>
              <InputLabel label='Full Name' id='name' />
              <TextOnlyInputController
                name='name'
                placeholder='Enter Full Name'
              />
            </div>
            <div>
              <InputLabel label='Phone Number' id='phone' />
              <div className='flex items-center gap-2'>
                <NumberOnlyInputController
                  name='phone'
                  placeholder='Enter Phone Number'
                  disabled={values.isPhoneVerified}
                  maxLength={10}
                />
                {!values.isPhoneVerified ? (
                  <Button
                    size='sm'
                    onClick={handleVerifyPhone}
                    disabled={isVerifying}
                    type='button'
                    className='whitespace-nowrap !px-3 !py-1.5'
                  >
                    {isVerifying ? <Loader size={15} /> : "Verify"}
                  </Button>
                ) : (
                  <div className='flex items-center gap-1 px-2 text-green-600'>
                    <ChecklistIcon className='text-lg' />
                    <span className='text-sm font-medium'>Verified</span>
                  </div>
                )}
              </div>
            </div>
            <div>
              <InputLabel label='Email' id='emailId' />
              <EmailInputController name='emailId' placeholder='Enter Email' />
            </div>
            <div>
              <InputLabel label='Service Type' id='serviceId' />
              <Controller
                name='serviceId'
                control={methods.control}
                render={({ field, fieldState: { error } }) => (
                  <CustomDropdown
                    id='serviceId'
                    name={field.name}
                    options={services}
                    value={field.value?.toString() || ""}
                    error={!!error}
                    helperText={error?.message}
                    handleChange={(e) => {
                      const serviceId = Number(e.target.value)
                      field.onChange(serviceId === 0 ? null : serviceId)

                      // For backward compatibility
                      const service = servicesData.find(
                        (s) => s.id === serviceId,
                      )
                      if (service) {
                        setValue("serviceType", service.name, {
                          shouldValidate: false,
                        })
                      }
                    }}
                    handleBlur={field.onBlur}
                  />
                )}
              />
            </div>
            <div>
              <InputLabel
                label='Preferred Contact Method'
                id='preferredContactMethodId'
              />
              <Controller
                name='preferredContactMethodId'
                control={methods.control}
                render={({ field, fieldState: { error } }) => (
                  <CustomDropdown
                    id='preferredContactMethodId'
                    name={field.name}
                    options={contactMethods}
                    value={field.value?.toString() || ""}
                    error={!!error}
                    helperText={error?.message}
                    handleChange={(e) => {
                      const methodId = Number(e.target.value)
                      field.onChange(methodId === 0 ? null : methodId)

                      // For backward compatibility
                      const method = contactMethods.find(
                        (m) => m.id === methodId,
                      )
                      if (method) {
                        setValue("preferedContactMethod", method.name, {
                          shouldValidate: false,
                        })
                      }
                    }}
                    handleBlur={field.onBlur}
                  />
                )}
              />
            </div>
            <div>
              <InputLabel
                label='Service Sub Category'
                id='serviceSubCategoryId'
              />
              <Controller
                name='serviceSubCategoryId'
                control={methods.control}
                render={({ field, fieldState: { error } }) => (
                  <CustomDropdown
                    id='serviceSubCategoryId'
                    name={field.name}
                    options={serviceSubCategoryOptions}
                    value={field.value?.toString() || ""}
                    error={!!error}
                    helperText={error?.message}
                    disabled={serviceSubCategoryOptions.length === 0}
                    handleChange={(e) => {
                      const subCategoryId = Number(e.target.value)
                      field.onChange(subCategoryId === 0 ? null : subCategoryId)

                      // For backward compatibility
                      const selectedService = servicesData.find(
                        (s) => s.name === serviceType,
                      )
                      const subCategory = selectedService?.subCategories.find(
                        (sc) => sc.id === subCategoryId,
                      )
                      if (subCategory) {
                        setValue("subCategory", subCategory.name, {
                          shouldValidate: false,
                        })
                      }
                    }}
                    handleBlur={field.onBlur}
                  />
                )}
              />
            </div>
            <div>
              <InputLabel label='Origin' id='origin' />
              <InputAutoComplete
                onChange={(place) => {
                  setSelectedOriginLocation(place || null)
                  setValue("origin", place?.formatted_address || "", {
                    shouldValidate: true,
                  })
                }}
              >
                <TextOnlyInputController
                  name='origin'
                  placeholder='Enter Origin Location'
                />
              </InputAutoComplete>
            </div>

            <div>
              <InputLabel label='Destination' id='destination' />
              <InputAutoComplete
                onChange={(place) => {
                  setSelectedDestinationLocation(place || null)
                  setValue("destination", place?.formatted_address || "", {
                    shouldValidate: true,
                  })
                }}
              >
                <TextOnlyInputController
                  name='destination'
                  placeholder='Enter Destination Location'
                />
              </InputAutoComplete>
            </div>

            {distance && (
              <div className='col-span-full flex items-center gap-2 rounded-md bg-blue-100 text-sm font-medium text-blue-600 ~px-3/5 ~py-2/2.5'>
                <ClockIcon className='flex-shrink-0 rotate-180 text-[1.2em]' />
                <p>
                  Distance between origin and destination is{" "}
                  <span className='text-base font-bold'>{distance}</span>
                </p>
              </div>
            )}

            <div className='sm:col-span-2'>
              <InputLabel label='Message' id='message' />
              <TextAreaInputController
                name='message'
                placeholder='Enter Message (Min 10 characters)'
                rows={4}
                maxLength={300}
              />
            </div>
          </div>
          <div className='flex flex-col items-center gap-4 ~mt-7/10'>
            <div className='flex w-full justify-center gap-4'>
              <Button
                size='sm'
                type='submit'
                className='w-full max-w-80 font-inter uppercase'
                disabled={!isValid || !values.isPhoneVerified || isSubmitting}
              >
                {isSubmitting ? <Loader size={15} /> : "Get a Quote"}
              </Button>
            </div>
          </div>
        </>
      </>

      {/* OTP Verification Modal */}
      <OtpVerificationModal
        open={isOtpModalOpen}
        onClose={() => setIsOtpModalOpen(false)}
        phoneNumber={values.phone || ""}
        onVerify={handleVerifyOtp}
        onResendOtp={handleResendOtp}
      />
    </FormProvider>
  )
}

export default RequestQuoteForm
