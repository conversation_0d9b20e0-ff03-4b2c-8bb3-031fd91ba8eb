"use client"

import { InputLabel } from "@/app/shared/components"
import { useAuth } from "@/context"
import { useSearchParams } from "next/navigation"
import { Button } from "../ui"
import { TextInputController } from "./controllers/text-field"
import { SchemaFormProvider } from "./SchemaFormProvider"
import { TUserFormValues, userFormSchema } from "./schemas"

const defaultValues: TUserFormValues = {
  name: "",
  email: "",
  phone: "",
}

type TCreateUserFormProps = {
  title?: string
  onUserCreation?: () => void
}

const CreateUserForm = (props: TCreateUserFormProps) => {
  const defaultTitle = "Fill the below form to get your calculated results"
  const { title = defaultTitle, onUserCreation } = props

  const { register } = useAuth()

  const searchParams = useSearchParams()
  const id = searchParams?.get("id") || "" // Assuming 'id' is a query parameter for the service ID
  // const id = searchParams.get("id") || ""

  const onSubmit = async (data: TUserFormValues) => {
    try {
      await register({ ...data, service: id })
      onUserCreation?.()
    } catch (error) {
      console.error(error)
    }
  }

  return (
    <div>
      <h2 className='font-medium ~text-lg/2xl ~mb-6/8 ~pr-8/10'>{title}</h2>
      <SchemaFormProvider
        schema={userFormSchema}
        defaultValues={defaultValues}
        onSubmit={onSubmit}
      >
        <div className='grid ~gap-2/3'>
          <div>
            <InputLabel label='Name' id='name' />
            <TextInputController name='name' placeholder='Enter your name' />
          </div>
          <div>
            <InputLabel label='Email' id='email' />
            <TextInputController name='email' placeholder='Enter your email' />
          </div>
          <div>
            <InputLabel label='Phone Number' id='phone' />
            <TextInputController
              name='phone'
              placeholder='Enter Phone Number'
              type='number'
            />
          </div>
        </div>
        <div className='flex justify-center ~mt-6/8'>
          <Button size='sm' type='submit' className='!px-10'>
            Submit
          </Button>
        </div>
      </SchemaFormProvider>
    </div>
  )
}

export default CreateUserForm
