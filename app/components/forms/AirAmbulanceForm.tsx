"use client"
import { InputLabel } from "@/app/shared/components"
import { zodResolver } from "@hookform/resolvers/zod"
import { useState } from "react"
import { FormProvider, useForm } from "react-hook-form"
import { InputAutoComplete, UserInfoModal } from "../common"
import { Button } from "../ui"
import { DdValueSelectInputController } from "./controllers/select-field"
import {
  NumberOnlyInputController,
  TextInputController,
} from "./controllers/text-field"
import { airAmbulanceFormSchema, TAirAmbulanceFormValues } from "./schemas"

import { sendEmail } from "@/app/shared/services/email.service"
import { getRegionTypes } from "@/app/shared/services/quote-calculator"
import { airAmbulanceCalculationService } from "@/app/shared/services/quote-calculator/air-ambulance"
import { getAirAmbulanceEmailHtml } from "@/app/shared/utils/email-templates"
import {
  checkForVerifiedPhone,
  getUserInfo,
} from "@/app/shared/utils/phone-verification"
import { useToggle } from "@/hooks"
import { extractCityName } from "@/lib/google-map-api"
import { QUERY_KEYS } from "@/lib/react-query"
import { useQuery } from "@tanstack/react-query"
import { toast } from "sonner"
import QuoteCalculatorModal from "../common/QuoteCalculatorModal"
import { QuoteResult } from "../common/types/quote-result.types"

const FORM_DEFAULT_VALUES: TAirAmbulanceFormValues = {
  regionTypeId: 0,
  sourceCity: "",
  destinationCity: "",
  cargoWeightInKgs: "",
}

const AirAmbulanceForm = () => {
  const methods = useForm<TAirAmbulanceFormValues>({
    resolver: zodResolver(airAmbulanceFormSchema),
    defaultValues: FORM_DEFAULT_VALUES,
    mode: "onChange",
    criteriaMode: "all",
  })
  const [sourLocation, setSourceCity] = useState<string | null>(null)
  const [destinationCity, setDestinationCity] = useState<string | null>(null)

  const { formState } = methods
  const { isValid } = formState

  // Fetch region types
  const { data: regionTypes, error: regionTypesError } = useQuery({
    queryKey: [QUERY_KEYS.regionTypes],
    queryFn: getRegionTypes,
  })

  // We don't need user context for this form

  // User info modal state
  const {
    value: userInfoModalOpen,
    onOpen: openUserInfoModal,
    onClose: closeUserInfoModal,
  } = useToggle()

  // Quote calculator modal state
  const {
    value: quoteModalOpen,
    onOpen: openQuoteModal,
    onClose: closeQuoteModal,
  } = useToggle()

  // state for calculation result
  const [calculationResult, setCalculationResult] =
    useState<QuoteResult | null>(null)
  const [isCalculationLoading, setIsCalculationLoading] = useState(false)

  // We don't need to track userInfo state anymore with the simplified approach

  // Using imported isPhoneVerified function

  // Handle user info submission
  const handleUserInfoSubmit = (data: {
    name: string
    email: string
    phone: string
    isPhoneVerified: boolean
  }) => {
    // Close the modal and perform calculation with user info
    closeUserInfoModal()
    performCalculation({
      name: data.name,
      email: data.email,
      phone: data.phone,
    })
  }

  // Perform the actual calculation
  const performCalculation = async (userInfoData?: {
    name: string
    email: string
    phone: string
  }) => {
    openQuoteModal()
    setIsCalculationLoading(true)

    try {
      // Get user info from session storage or from the parameter
      const userInfo = userInfoData || getUserInfo()

      // Include user info in the request data
      const formValues = methods.getValues()
      const result = await airAmbulanceCalculationService({
        data: {
          ...formValues,
          // Add user info to the request
          sourceCity: sourLocation || "",
          destinationCity: destinationCity || "",
          name: userInfo?.name,
          emailId: userInfo?.email,
          phone: userInfo?.phone,
        },
      })
      setCalculationResult(result)

      // Send notification email after successful calculation via backend API
      await sendEmail({
        subject: "New Air Ambulance Service Request",
        html: getAirAmbulanceEmailHtml({
          name: userInfo?.name || "",
          emailId: userInfo?.email || "",
          phone: userInfo?.phone || "",
          regionType: String(formValues.regionTypeId ?? ""),
          sourceCity: sourLocation || formValues.sourceCity || "",
          destinationCity: destinationCity || formValues.destinationCity || "",
          cargoWeightInKgs: formValues.cargoWeightInKgs || "",
        }),
      })
      toast.success(
        "Your request has been submitted and a notification email has been sent.",
      )
    } catch (error) {
      const errorMsg =
        error instanceof Error ? error.message : "Oh no! Something went wrong."
      toast.error(errorMsg)
      setCalculationResult(null)
    } finally {
      setIsCalculationLoading(false)
    }
  }

  // Using imported checkForVerifiedPhone function

  // Form submission handler
  const onSubmit = () => {
    // Check if any phone has been verified in this session
    const verifiedPhone = checkForVerifiedPhone()

    if (verifiedPhone) {
      // If any phone is verified in the session, skip the user info modal
      performCalculation()
    } else {
      // Otherwise, show the user info modal
      openUserInfoModal()
    }
  }

  return (
    <div>
      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(onSubmit)}>
          <div className='grid ~gap-3/4'>
            <div>
              <InputLabel label='Select Your Region' id='regionTypeId' />
              <DdValueSelectInputController
                name='regionTypeId'
                placeholder='Choose your region'
                disabled={!!regionTypesError}
                options={regionTypes ?? []}
              />
            </div>
            <InputAutoComplete
              onChange={(data) => {
                if (data) {
                  const cityName = extractCityName(data)
                  setSourceCity(cityName)
                  methods.setValue("sourceCity", data.formatted_address || "", {
                    shouldValidate: true,
                  })
                }
              }}
            >
              <div>
                <InputLabel label='Source City' id='sourceCity' />
                <TextInputController
                  name='sourceCity'
                  placeholder='Enter Source City'
                />
              </div>
            </InputAutoComplete>
            <InputAutoComplete
              onChange={(data) => {
                if (data) {
                  const cityName = extractCityName(data)
                  setDestinationCity(cityName)
                  methods.setValue(
                    "destinationCity",
                    data.formatted_address || "",
                    {
                      shouldValidate: true,
                    },
                  )
                }
              }}
            >
              <div>
                <InputLabel label='Destination City' id='destinationCity' />
                <TextInputController
                  name='destinationCity'
                  placeholder='Enter Destination City'
                />
              </div>
            </InputAutoComplete>
            <div>
              <InputLabel label='Weight (kg)' id='cargoWeightInKgs' />
              <NumberOnlyInputController
                name='cargoWeightInKgs'
                placeholder='Enter cargo weight in kg'
              />
            </div>
          </div>
          <Button
            size='sm'
            type='submit'
            className='w-full ~mt-6/8'
            disabled={!isValid}
          >
            Calculate
          </Button>
        </form>
      </FormProvider>
      {/* User Info Modal */}
      <UserInfoModal
        open={userInfoModalOpen}
        onClose={closeUserInfoModal}
        onSubmit={handleUserInfoSubmit}
      />

      {/* Quote Calculator Modal */}
      <QuoteCalculatorModal
        open={quoteModalOpen}
        onClose={closeQuoteModal}
        loading={isCalculationLoading}
        calculationResult={calculationResult}
        serviceName='Air Ambulance'
      />
    </div>
  )
}

export default AirAmbulanceForm
