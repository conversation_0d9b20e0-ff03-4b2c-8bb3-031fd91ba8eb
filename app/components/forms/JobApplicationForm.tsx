"use client"

import { Loader } from "@/app/components/common"
import { InputLabel } from "@/app/shared/components"
import { getRefernce } from "@/app/shared/services/contact-request/contact-request.service"
import { uploadFile } from "@/app/shared/services/file-upload"
import {
  JobPosting,
  submitJobApplication,
} from "@/app/shared/services/job-application"
import { sendEmail } from "@/app/shared/utils/email"
import { getJobApplicationEmailHtml } from "@/app/shared/utils/email-templates"
import { QUERY_KEYS } from "@/lib/react-query"
import { zodResolver } from "@hookform/resolvers/zod"
import { useQuery } from "@tanstack/react-query"
import { useEffect, useState } from "react"
import { FormProvider, useForm, useFormContext } from "react-hook-form"
import { toast } from "sonner"
import { Button } from "../ui"
import { CheckboxInputController } from "./controllers/checkbox-field"
import { Date<PERSON>ontroller } from "./controllers/date-field"
import { FileInputController } from "./controllers/file-field/FileInputController"
import { SelectInputController } from "./controllers/select-field"
import {
  NumberOnlyInputController,
  TextInputController,
} from "./controllers/text-field"
import { ZipCodeAutoFill } from "./controllers/text-field/ZipCodeAutoFill"
import { jobApplicationFormSchema, TJobApplicationFormValues } from "./schemas"

// Updated default values to match the flattened structure
const defaultValues: TJobApplicationFormValues = {
  // Personal Details
  name: "",
  phone: "",
  emailId: "",
  addressLine1: "",
  addressLine2: "",
  landmark: "",
  zipcode: "",
  city: "",
  state: "",
  country: "",

  // Employment Details
  totalRelevantExperienceInYears: "",
  previousEmployer: "",
  previousJobPosition: "",
  previousExploymentInYears: "",

  // Job Details
  jobPositionId: 0,
  jobLocation: "",
  availableFromDate: "",
  preferredContactMethodId: 1,
  reference: "Website",

  // Additional Information
  isAuthorizedToWorkInIndia: false,
  requireVisaSponsorship: false,
  resume: null,

  // Terms and Conditions
  isTCAccepted: false,
}

interface JobApplicationFormProps {
  jobPosting: JobPosting
  onSuccess: () => void
  isModal: boolean
}

const JobApplicationForm = ({
  jobPosting,
  onSuccess,
  isModal = false,
}: JobApplicationFormProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Fetch reference options using React Query
  const {
    data: referenceOptions = [],
    isLoading: isLoadingReferences,
    error: referencesError,
  } = useQuery({
    queryKey: QUERY_KEYS.references,
    queryFn: getRefernce,
  })

  // Form setup
  const methods = useForm<TJobApplicationFormValues>({
    resolver: zodResolver(jobApplicationFormSchema),
    defaultValues,
    mode: "onChange",
    criteriaMode: "all",
  })
  const { isValid } = methods.formState

  // Set position ID from props or URL query parameters
  useEffect(() => {
    if (jobPosting.jobPositionId && !isNaN(Number(jobPosting.jobPositionId))) {
      methods.setValue("jobPositionId", Number(jobPosting.jobPositionId), {
        shouldValidate: true,
      })
    } else if (typeof window !== "undefined") {
      const params = new URLSearchParams(window.location.search)
      const positionIdParam = params.get("positionId")
      if (positionIdParam && !isNaN(Number(positionIdParam))) {
        methods.setValue("jobPositionId", Number(positionIdParam), {
          shouldValidate: true,
        })
      }
    }
  }, [jobPosting, methods])

  // Show error toast if reference options fetch fails
  useEffect(() => {
    if (referencesError) {
      toast.error("Failed to load reference options")
      console.error("Error fetching reference options:", referencesError)
    }
  }, [referencesError])

  const onSubmit = async (data: TJobApplicationFormValues) => {
    try {
      setIsSubmitting(true)
      let resumeDocumentUrl = ""
      let resumeFileName = ""
      let resumeContentType = ""
      if (data.resume && data.resume.length > 0) {
        const resumeFile = data.resume[0]
        try {
          resumeFileName = resumeFile.name
          resumeContentType = resumeFile.type
          resumeDocumentUrl = await uploadFile(resumeFile, "resumes")
          toast.success("Resume uploaded successfully!")
        } catch (uploadError) {
          console.error("Error uploading resume:", uploadError)
          toast.error("Failed to upload resume. Please try again.")
          setIsSubmitting(false)
          return
        }
      } else {
        toast.error("Please upload your resume before submitting.")
        setIsSubmitting(false)
        return
      }
      const applicationData = {
        ...data,
        resume: undefined,
        jobApplicationStatusId: 1,
        totalRelevantExperienceInYears: Number(
          data.totalRelevantExperienceInYears || 0,
        ),
        assignedAdminId: 1,
        resumeDocumentUrl,
        resumeFileName,
        resumeContentType,
        jobLocations: data.jobLocation,
        referenceId:
          referenceOptions.find((opt) => opt.name === data.reference)?.id ||
          undefined,
      }
      await submitJobApplication(applicationData)

      // Send notification email (minimal details)
      await sendEmail({
        from: data.emailId,
        to: "<EMAIL>",
        subject: "New Job Application Received",
        html: getJobApplicationEmailHtml({
          name: data.name,
          emailId: data.emailId,
          phone: data.phone,
        }),
      })

      toast.success("Your application has been submitted successfully!")
      if (onSuccess) onSuccess()
    } catch (error) {
      console.error("Error submitting application:", error)
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to submit application. Please try again.",
      )
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)}>
        <div
          className={`grid ~gap-4/6 ${
            isModal
              ? "max-h-[calc(90vh-100px)] overflow-y-auto overflow-x-hidden"
              : ""
          }`}
        >
          {/* Personal Details Section */}
          <div className='rounded-lg border border-zinc-100 bg-white p-6 pb-2 shadow-sm'>
            <p className='mb-4 border-b pb-2 text-lg font-semibold text-primary'>
              Personal Details
            </p>
            <div className='grid ~gap-x-3/5 ~gap-y-2/4 sm:grid-cols-2'>
              <div>
                <InputLabel label='Full Name' id='name' required />
                <TextInputController
                  name='name'
                  placeholder='Enter Full Name'
                />
              </div>
              <div>
                <InputLabel label='Contact Number' id='phone' required />
                <TextInputController
                  name='phone'
                  placeholder='Enter Contact Number'
                  type='tel'
                />
              </div>
              <div className='sm:col-span-2'>
                <InputLabel label='Email' id='emailId' required />
                <TextInputController
                  name='emailId'
                  placeholder='Enter Email'
                  type='email'
                />
              </div>
              <div className='sm:col-span-2'>
                <InputLabel label='Address Line 1' id='addressLine1' required />
                <TextInputController
                  name='addressLine1'
                  placeholder='Enter Address Line 1'
                />
              </div>
              <div className='sm:col-span-2'>
                <InputLabel label='Address Line 2' id='addressLine2' />
                <TextInputController
                  name='addressLine2'
                  placeholder='Enter Address Line 2'
                />
              </div>
              <div>
                <InputLabel label='Landmark (Optional)' id='landmark' />
                <TextInputController
                  name='landmark'
                  placeholder='Enter Landmark'
                />
              </div>
              <div>
                <InputLabel label='Zip Code' id='zipcode' required />
                <ZipCodeAutoFill
                  name='zipcode'
                  onAddressFetched={({ city, state, country }) => {
                    methods.setValue("city", city || "", {
                      shouldValidate: true,
                    })
                    methods.setValue("state", state || "", {
                      shouldValidate: true,
                    })
                    methods.setValue("country", country || "", {
                      shouldValidate: true,
                    })
                  }}
                />
                <DisplayZipDetails />
              </div>
            </div>
          </div>

          {/* Employment Details Section */}
          <div className='rounded-lg border border-zinc-100 bg-white p-6 shadow-sm'>
            <p className='mb-4 border-b pb-2 text-lg font-semibold text-primary'>
              Employment Details
            </p>
            <div className='grid ~gap-x-3/5 ~gap-y-2/4 sm:grid-cols-2'>
              <div>
                <InputLabel
                  label='Total Relevant Experience (Years)'
                  id='totalRelevantExperienceInYears'
                />
                <NumberOnlyInputController
                  name='totalRelevantExperienceInYears'
                  placeholder='Enter Experience in Years'
                />
              </div>
              <div>
                <InputLabel
                  label='Previous Employment Duration'
                  id='previousExploymentInYears'
                />
                <TextInputController
                  name='previousExploymentInYears'
                  placeholder='e.g., 3 Years'
                />
              </div>
              <div>
                <InputLabel label='Previous Employer' id='previousEmployer' />
                <TextInputController
                  name='previousEmployer'
                  placeholder='Enter Previous Employer'
                />
              </div>
              <div>
                <InputLabel
                  label='Previous Job Position'
                  id='previousJobPosition'
                />
                <TextInputController
                  name='previousJobPosition'
                  placeholder='Enter Previous Job Position'
                />
              </div>
            </div>
          </div>

          {/* Job Details Section */}
          <div className='rounded-lg border border-zinc-100 bg-white p-6 shadow-sm'>
            <p className='mb-4 border-b pb-2 text-lg font-semibold text-primary'>
              Job Details
            </p>
            <div className='grid ~gap-x-3/5 ~gap-y-3/6 sm:grid-cols-2'>
              <div>
                <InputLabel label='Job Location' id='jobLocation' required />
                <SelectInputController
                  name='jobLocation'
                  options={jobPosting?.jobLocations.map((location, index) => ({
                    name: location,
                    id: index,
                  }))}
                  placeholder='Select Job Location'
                  valueType='name'
                />
              </div>
              <div>
                <InputLabel
                  label='Available From Date'
                  id='availableFromDate'
                  required
                />
                <DateController name='availableFromDate' label='Date' />
              </div>
              <div>
                <InputLabel label='Reference' id='reference' />
                {isLoadingReferences ? (
                  <div className='flex h-10 items-center justify-center'>
                    <Loader size={20} />
                  </div>
                ) : (
                  <SelectInputController
                    name='reference'
                    options={referenceOptions}
                    placeholder='How did you hear about us?'
                    valueType='name'
                  />
                )}
              </div>
            </div>
          </div>

          {/* Additional Information Section */}
          <div className='mb-4 rounded-lg border border-zinc-100 bg-white p-6 shadow-sm'>
            <p className='mb-4 border-b pb-2 text-lg font-semibold text-primary'>
              Additional Information
            </p>
            <div className='grid ~gap-x-3/5 ~gap-y-3/6 sm:grid-cols-2'>
              <div className='grid'>
                <InputLabel
                  label='Are you legally authorized to work in India?'
                  id='isAuthorizedToWorkInIndia'
                />
                <CheckboxInputController
                  label='Yes, I am legally authorized to work in India'
                  name='isAuthorizedToWorkInIndia'
                />
              </div>
              <div className='grid'>
                <InputLabel
                  label='Will you require visa sponsorship?'
                  id='requireVisaSponsorship'
                />
                <CheckboxInputController
                  label='Yes, I will require visa sponsorship'
                  name='requireVisaSponsorship'
                />
              </div>
              <div className='sm:col-span-2'>
                <InputLabel
                  label='Attach your resume in PDF format: (Max size: 5MB)'
                  id='resume'
                />
                <FileInputController accept='.pdf,.doc,.docx' name='resume' />
                <p className='mt-1 text-xs text-gray-500'>
                  Supported formats: PDF, DOC, DOCX (Max size: 5MB)
                </p>
              </div>
              <div className='w-full break-words sm:col-span-2'>
                <CheckboxInputController
                  label='By submitting this form, I confirm that all the information provided is accurate and complete. I understand that any false statements or omissions may disqualify me from further consideration for employment and may result in dismissal if discovered at a later date.'
                  name='isTCAccepted'
                />
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className='sticky bottom-0 z-10 bg-white p-3 shadow-sm'>
            <Button
              size='sm'
              type='submit'
              className='w-full bg-primary ~mt-6/8'
              disabled={!isValid || isSubmitting}
            >
              {isSubmitting ? "SUBMITTING..." : "SUBMIT APPLICATION"}
            </Button>
          </div>
        </div>
      </form>
    </FormProvider>
  )
}

const DisplayZipDetails = () => {
  const { watch } = useFormContext()
  const city = watch("city")
  const state = watch("state")
  const country = watch("country")
  if (!city && !state && !country) return null
  return (
    <div className='mt-1 text-xs text-gray-600'>
      {city && (
        <span>
          City: <b>{city}</b>{" "}
        </span>
      )}
      {state && (
        <span>
          State: <b>{state}</b>{" "}
        </span>
      )}
      {country && (
        <span>
          Country: <b>{country}</b>
        </span>
      )}
    </div>
  )
}

export default JobApplicationForm
