/* eslint-disable @typescript-eslint/no-explicit-any */
"use client"

import { ClockIcon } from "@/Icons"
import { InputLabel } from "@/app/shared/components"
import { sendEmail } from "@/app/shared/services/email.service"
import { HouseholdCapacity } from "@/app/shared/services/quote-calculator"
import {
  getHouseholdCapacities,
  getPackageTypes,
} from "@/app/shared/services/quote-calculator/packing-and-moving"
import { storageCalculationService } from "@/app/shared/services/quote-calculator/storage"
import { getStorageEmailHtml } from "@/app/shared/utils/email-templates"
import {
  checkForVerifiedPhone,
  getUserInfo,
} from "@/app/shared/utils/phone-verification"
import { useAuth } from "@/context"
import { useToggle } from "@/hooks"
import { calculateDistance } from "@/lib/google-map-api"
import { QUERY_KEYS } from "@/lib/react-query"
import { zodResolver } from "@hookform/resolvers/zod"
import { useQuery } from "@tanstack/react-query"
import { useEffect, useState } from "react"
import { useForm } from "react-hook-form"
import { toast } from "sonner"
import { InputAutoComplete, UserInfoModal } from "../common"
import QuoteCalculatorModal from "../common/QuoteCalculatorModal"
import { QuoteResult } from "../common/types/quote-result.types"
import { Button } from "../ui"
import { FormProvider } from "./FormProvider"
import { CheckboxInputController } from "./controllers/checkbox-field"
import { DdValueSelectInputController } from "./controllers/select-field"
import {
  NumberOnlyInputController,
  TextInputController,
} from "./controllers/text-field"
import { storageFormSchema, StorageFormValuesExtended } from "./schemas"

const serviceId = 1

const defaultValues: StorageFormValuesExtended = {
  SourceLocation: "",
  DestinationLocation: "",
  RequireInsurance: false,
  HouseHoldCapacityId: 0,
  VehicleId: 0,
  PackageTypeId: 0,
  GoodsValue: "",
  LabourCount: 0,
  LabourDays: 0,
  StorageDurationInDays: "",
  Distance: 0,
  HouseHoldCapacityName: "",
  VehicleName: "",
  PackageTypeName: "",
}

const StorageForm = () => {
  // Form setup
  const methods = useForm<StorageFormValuesExtended>({
    resolver: zodResolver(storageFormSchema),
    defaultValues,
    mode: "onChange",
    criteriaMode: "all",
  })
  const { isValid } = methods.formState
  const { user } = useAuth()

  // Location and distance state
  const [selectedSourceLocation, setSelectedSourceLocation] =
    useState<google.maps.places.PlaceResult | null>(null)
  const [selectedDestinationLocation, setSelectedDestinationLocation] =
    useState<google.maps.places.PlaceResult | null>(null)
  const [distance, setDistance] = useState<string | null>(null)
  const [distanceValue, setDistanceValue] = useState<number>(0)

  // User info modal state
  const {
    value: userInfoModalOpen,
    onOpen: openUserInfoModal,
    onClose: closeUserInfoModal,
  } = useToggle()

  // Quote calculator modal state
  const {
    value: quoteModalOpen,
    onOpen: openQuoteModal,
    onClose: closeQuoteModal,
  } = useToggle()
  const [calculationResult, setCalculationResult] =
    useState<QuoteResult | null>(null)
  const [isCalculationLoading, setIsCalculationLoading] = useState(false)

  // We don't need to track userInfo state anymore with the simplified approach

  // Data fetching
  const { data: householdCapacities, error: householdCapacitiesError } =
    useQuery({
      queryKey: [QUERY_KEYS.householdCapacities, serviceId],
      queryFn: () => getHouseholdCapacities(serviceId),
    })

  const { data: packageTypes, error: packageTypesError } = useQuery({
    queryKey: [QUERY_KEYS.packageTypes],
    queryFn: getPackageTypes,
  })

  // Derived state
  const availableVehicles = methods.watch("HouseHoldCapacityId")
    ? householdCapacities?.find(
        (capacity: HouseholdCapacity) =>
          capacity.id === methods.watch("HouseHoldCapacityId"),
      )?.allowedVehicles || []
    : []

  // Calculate distance when both locations are selected
  useEffect(() => {
    if (selectedSourceLocation && selectedDestinationLocation) {
      const getDistance = async () => {
        try {
          toast.loading("Calculating distance...", {
            id: "calculate-distance",
          })

          const distance = await calculateDistance(
            selectedSourceLocation,
            selectedDestinationLocation,
          )
          toast.dismiss("calculate-distance")

          setDistance(distance)
          const numericDistance = parseFloat(
            distance?.replace(/[^0-9.]/g, "") || "0",
          )
          setDistanceValue(numericDistance)

          if (numericDistance > 0) {
            methods.setValue(
              "SourceLocation",
              selectedSourceLocation?.formatted_address || "",
              { shouldValidate: true },
            )
            methods.setValue(
              "DestinationLocation",
              selectedDestinationLocation?.formatted_address || "",
              { shouldValidate: true },
            )
            methods.setValue("Distance", numericDistance, {
              shouldValidate: true,
            })
          }
        } catch (error) {
          console.error("Error calculating distance:", error)
          toast.error("Failed to calculate distance. Please try again.", {
            id: "calculate-distance",
          })
        }
      }
      getDistance()
    }
  }, [selectedSourceLocation, selectedDestinationLocation, methods])

  const sourceLocation = methods.watch("SourceLocation")
  const destinationLocation = methods.watch("DestinationLocation")
  // Reset distance when locations are cleared
  useEffect(() => {
    if (sourceLocation === "" || destinationLocation === "") {
      setDistance(null)
      setDistanceValue(0)
      methods.setValue("Distance", 0, { shouldValidate: false })
    }
  }, [sourceLocation, destinationLocation, methods])

  const houseHoldCapacityId = methods.watch("HouseHoldCapacityId")
  // Reset vehicle when house type changes
  useEffect(() => {
    methods.setValue("VehicleId", 0, { shouldValidate: false })
  }, [houseHoldCapacityId, methods])

  // Using imported isPhoneVerified function

  // Handle user info submission
  const handleUserInfoSubmit = (data: {
    name: string
    email: string
    phone: string
    isPhoneVerified: boolean
  }) => {
    // Close the modal and perform calculation with user info
    closeUserInfoModal()
    performCalculation({
      name: data.name,
      email: data.email,
      phone: data.phone,
    })
  }

  // Perform the actual calculation
  const performCalculation = async (userInfoData?: {
    name: string
    email: string
    phone: string
  }) => {
    openQuoteModal()
    setIsCalculationLoading(true)

    try {
      const formData = methods.getValues()
      const { ...cleanFormData } = formData
      const userInfo = userInfoData || getUserInfo()
      const goodsValue =
        typeof cleanFormData.GoodsValue === "string"
          ? cleanFormData.GoodsValue
          : String(cleanFormData.GoodsValue || "")
      const result = await storageCalculationService({
        data: {
          ...cleanFormData,
          Distance: distanceValue,
          GoodsValue: cleanFormData.RequireInsurance ? Number(goodsValue) : 0,
          LabourCount: cleanFormData.LabourCount,
          LabourDays: cleanFormData.LabourDays,
          StorageDurationInDays: Number(cleanFormData.StorageDurationInDays),
          name: userInfo?.name,
          emailId: userInfo?.email,
          phone: userInfo?.phone,
        },
        userId: user?.id || 0,
        serviceId: serviceId,
      })
      setCalculationResult(result)

      // Send email notification for new storage request via backend API
      await sendEmail({
        subject: `New Storage Request from ${userInfo?.name || "User"}`,
        html: getStorageEmailHtml({
          name: userInfo?.name || "",
          emailId: userInfo?.email || "",
          phone: userInfo?.phone || "",
          sourceLocation: cleanFormData.SourceLocation || "",
          destinationLocation: cleanFormData.DestinationLocation || "",
          distance: distanceValue || 0,
          houseHoldCapacityId: cleanFormData.HouseHoldCapacityId ?? 0,
          vehicleId: cleanFormData.VehicleId ?? 0,
          packageTypeId: cleanFormData.PackageTypeId ?? 0,
          requireInsurance: !!cleanFormData.RequireInsurance,
          goodsValue: goodsValue || "",
          labourCount: cleanFormData.LabourCount ?? 0,
          labourDays: cleanFormData.LabourDays ?? 0,
          storageDurationInDays:
            Number(cleanFormData.StorageDurationInDays) || 0,
        }),
      })
    } catch (error) {
      const errorMsg =
        error instanceof Error ? error.message : "Oh no! Something went wrong."
      toast.error(errorMsg)
      setCalculationResult(null)
    } finally {
      setIsCalculationLoading(false)
    }
  }

  // Form submission handler
  const onSubmit = async () => {
    // Check if any phone has been verified in this session
    const verifiedPhone = checkForVerifiedPhone()

    if (verifiedPhone) {
      // If any phone is verified in the session, skip the user info modal
      performCalculation()
    } else {
      // Otherwise, show the user info modal
      openUserInfoModal()
    }
  }

  return (
    <div>
      <FormProvider methods={methods} onSubmit={onSubmit}>
        <div className='grid ~gap-3/4'>
          {/* Source Location */}
          <InputAutoComplete
            onChange={(data) => setSelectedSourceLocation(data!)}
          >
            <div>
              <InputLabel label='Source Location' id='SourceLocation' />
              <TextInputController
                name='SourceLocation'
                placeholder='Enter Source Address'
                overrideValue={
                  selectedSourceLocation?.address_components?.[0]?.long_name
                }
              />
            </div>
          </InputAutoComplete>

          {/* Destination Location */}
          <InputAutoComplete
            onChange={(data) => setSelectedDestinationLocation(data!)}
          >
            <div>
              <InputLabel
                label='Destination Location'
                id='DestinationLocation'
              />
              <TextInputController
                name='DestinationLocation'
                placeholder='Enter Destination Address'
                overrideValue={
                  selectedDestinationLocation?.address_components?.[0]
                    ?.long_name
                }
              />
            </div>
          </InputAutoComplete>

          {/* Distance Display */}
          {distance && (
            <div className='col-span-full flex items-center gap-2 rounded-md bg-blue-100 text-sm font-medium text-blue-600 ~px-3/5 ~py-2/2.5'>
              <ClockIcon className='flex-shrink-0 rotate-180 text-[1.2em]' />
              <p>
                Distance between locations:{" "}
                <span className='text-base font-bold'>{distance}</span>
              </p>
            </div>
          )}

          {/* House Type Selection */}
          <div>
            <InputLabel label='House Type' id='HouseHoldCapacityId' />
            <DdValueSelectInputController
              name='HouseHoldCapacityId'
              placeholder='Choose your house capacity'
              disabled={!!householdCapacitiesError}
              options={householdCapacities ?? []}
            />
          </div>

          {/* Vehicle Selection */}
          <div>
            <InputLabel label='Select Vehicle' id='VehicleId' />
            <DdValueSelectInputController
              name='VehicleId'
              placeholder='Select an option'
              onEmptyOptionMessage="Uh-oh! there's no vehicle available, Please try different options"
              options={availableVehicles ?? []}
            />
          </div>

          {/* Package Type Selection */}
          <div>
            <InputLabel label='Package Type' id='PackageTypeId' />
            <DdValueSelectInputController
              name='PackageTypeId'
              placeholder='Choose your package type'
              disabled={!!packageTypesError}
              options={
                packageTypes?.map((item) => ({
                  id: Number(item.id),
                  name: item.name,
                })) || []
              }
            />
          </div>

          {/* Storage Duration */}
          <div>
            <InputLabel
              label='Storage Duration (days)'
              id='StorageDurationInDays'
            />
            <NumberOnlyInputController
              name='StorageDurationInDays'
              placeholder='Enter storage duration in days'
            />
          </div>

          {/* Conditional Goods Value Field */}
          {methods.watch("RequireInsurance") && (
            <div>
              <InputLabel label='Goods Value' id='GoodsValue' />
              <NumberOnlyInputController
                name='GoodsValue'
                placeholder='Enter your goods value'
              />
            </div>
          )}

          {/* Insurance Checkbox */}
          <div className='w-fit'>
            <CheckboxInputController
              name='RequireInsurance'
              label='Transit Risk'
              formControlLabelProps={{ labelPlacement: "start" }}
            />
          </div>
        </div>

        {/* Submit Button */}
        <Button
          size='sm'
          type='submit'
          className='w-full ~mt-6/8'
          disabled={!isValid}
        >
          Calculate
        </Button>
      </FormProvider>

      {/* User Info Modal */}
      <UserInfoModal
        open={userInfoModalOpen}
        onClose={closeUserInfoModal}
        onSubmit={handleUserInfoSubmit}
      />

      {/* Results Modal */}
      <QuoteCalculatorModal
        open={quoteModalOpen}
        onClose={closeQuoteModal}
        loading={isCalculationLoading}
        calculationResult={calculationResult}
        serviceName='Storage'
        fieldConfigs={{
          LabourCount: { stepValue: 1, maxValue: 20 },
          LabourDays: { stepValue: 0.5, maxValue: 10 },
          StorageDurationInDays: { stepValue: 1, maxValue: 365 },
        }}
        onFormValueChange={(field, value) => {
          // Update form values directly when changed in the details view
          methods.setValue(field as any, value, { shouldValidate: true })
        }}
        onRecalculate={(updatedFormValues) => {
          // Handle recalculation logic here
          if (updatedFormValues) {
            // Update all form values from the updated values
            Object.entries(updatedFormValues).forEach(([field, value]) => {
              if (value !== undefined) {
                methods.setValue(field as any, value, { shouldValidate: true })
              }
            })

            // Trigger recalculation
            performCalculation()
          }
        }}
      />
    </div>
  )
}

export default StorageForm
