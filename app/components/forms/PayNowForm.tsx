"use client"
import { Loader, OtpVerificationModal } from "@/app/components/common"
import {
  EmailInputController,
  NumberOnlyInputController,
  TextAreaInputController,
  TextOnlyInputController,
} from "@/app/components/forms/controllers/text-field"
import { FormProvider } from "@/app/components/forms/FormProvider"
import Button from "@/app/components/ui/Button"
import { InputLabel } from "@/app/shared/components"
import { sendOtp, verifyOtp } from "@/app/shared/services/otp/otp.service"
import { fetchPaymentDetails } from "@/app/shared/services/paynow/payment-service"
import { zodResolver } from "@hookform/resolvers/zod"
import { useEffect, useState } from "react"
import { useForm } from "react-hook-form"
import { toast } from "sonner"

// Razorpay type declaration for TS
declare global {
  interface Window {
    Razorpay?: unknown
  }
}

import { z } from "zod"

const payNowFormSchema = z.object({
  name: z.string().min(2, "Name is required"),
  phone: z
    .string()
    .min(10, "Phone is required")
    .max(10, "Phone must be 10 digits"),
  emailId: z.string().email("Invalid email address"),
  paymentId: z.string().min(1, "Payment ID is required"),
  amount: z.number().optional(),
  message: z.string().optional(),
  isPhoneVerified: z
    .boolean()
    .refine((val) => val === true, "Phone must be verified"),
})

export type PayNowFormValues = z.infer<typeof payNowFormSchema>

const defaultValues: Partial<PayNowFormValues> = {
  name: "",
  phone: "",
  emailId: "",
  paymentId: "",
  amount: undefined,
  message: "",
  isPhoneVerified: false,
}

import { useSearchParams } from "next/navigation"

const PayNowForm = () => {
  const searchParams = useSearchParams()
  const payId = searchParams?.get("pay-id")
  const [isOtpModalOpen, setIsOtpModalOpen] = useState(false)
  const [isVerifying, setIsVerifying] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const methods = useForm<PayNowFormValues>({
    defaultValues: { ...defaultValues },
    resolver: zodResolver(payNowFormSchema),
    mode: "onChange",
    criteriaMode: "all",
  })

  useEffect(() => {
    if (payId) {
      fetchPaymentDetails(payId)
        .then((details) => {
          if (details) {
            if (details.name) methods.setValue("name", String(details.name))
            if (details.email)
              methods.setValue("emailId", String(details.email))
            if (details.phone) {
              // Remove +91 if present
              let phone = String(details.phone)
              if (phone.startsWith("+91")) phone = phone.slice(3)
              methods.setValue("phone", phone)
            }
            if (details.amount)
              methods.setValue("amount", Number(details.amount))
            methods.setValue("paymentId", payId)
          }
        })
        .catch(() => {})
    } else {
      methods.setValue("amount", undefined)
    }
  }, [methods, payId])

  const { watch, setValue, formState } = methods
  const values = watch()
  const { isValid } = formState

  const handleVerifyPhone = async () => {
    const phoneValue = values.phone
    if (!phoneValue) {
      toast.error("Please enter a phone number")
      return
    }
    const phoneNumber = phoneValue.replace(/\D/g, "")
    if (phoneNumber.length !== 10) {
      toast.error("Please enter a valid 10-digit phone number")
      return
    }
    setValue("phone", phoneNumber, { shouldValidate: true })
    try {
      setIsVerifying(true)
      toast.loading("Sending OTP...", { id: "send-otp" })
      const result = await sendOtp(phoneNumber)
      toast.dismiss("send-otp")
      if (result.success) {
        setIsOtpModalOpen(true)
      } else {
        toast.error(result.message || "Failed to send OTP.", { id: "send-otp" })
      }
    } catch (error) {
      console.error("Error sending OTP:", error)
      toast.error("Failed to send OTP. Please try again.", { id: "send-otp" })
    } finally {
      setIsVerifying(false)
    }
  }

  const handleVerifyOtp = async (otp: string) => {
    let phoneNumber = values.phone
    if (!phoneNumber) {
      toast.error("Phone number is missing")
      throw new Error("Phone number is missing")
    }
    phoneNumber = phoneNumber.replace(/\D/g, "")
    if (!otp || otp.length !== 6 || !/^\d+$/.test(otp)) {
      toast.error("Please enter a valid 6-digit OTP")
      throw new Error("Invalid OTP format")
    }
    try {
      const result = await verifyOtp(phoneNumber, otp)
      if (result.isVerified) {
        setValue("isPhoneVerified", true, { shouldValidate: true })
        toast.success("Phone number verified successfully", {
          id: "verify-otp",
        })
        setIsOtpModalOpen(false)
      } else {
        toast.error(result.message || "Invalid OTP. Please try again.", {
          id: "verify-otp",
        })
        throw new Error(result.message || "Invalid OTP. Please try again.")
      }
    } catch (error) {
      console.error("Error verifying OTP:", error)
      toast.error("Failed to verify OTP. Please try again.", {
        id: "verify-otp",
      })
      throw new Error("Failed to verify OTP. Please try again.")
    }
  }

  const handleResendOtp = async () => {
    let phoneNumber = values.phone
    if (!phoneNumber) {
      toast.error("Phone number is missing")
      throw new Error("Phone number is missing")
    }
    phoneNumber = phoneNumber.replace(/\D/g, "")
    try {
      toast.loading("Sending OTP...", { id: "resend-otp" })
      const result = await sendOtp(phoneNumber)
      toast.dismiss("resend-otp")
      if (result.success) {
        toast.success("OTP resent successfully", { id: "resend-otp" })
      } else {
        toast.error(result.message || "Failed to resend OTP.", {
          id: "resend-otp",
        })
        throw new Error(result.message || "Failed to resend OTP.")
      }
    } catch (error) {
      console.error("Error resending OTP:", error)
      toast.error("Failed to resend OTP. Please try again.", {
        id: "resend-otp",
      })
      throw new Error("Failed to resend OTP. Please try again.")
    }
  }

  useEffect(() => {
    if (typeof window !== "undefined" && !window.Razorpay) {
      const script = document.createElement("script")
      script.src = "https://checkout.razorpay.com/v1/checkout.js"
      script.async = true
      document.body.appendChild(script)
    }
  }, [])

  const onSubmit = async (formValues: PayNowFormValues) => {
    if (!formValues.isPhoneVerified) {
      toast.error("Please verify your phone number before submitting")
      return
    }
    setIsSubmitting(true)
    toast.loading("Redirecting to payment...", { id: "paynow" })
    try {
      // Optionally send POST to backend to create DB entry
      // await submitPayNowForm(formValues)

      const paymentDetails = await fetchPaymentDetails(formValues.paymentId)
      toast.dismiss("paynow")
      methods.reset({ ...defaultValues })
      if (paymentDetails.short_url) {
        window.location.href = String(paymentDetails.short_url)
      } else if (paymentDetails.payment_link) {
        window.location.href = String(paymentDetails.payment_link)
      } else {
        toast.error("Payment link not found.")
      }
    } catch (error) {
      console.error("Error submitting payment:", error)
      toast.dismiss("paynow")
      toast.error(
        error instanceof Error ? error.message : "Failed to process payment",
      )
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <>
      <FormProvider methods={methods} onSubmit={onSubmit}>
        {!payId && (
          <div className='mb-4 w-full bg-yellow-50 py-3 text-center font-semibold text-yellow-800'>
            Warning: No Payment Link ID found. You cannot proceed with payment.
            Please use a valid pay-now link.
          </div>
        )}
        <div className='mx-auto grid max-w-screen-md gap-4 px-4 sm:grid-cols-2'>
          {/* Amount (only if valid and present) */}
          {payId && values.amount && values.amount > 0 && (
            <div className='sm:col-span-2'>
              <InputLabel label='Amount' id='amount' />
              <div className='flex select-none items-center rounded-lg border border-gray-200 bg-gray-50 px-4 py-3 text-2xl font-bold text-gray-800 shadow-sm'>
                ₹{" "}
                {values.amount?.toLocaleString("en-IN", {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}
              </div>
            </div>
          )}
          {/* Name */}
          <div>
            <InputLabel label='Full Name' id='name' />
            <TextOnlyInputController
              name='name'
              placeholder='Enter Full Name'
            />
          </div>
          {/* Phone */}
          <div>
            <InputLabel label='Contact Number' id='phone' />
            <div className='flex items-center gap-2'>
              <NumberOnlyInputController
                name='phone'
                placeholder='Enter Contact Number'
                disabled={values.isPhoneVerified}
                maxLength={10}
              />
              {!values.isPhoneVerified ? (
                <Button
                  size='sm'
                  onClick={handleVerifyPhone}
                  disabled={isVerifying}
                  type='button'
                  className='whitespace-nowrap !px-3 !py-1.5'
                >
                  {isVerifying ? <Loader size={15} /> : "Verify"}
                </Button>
              ) : (
                <div className='flex items-center gap-1 px-2 text-green-600'>
                  <span className='text-sm font-medium'>Verified</span>
                </div>
              )}
            </div>
          </div>
          {/* Email */}
          <div>
            <InputLabel label='Email' id='emailId' />
            <EmailInputController
              name='emailId'
              placeholder='Enter Email Address'
            />
          </div>
          {/* Message */}
          <div className='sm:col-span-2'>
            <InputLabel label='Message' id='message' />
            <TextAreaInputController
              name='message'
              placeholder='Write your message here...'
              rows={4}
              maxLength={300}
            />
          </div>
        </div>
        <div className='flex w-full flex-col items-center gap-4 px-4 pb-6 ~my-3/6'>
          <Button
            size='sm'
            type='submit'
            className='w-full font-inter uppercase md:max-w-80'
            disabled={!isValid || !values.isPhoneVerified || isSubmitting}
          >
            {isSubmitting ? <Loader size={15} /> : "Submit"}
          </Button>
        </div>
      </FormProvider>
      <OtpVerificationModal
        open={isOtpModalOpen}
        onClose={() => setIsOtpModalOpen(false)}
        phoneNumber={values.phone || ""}
        onVerify={handleVerifyOtp}
        onResendOtp={handleResendOtp}
      />
    </>
  )
}

export default PayNowForm
