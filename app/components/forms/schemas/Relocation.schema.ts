import { z } from "zod"

export const relocationFormSchema = z
  .object({
    sourceLocation: z.string().min(1, "Please enter a source location"),
    destinationLocation: z
      .string()
      .min(1, "Please enter a destination location"),

    requireInsurance: z.boolean().default(false),

    houseHoldCapacityId: z
      .number()
      .min(1, "Please select your house capacity size"),
    vehicleId: z.number().min(1, "Please choose a vehicle based on your needs"),
    packageTypeId: z.number().min(1, "Please choose a package type"),
    goodsValue: z.string().optional(),
    LabourCount: z.number().default(0),
    LabourDays: z.number().default(0),
  })
  .refine(
    (data) => {
      if (data.requireInsurance) {
        return data.goodsValue && data.goodsValue.trim().length > 0
      }
      return true
    },
    {
      message: "Please enter your goods value",
      path: ["goodsValue"],
    },
  )

export type TRelocationFormValues = z.infer<typeof relocationFormSchema>

export type TRelocationFormValuesExtended = TRelocationFormValues & {
  houseHoldCapacityName?: string
  vehicleName?: string
  packageTypeName?: string
}
