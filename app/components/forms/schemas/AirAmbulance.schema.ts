import { z } from "zod"

export const airAmbulanceFormSchema = z.object({
  // User info fields (will be filled from UserInfoModal)
  name: z.string().optional(),
  emailId: z.string().optional(),
  phone: z.string().optional(),

  // Form fields
  regionTypeId: z.number().min(1, "Please select your region"),
  sourceCity: z.string().min(1, "Please enter a source location"),
  destinationCity: z.string().min(1, "Please enter a destination location"),
  cargoWeightInKgs: z.string().min(1, "Please enter the exact weight in kg"),
})

export type TAirAmbulanceFormValues = z.infer<typeof airAmbulanceFormSchema>
