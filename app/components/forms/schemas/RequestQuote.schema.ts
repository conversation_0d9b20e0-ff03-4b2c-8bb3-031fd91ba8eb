import { z } from "zod"

export const requestQuoteFormSchema = z.object({
  name: z
    .string()
    .min(3, "Name must be at least 3 characters")
    .max(50, "Name must not exceed 50 characters")
    .refine((name) => /^[A-Za-z\s.'-]+$/.test(name), {
      message:
        "Name should only contain letters, spaces, and characters like . ' -",
    }),
  phone: z
    .string()
    .min(1, "Phone number is required")
    .refine(
      (phone) => {
        // Remove any non-digit characters and check if it's exactly 10 digits
        return phone.replace(/\D/g, "").length === 10
      },
      { message: "Please enter a valid 10-digit phone number" },
    ),
  emailId: z
    .string()
    .min(1, "Email is required")
    .email("Invalid email format")
    .max(100, "Email must not exceed 100 characters"),
  serviceId: z
    .number()
    .nullable()
    .refine((value) => value !== null, {
      message: "Please select a service type",
    }),
  serviceSubCategoryId: z
    .number()
    .nullable()
    .refine((value) => value !== null, {
      message: "Please select a service sub-category",
    }),
  message: z
    .string()
    .min(10, "Message must be at least 10 characters")
    .max(300, "Message should not exceed 300 characters"),
  preferredContactMethodId: z
    .number()
    .nullable()
    .refine((value) => value !== null, {
      message: "Please select a preferred contact method",
    }),
  origin: z.string().min(1, "Origin location is required"),
  destination: z.string().min(1, "Destination location is required"),
  distanceInKm: z
    .number()
    .min(
      0.1,
      "Distance calculation is required. Please select valid locations.",
    ),
  isPhoneVerified: z
    .boolean()
    .default(false)
    .refine((value) => value === true, {
      message: "Phone number must be verified",
    }),

  // For backward compatibility with existing form
  serviceType: z.string().optional(),
  subCategory: z.string().optional(),
  preferedContactMethod: z.string().optional(),
})

export type TRequestQuoteFormValues = z.infer<typeof requestQuoteFormSchema>
