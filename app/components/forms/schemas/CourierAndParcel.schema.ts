import { z } from "zod"

export const CourierAndParcelFormSchema = z.object({
  // User info fields (will be filled from UserInfoModal)
  name: z.string().optional(),
  emailId: z.string().optional(),
  phone: z.string().optional(),

  // Required fields from the JSON structure
  sourceLocation: z.string().min(1, "Please enter a source location"),
  destinationLocation: z.string().min(1, "Please enter a destination location"),
  documentTypeId: z.number().min(1, "Please select your document type"),
  weightInKgs: z
    .string()
    .min(1, "Please enter the exact weight of your goods in kg"),
  lengthInCms: z.string().min(1, "Please enter length of the goods in cm"),
  widthInCms: z.string().min(1, "Please enter width of the goods in cm"),
  heightInCms: z.string().min(1, "Please enter height of the goods in cm"),
  distance: z.number().optional(),
  isRiskSurchargeByCarrier: z.boolean().default(false),
  goodsValue: z.string().optional(),
})

export type TCouriercargoFormValues = z.infer<typeof CourierAndParcelFormSchema>
