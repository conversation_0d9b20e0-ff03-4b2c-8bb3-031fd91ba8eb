import { z } from "zod"

const MAX_FILE_SIZE = 5 * 1024 * 1024 // 5 MB

// Flattened schema with all fields at the root level
export const jobApplicationFormSchema = z.object({
  // Personal Details
  name: z
    .string()
    .min(1, "Name is required")
    .refine((name) => /^[A-Za-z\s.'-]+$/.test(name), {
      message:
        "Name should only contain letters, spaces, and characters like . ' -",
    }),
  phone: z
    .string()
    .min(1, "Phone is required")
    .refine(
      (phone) => {
        // Remove any non-digit characters and check if it's exactly 10 digits
        return phone.replace(/\D/g, "").length === 10
      },
      { message: "Please enter a valid 10-digit phone number" },
    ),
  emailId: z.string().min(1, "Email is required").email("Invalid email format"),
  addressLine1: z.string().min(1, "Address line 1 is required"),
  addressLine2: z.string().optional(),
  landmark: z.string().optional(),
  zipcode: z
    .string()
    .min(1, "Zip code is required")
    .length(6, "Zip Code must be 6 digits"),
  city: z.string().optional(),
  state: z.string().optional(),
  country: z.string().optional(),

  // Employment Details
  totalRelevantExperienceInYears: z
    .string()
    .min(0, "Experience cannot be negative")
    .optional(),
  previousEmployer: z.string().optional(),
  previousJobPosition: z.string().optional(),
  previousExploymentInYears: z.string().optional(),

  // Job Details
  jobPositionId: z.number().min(0, "Position ID is required"),
  jobLocation: z.string().min(1, "Job location is required"),
  availableFromDate: z.string().min(1, "Available start date is required"),
  preferredContactMethodId: z.number().default(1),
  reference: z.string().default("Website"),
  referenceId: z.number().optional(),

  // Additional Information
  isAuthorizedToWorkInIndia: z.boolean(),
  requireVisaSponsorship: z.boolean(),
  resume: z
    .custom<FileList | null>()
    .refine((files) => files && files?.length > 0, "Resume is required.")
    .refine(
      (files) => files && files?.[0]?.size <= MAX_FILE_SIZE,
      "Max file size is 5MB.",
    ),

  // Terms and Conditions
  isTCAccepted: z.literal<boolean>(true, {
    errorMap: () => ({
      message: "You must accept the terms and conditions before submitting.",
    }),
  }),
})

export type TJobApplicationFormValues = z.infer<typeof jobApplicationFormSchema>
