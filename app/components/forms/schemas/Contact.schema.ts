import { z } from "zod"

// Define a schema for DropDownValue objects
const ddValueSchema = z.object({
  id: z.number(),
  name: z.string(),
  label: z.string().optional(),
  value: z.string().optional(),
})

// Contact method options could be mapped to a number ID if needed
export const contactFormSchema = z.object({
  name: z
    .string()
    .min(3, "Name must be at least 3 characters")
    .max(50, "Name must not exceed 50 characters")
    .refine((name) => /^[A-Za-z\s.'-]+$/.test(name), {
      message:
        "Name should only contain letters, spaces, and characters like . ' -",
    }),
  phone: z.string().refine(
    (phone) => {
      // Remove any non-digit characters and check if it's exactly 10 digits
      return phone.replace(/\D/g, "").length === 10
    },
    { message: "Please enter a valid 10-digit phone number" },
  ),
  emailId: z
    .string()
    .min(1, "Email is required")
    .email("Invalid email format")
    .max(100, "Email must not exceed 100 characters"),
  subject: z.union([
    z
      .string()
      .min(1, "Subject is required")
      .max(100, "Subject must not exceed 100 characters"),
    ddValueSchema,
  ]),
  reference: z.union([
    z
      .string()
      .min(1, "Reference is required")
      .max(100, "Reference must not exceed 100 characters"),
    ddValueSchema,
  ]),
  message: z
    .string()
    .min(10, "Message must be at least 10 characters")
    .max(300, "Message should not exceed 300 characters"),
  preferredContactMethod: z
    .object({
      id: z.number(),
      name: z.string().min(1, "Preferred contact method is required"),
    })
    .nullable()
    .refine((value) => value !== null, {
      message: "Please select a preferred contact method",
    }),
  isPhoneVerified: z
    .boolean()
    .default(false)
    .refine((value) => value === true, {
      message: "Phone number must be verified",
    }),
})

export type ContactFormValues = z.infer<typeof contactFormSchema>
