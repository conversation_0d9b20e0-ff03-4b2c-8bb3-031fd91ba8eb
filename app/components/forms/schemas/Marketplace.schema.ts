import { z } from "zod"

export const marketplaceFormSchema = z.object({
  name: z
    .string()
    .min(3, "Name must be at least 3 characters")
    .max(50, "Name must not exceed 50 characters")
    .refine((name) => /^[A-Za-z\s.'-]+$/.test(name), {
      message:
        "Name should only contain letters, spaces, and characters like . ' -",
    }),
  contact: z.string().refine(
    (phone) => {
      // Remove any non-digit characters and check if it's exactly 10 digits
      return phone.replace(/\D/g, "").length === 10
    },
    { message: "Please enter a valid 10-digit contact number" },
  ),
  email: z
    .string()
    .min(1, "Email is required")
    .email("Invalid email format")
    .max(100, "Email must not exceed 100 characters"),
  category: z
    .object({
      id: z.number(),
      name: z.string().min(1, "Category is required"),
    })
    .optional()
    .refine((value) => value !== undefined, {
      message: "Please select a category",
    }),
  location: z
    .string()
    .min(1, "Location is required")
    .max(100, "Location must not exceed 100 characters"),
  isPhoneVerified: z
    .boolean()
    .default(false)
    .refine((value) => value === true, {
      message: "Contact number must be verified",
    }),
})

export type MarketplaceFormValues = z.infer<typeof marketplaceFormSchema>
