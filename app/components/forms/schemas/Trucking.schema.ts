import { z } from "zod"

export const truckingFormSchema = z
  .object({
    SourceLocation: z
      .string()
      .min(1, { message: "Please enter a source location" }),
    DestinationLocation: z
      .string()
      .min(1, { message: "Please enter a destination location" }),
    TruckingTypeId: z
      .number()
      .min(1, { message: "Please select a trucking type" }),
    VehicleId: z.number().min(1, { message: "Please select a vehicle" }),
    GoodsTypeId: z.number().min(1, { message: "Please select a goods type" }),
    LabourCount: z.number().default(0),
    LabourDays: z.number().default(0),
    Distance: z.number().default(0),
    RequireInsurance: z.boolean().default(false),
    GoodsValue: z.string().optional(),
  })
  .refine(
    (data) => {
      if (data.RequireInsurance) {
        return data.GoodsValue && data.GoodsValue.trim().length > 0
      }
      return true
    },
    {
      message: "Please enter your goods value",
      path: ["GoodsValue"],
    },
  )

export type TruckingFormValues = z.infer<typeof truckingFormSchema>

export type TruckingFormValuesExtended = TruckingFormValues & {
  TruckingTypeName?: string
  VehicleName?: string
  GoodsTypeName?: string
}
