import { z } from "zod"

export const storageFormSchema = z
  .object({
    // User info fields (will be filled from UserInfoModal)
    name: z.string().optional(),
    emailId: z.string().optional(),
    phone: z.string().optional(),

    // Form fields
    SourceLocation: z.string().min(1, "Please enter a source location"),
    DestinationLocation: z
      .string()
      .min(1, "Please enter a destination location"),
    RequireInsurance: z.boolean().default(false),
    HouseHoldCapacityId: z
      .number()
      .min(1, "Please select your house capacity size"),
    VehicleId: z.number().min(1, "Please choose a vehicle based on your needs"),
    PackageTypeId: z.number().min(1, "Please choose a package type"),
    GoodsValue: z.string().optional(),
    LabourCount: z.number().default(0),
    LabourDays: z.number().default(0),
    StorageDurationInDays: z
      .string()
      .min(1, "Please enter storage duration in days"),
    Distance: z.number().default(0),
  })
  .refine(
    (data) => {
      if (data.RequireInsurance) {
        return data.GoodsValue && data.GoodsValue.trim().length > 0
      }
      return true
    },
    {
      message: "Please enter your goods value",
      path: ["GoodsValue"],
    },
  )

export type StorageFormValues = z.infer<typeof storageFormSchema>

export type StorageFormValuesExtended = StorageFormValues & {
  HouseHoldCapacityName?: string
  VehicleName?: string
  PackageTypeName?: string
}
