import { z } from "zod"

export const userFormSchema = z.object({
  name: z.string().min(1, "Please enter your name"),
  email: z.string().min(1, "Email is required").email("Invalid email format"),
  phone: z
    .string()
    .min(1, "Phone is required")
    .refine(
      (phone) => {
        return phone.length === 10
      },
      { message: "Phone must be 10 digits" },
    ),
})

export default userFormSchema

export type TUserFormValues = z.infer<typeof userFormSchema>
