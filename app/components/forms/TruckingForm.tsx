"use client"

import { ClockIcon } from "@/Icons"
import { InputLabel } from "@/app/shared/components"
import { sendEmail } from "@/app/shared/services/email.service"
import {
  getAllTruckingVehicles,
  getGoodsTypes,
  getTruckingTypes,
  truckingCalculationService,
} from "@/app/shared/services/quote-calculator/trucking"
import { getTruckingEmailHtml } from "@/app/shared/utils/email-templates"
import {
  checkForVerifiedPhone,
  getUserInfo,
} from "@/app/shared/utils/phone-verification"
import { useAuth } from "@/context"
import { useToggle } from "@/hooks"
import { calculateDistance } from "@/lib/google-map-api"
import { QUERY_KEYS } from "@/lib/react-query"
import { zodResolver } from "@hookform/resolvers/zod"
import { useQuery } from "@tanstack/react-query"
import { useEffect, useState } from "react"
import { useForm } from "react-hook-form"
import { toast } from "sonner"
import { InputAutoComplete, UserInfoModal } from "../common"
import QuoteCalculatorModal from "../common/QuoteCalculatorModal"
import { QuoteResult } from "../common/types/quote-result.types"
import { Button } from "../ui"
import { FormProvider } from "./FormProvider"
import { CheckboxInputController } from "./controllers/checkbox-field"
import { DdValueSelectInputController } from "./controllers/select-field"
import { VehicleSelectInputController } from "./controllers/select-field/VehicleSelectInputController"
import {
  NumberOnlyInputController,
  TextInputController,
} from "./controllers/text-field"
import { truckingFormSchema, TruckingFormValuesExtended } from "./schemas"

const serviceId = 2 // Assuming 2 is the ID for Trucking service

const defaultValues: TruckingFormValuesExtended = {
  SourceLocation: "",
  DestinationLocation: "",
  TruckingTypeId: 0,
  VehicleId: 0,
  GoodsTypeId: 0,
  LabourCount: 0,
  LabourDays: 0,
  Distance: 0,
  RequireInsurance: false,
  GoodsValue: "",
  TruckingTypeName: "",
  VehicleName: "",
  GoodsTypeName: "",
}

export const TruckingForm = () => {
  // Form setup
  const methods = useForm<TruckingFormValuesExtended>({
    resolver: zodResolver(truckingFormSchema),
    defaultValues,
    mode: "onChange",
    criteriaMode: "all",
  })
  const { isValid } = methods.formState
  const { user } = useAuth()

  // Location and distance state
  type TGooglePlaceResult = google.maps.places.PlaceResult
  const [selectedSourceLocation, setSelectedSourceLocation] =
    useState<TGooglePlaceResult | null>(null)
  const [selectedDestinationLocation, setSelectedDestinationLocation] =
    useState<TGooglePlaceResult | null>(null)
  const [distance, setDistance] = useState<string | null>(null)
  // const [distanceValue, setDistanceValue] = useState<number>(0)

  // User info modal state
  const {
    value: userInfoModalOpen,
    onOpen: openUserInfoModal,
    onClose: closeUserInfoModal,
  } = useToggle()

  // Quote calculator modal state
  const {
    value: quoteModalOpen,
    onOpen: openQuoteModal,
    onClose: closeQuoteModal,
  } = useToggle()
  const [calculationResult, setCalculationResult] =
    useState<QuoteResult | null>(null)
  const [isCalculationLoading, setIsCalculationLoading] = useState(false)

  // We don't need to track userInfo state anymore with the simplified approach

  // Data fetching
  const { data: truckingTypes, error: truckingTypesError } = useQuery({
    queryKey: [QUERY_KEYS.truckingTypes],
    queryFn: getTruckingTypes,
  })

  const { data: goodsTypes, error: goodsTypesError } = useQuery({
    queryKey: [QUERY_KEYS.goodsTypes],
    queryFn: getGoodsTypes,
  })

  const { data: allVehicles, error: allVehiclesError } = useQuery({
    queryKey: [QUERY_KEYS.vehicles],
    queryFn: getAllTruckingVehicles,
  })
  // Calculate distance between source and destination locations
  useEffect(() => {
    if (selectedSourceLocation && selectedDestinationLocation) {
      const getDistance = async () => {
        const distanceResult = await calculateDistance(
          selectedSourceLocation,
          selectedDestinationLocation,
        )
        setDistance(distanceResult)

        // Extract numeric value for API
        const numericDistance = parseInt(
          distanceResult?.replace(/[^0-9]/g, "") || "0",
          10,
        )
        // setDistanceValue(numericDistance)
        methods.setValue("Distance", numericDistance, { shouldValidate: true })
      }
      getDistance()
    }
  }, [selectedSourceLocation, selectedDestinationLocation, methods])

  const sourceLocation = methods.watch("SourceLocation")
  const destinationLocation = methods.watch("DestinationLocation")
  // Reset distance when locations are cleared
  useEffect(() => {
    if (sourceLocation === "" || destinationLocation === "") {
      setDistance(null)
      methods.setValue("Distance", 0, { shouldValidate: false })
    }
  }, [sourceLocation, destinationLocation, methods])

  // Using imported isPhoneVerified function

  // Handle user info submission
  const handleUserInfoSubmit = (data: {
    name: string
    email: string
    phone: string
    isPhoneVerified: boolean
  }) => {
    // Close the modal and perform calculation with user info
    closeUserInfoModal()
    performCalculation({
      name: data.name,
      email: data.email,
      phone: data.phone,
    })
  }

  // Perform the actual calculation
  const performCalculation = async (userInfoData?: {
    name: string
    email: string
    phone: string
  }) => {
    openQuoteModal()
    setIsCalculationLoading(true)

    try {
      const formData = methods.getValues()
      const { ...cleanFormData } = formData
      const userInfo = userInfoData || getUserInfo()
      const goodsValue =
        typeof cleanFormData.GoodsValue === "string"
          ? cleanFormData.GoodsValue
          : String(cleanFormData.GoodsValue || "")
      const result = await truckingCalculationService({
        data: {
          truckingTypeId: cleanFormData.TruckingTypeId,
          vehicleId: cleanFormData.VehicleId,
          goodsTypeId: cleanFormData.GoodsTypeId,
          labourCount: cleanFormData.LabourCount,
          labourHours: cleanFormData.LabourDays,
          sourceLocation: cleanFormData.SourceLocation,
          destinationLocation: cleanFormData.DestinationLocation,
          distance: cleanFormData.Distance,
          requireInsurance: cleanFormData.RequireInsurance,
          goodsValue: cleanFormData.RequireInsurance ? Number(goodsValue) : 0,
          name: userInfo?.name,
          emailId: userInfo?.email,
          phone: userInfo?.phone,
        },
        userId: user?.id || 0,
        serviceId: serviceId,
      })
      setCalculationResult(result)

      // Send email notification for new trucking request via backend API
      await sendEmail({
        subject: `New Trucking Request from ${userInfo?.name || "User"}`,
        html: getTruckingEmailHtml({
          name: userInfo?.name || "",
          emailId: userInfo?.email || "",
          phone: userInfo?.phone || "",
          sourceLocation: cleanFormData.SourceLocation || "",
          destinationLocation: cleanFormData.DestinationLocation || "",
          distance: cleanFormData.Distance ?? 0,
          truckingTypeId: cleanFormData.TruckingTypeId ?? 0,
          vehicleId: cleanFormData.VehicleId ?? 0,
          goodsTypeId: cleanFormData.GoodsTypeId ?? 0,
          requireInsurance: !!cleanFormData.RequireInsurance,
          goodsValue: goodsValue || "",
          labourCount: cleanFormData.LabourCount ?? 0,
          labourDays: cleanFormData.LabourDays ?? 0,
        }),
      })
    } catch (error) {
      const errorMsg =
        error instanceof Error ? error.message : "Oh no! Something went wrong."
      toast.error(errorMsg)
      setCalculationResult(null)
    } finally {
      setIsCalculationLoading(false)
    }
  }

  // Form submission handler
  const onSubmit = async () => {
    // Check if any phone has been verified in this session
    const verifiedPhone = checkForVerifiedPhone()

    if (verifiedPhone) {
      // If any phone is verified in the session, skip the user info modal
      performCalculation()
    } else {
      // Otherwise, show the user info modal
      openUserInfoModal()
    }
  }

  return (
    <div>
      <FormProvider methods={methods} onSubmit={onSubmit}>
        <div className='grid ~gap-3/4'>
          {/* Source Location */}
          <InputAutoComplete
            onChange={(data) => {
              setSelectedSourceLocation(data!)
              if (data?.formatted_address) {
                methods.setValue("SourceLocation", data.formatted_address, {
                  shouldValidate: true,
                })
              }
            }}
          >
            <div>
              <InputLabel label='Source Location' id='SourceLocation' />
              <TextInputController
                name='SourceLocation'
                placeholder='Enter Source Address'
                overrideValue={selectedSourceLocation?.formatted_address}
              />
            </div>
          </InputAutoComplete>

          {/* Destination Location */}
          <InputAutoComplete
            onChange={(data) => {
              setSelectedDestinationLocation(data!)
              if (data?.formatted_address) {
                methods.setValue(
                  "DestinationLocation",
                  data.formatted_address,
                  {
                    shouldValidate: true,
                  },
                )
              }
            }}
          >
            <div>
              <InputLabel
                label='Destination Location'
                id='DestinationLocation'
              />
              <TextInputController
                name='DestinationLocation'
                placeholder='Enter Destination Address'
                overrideValue={selectedDestinationLocation?.formatted_address}
              />
            </div>
          </InputAutoComplete>

          {distance && (
            <div className='col-span-full flex items-center gap-2 rounded-md bg-blue-100 text-sm font-medium text-blue-600 ~px-3/5 ~py-2/2.5'>
              <ClockIcon className='flex-shrink-0 rotate-180 text-[1.2em]' />
              <p>
                Distance between locations:{" "}
                <span className='text-base font-bold'>{distance}</span>
              </p>
            </div>
          )}

          {/* Trucking Type Selection */}
          <div>
            <InputLabel label='Trucking Type' id='TruckingTypeId' />
            <DdValueSelectInputController
              name='TruckingTypeId'
              placeholder='Choose your trucking type'
              disabled={!!truckingTypesError}
              options={truckingTypes || []}
            />
          </div>

          {/* Vehicle Selection */}
          <div>
            <InputLabel label='Vehicle' id='VehicleId' />
            <VehicleSelectInputController
              name='VehicleId'
              options={allVehicles || []}
              placeholder='Choose a vehicle'
              disabled={!!allVehiclesError}
            />
          </div>

          {/* Goods Type Selection */}
          <div>
            <InputLabel label='Goods Type' id='GoodsTypeId' />
            <DdValueSelectInputController
              name='GoodsTypeId'
              placeholder='Choose your goods type'
              disabled={!!goodsTypesError}
              options={goodsTypes || []}
            />
          </div>

          {/* Conditional Goods Value Field */}
          {methods.watch("RequireInsurance") && (
            <div>
              <InputLabel label='Goods Value' id='GoodsValue' />
              <NumberOnlyInputController
                name='GoodsValue'
                placeholder='Enter your goods value'
              />
            </div>
          )}
          {/* Insurance Checkbox */}
          <div className='w-fit'>
            <CheckboxInputController
              name='RequireInsurance'
              label='Transit Risk'
              formControlLabelProps={{ labelPlacement: "start" }}
            />
          </div>
        </div>

        {/* Submit Button */}
        <Button
          size='sm'
          type='submit'
          className='w-full ~mt-6/8'
          disabled={!isValid}
        >
          Calculate
        </Button>
      </FormProvider>

      {/* User Info Modal */}
      <UserInfoModal
        open={userInfoModalOpen}
        onClose={closeUserInfoModal}
        onSubmit={handleUserInfoSubmit}
      />

      {/* Results Modal */}
      <QuoteCalculatorModal
        open={quoteModalOpen}
        onClose={closeQuoteModal}
        loading={isCalculationLoading}
        serviceName='Trucking'
        calculationResult={calculationResult}
        fieldConfigs={{
          LabourCount: { stepValue: 1, maxValue: 20 },
          LabourDays: { stepValue: 0.5, maxValue: 10 },
        }}
        onFormValueChange={(field, value) => {
          // Update form values directly when changed in the details view
          methods.setValue(field as keyof TruckingFormValuesExtended, value, {
            shouldValidate: true,
          })
        }}
        onRecalculate={(updatedFormValues) => {
          if (updatedFormValues) {
            // Update all form values from the updated values
            ;(
              Object.entries(updatedFormValues) as [
                keyof TruckingFormValuesExtended,
                TruckingFormValuesExtended[keyof TruckingFormValuesExtended],
              ][]
            ).forEach(([field, value]) => {
              if (value !== undefined) {
                methods.setValue(field, value, { shouldValidate: true })
              }
            })
            // Trigger recalculation
            performCalculation()
          }
        }}
      />
    </div>
  )
}

export default TruckingForm
