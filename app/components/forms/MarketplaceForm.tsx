"use client"

import { InputLabel } from "@/app/shared/components"
import { sendOtp, verifyOtp } from "@/app/shared/services/otp/otp.service"
import { DropDownValue } from "@/app/shared/types/DropDownValue"
import { ChecklistIcon } from "@/Icons"
import { zodResolver } from "@hookform/resolvers/zod"
import { useEffect, useState } from "react"
import { Controller, useForm } from "react-hook-form"
import { toast } from "sonner"
import { InputAutoComplete, Loader, OtpVerificationModal } from "../common"
import { Button } from "../ui"
import {
  EmailInputController,
  NumberOnlyInputController,
  TextInputController,
  TextOnlyInputController,
} from "./controllers/text-field"
import CustomDropdown from "./custom-dropdown/CustomDropdown"
import { FormProvider } from "./FormProvider"
import { marketplaceFormSchema, MarketplaceFormValues } from "./schemas"

// Define initial form values
const defaultValues = {
  name: "",
  contact: "",
  email: "",
  category: undefined,
  location: "",
  isPhoneVerified: false,
} satisfies Partial<MarketplaceFormValues>

const MarketplaceForm = () => {
  const [categoryData, setCategoryData] = useState<DropDownValue[]>(
    [] as DropDownValue[],
  )
  const [isOtpModalOpen, setIsOtpModalOpen] = useState(false)
  const [isVerifying, setIsVerifying] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  // const [location, setLocation] = useState<string | null>(null)

  // // Convert STATE_OPTIONS to DropDownValue format for location autocomplete
  // const locationOptions: DropDownValue[] = STATE_OPTIONS.map(
  //   (state, index) => ({
  //     id: index + 1,
  //     name: state.value,
  //   }),
  // )

  // Create form methods to be passed to FormProvider
  const methods = useForm<MarketplaceFormValues>({
    defaultValues: defaultValues,
    resolver: zodResolver(marketplaceFormSchema),
    mode: "onChange",
    criteriaMode: "all",
  })

  // Get form utilities
  const { watch, setValue, formState } = methods
  const values = watch()
  const { isValid } = formState

  // Mock category data - in real implementation, this would come from backend
  useEffect(() => {
    // Mock categories - replace with actual API call
    const mockCategories: DropDownValue[] = [
      { id: 1, name: "Moving & Relocation" },
      { id: 2, name: "Courier & Parcel" },
      { id: 3, name: "Trucking & Transportation" },
      { id: 4, name: "Air Ambulance" },
      { id: 5, name: "Storage & Warehousing" },
      { id: 6, name: "Packing Services" },
      { id: 7, name: "Logistics Solutions" },
      { id: 8, name: "Other Services" },
    ]
    setCategoryData(mockCategories)
  }, [])

  const handleVerifyPhone = async () => {
    const phoneValue = values.contact

    if (!phoneValue) {
      toast.error("Please enter a contact number")
      return
    }

    const phoneNumber = phoneValue.replace(/\D/g, "")

    if (phoneNumber.length !== 10) {
      toast.error("Please enter a valid 10-digit contact number")
      return
    }

    setValue("contact", phoneNumber, { shouldValidate: true })

    try {
      setIsVerifying(true)
      toast.loading("Sending OTP...", { id: "send-otp" })
      await sendOtp(phoneNumber)
      toast.dismiss("send-otp")
      setIsOtpModalOpen(true)
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to send OTP. Please try again."
      toast.error(errorMessage, { id: "send-otp" })
    } finally {
      setIsVerifying(false)
    }
  }

  const handleVerifyOtp = async (otp: string) => {
    let phoneNumber = values.contact

    if (!phoneNumber) {
      toast.error("Contact number is missing")
      throw new Error("Contact number is missing")
    }

    phoneNumber = phoneNumber.replace(/\D/g, "")

    if (!otp || otp.length !== 6 || !/^\d+$/.test(otp)) {
      toast.error("Please enter a valid 6-digit OTP")
      throw new Error("Invalid OTP format")
    }

    try {
      const response = await verifyOtp(phoneNumber, otp)

      if (response.isVerified) {
        setValue("isPhoneVerified", true, { shouldValidate: true })
        toast.success("Contact number verified successfully", {
          id: "verify-otp",
        })
        setIsOtpModalOpen(false)
      } else {
        toast.error(response.message || "Invalid OTP. Please try again.", {
          id: "verify-otp",
        })
        throw new Error(response.message || "Invalid OTP. Please try again.")
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to verify OTP. Please try again."
      toast.error(errorMessage, { id: "verify-otp" })
      throw new Error(errorMessage)
    }
  }

  const handleResendOtp = async () => {
    let phoneNumber = values.contact

    if (!phoneNumber) {
      toast.error("Contact number is missing")
      throw new Error("Contact number is missing")
    }

    phoneNumber = phoneNumber.replace(/\D/g, "")

    try {
      toast.loading("Sending OTP...", { id: "resend-otp" })
      await sendOtp(phoneNumber)
      toast.dismiss("resend-otp")
      toast.success("OTP resent successfully", { id: "resend-otp" })
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to resend OTP. Please try again."
      toast.error(errorMessage, { id: "resend-otp" })
      throw new Error(errorMessage)
    }
  }

  const onSubmit = async (values: MarketplaceFormValues): Promise<void> => {
    if (!values.isPhoneVerified) {
      toast.error("Please verify your contact number before submitting")
      return
    }

    if (!values.category) {
      toast.error("Please select a category")
      return
    }

    const cleanPhone = values.contact.replace(/\D/g, "")

    // Mock submission data structure - adjust based on actual API requirements
    const _postData = {
      name: values.name.trim(),
      email: values.email.trim(),
      contact: cleanPhone,
      categoryId: values.category?.id,
      categoryName: values.category?.name,
      location: values.location.trim(),
      isPhoneVerified: values.isPhoneVerified,
    }

    // TODO: Replace with actual marketplace registration API call
    // Need to implement the marketplace registration service
    // await marketplaceRegistrationService(_postData)

    try {
      setIsSubmitting(true)
      toast.loading("Submitting your marketplace registration...", {
        id: "marketplaceRequest",
      })

      // Mock API call - replace with actual marketplace registration service
      await new Promise((resolve) => setTimeout(resolve, 2000))

      methods.reset(defaultValues)

      toast.success(
        "Your marketplace registration has been submitted successfully!",
        {
          id: "marketplaceRequest",
        },
      )
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to submit marketplace registration. Please try again."
      toast.error(errorMessage, { id: "marketplaceRequest" })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <>
      <FormProvider methods={methods} onSubmit={onSubmit}>
        <div className='mx-auto grid max-w-screen-lg ~mt-3/5 ~gap-x-3/5 ~gap-y-3/6 sm:grid-cols-2'>
          <div>
            <InputLabel label='Full Name' id='name' />
            <TextOnlyInputController
              name='name'
              placeholder='Enter Full Name'
            />
          </div>

          <div>
            <InputLabel label='Contact Number' id='contact' />
            <div className='flex items-center gap-2'>
              <NumberOnlyInputController
                name='contact'
                placeholder='Enter Contact Number'
                disabled={values.isPhoneVerified}
                maxLength={10}
              />
              {!values.isPhoneVerified ? (
                <Button
                  size='sm'
                  onClick={handleVerifyPhone}
                  disabled={isVerifying}
                  type='button'
                  className='whitespace-nowrap !px-3 !py-1.5'
                >
                  {isVerifying ? <Loader size={15} /> : "Verify"}
                </Button>
              ) : (
                <div className='flex items-center gap-1 px-2 text-green-600'>
                  <ChecklistIcon className='text-lg' />
                  <span className='text-sm font-medium'>Verified</span>
                </div>
              )}
            </div>
          </div>

          <div>
            <InputLabel label='Email' id='email' />
            <EmailInputController
              name='email'
              placeholder='Enter Email Address'
            />
          </div>

          <div>
            <InputLabel label='Category' id='category' />
            <Controller
              name='category'
              render={({ field, fieldState: { error } }) => (
                <CustomDropdown
                  id='categoryId'
                  name={field.name}
                  options={categoryData}
                  value={field.value?.id?.toString() || ""}
                  handleChange={(event) => {
                    const selectedOption = categoryData.find(
                      (option) => option.id === Number(event.target.value),
                    )
                    field.onChange(selectedOption || null)
                  }}
                  handleBlur={field.onBlur}
                  error={!!error}
                  helperText={error?.message}
                />
              )}
            />
          </div>

          <div className='sm:col-span-2'>
            <InputAutoComplete
              onChange={(data) => {
                if (data) {
                  // const cityName = extractCityName(data)
                  // setLocation(cityName)
                  methods.setValue("location", data.formatted_address || "", {
                    shouldValidate: true,
                  })
                }
              }}
            >
              <div>
                <InputLabel label='Location' id='location' />
                <TextInputController
                  name='location'
                  placeholder='Enter Location'
                />
              </div>
            </InputAutoComplete>
          </div>
        </div>

        <div className='flex flex-col items-center gap-4 ~mt-7/10'>
          <div className='flex w-full justify-center gap-4'>
            <Button
              size='sm'
              type='submit'
              className='w-full max-w-80 font-inter uppercase'
              disabled={!isValid || !values.isPhoneVerified || isSubmitting}
            >
              {isSubmitting ? <Loader size={15} /> : "Submit Registration"}
            </Button>
          </div>
        </div>
      </FormProvider>

      {/* OTP Verification Modal */}
      <OtpVerificationModal
        open={isOtpModalOpen}
        onClose={() => setIsOtpModalOpen(false)}
        phoneNumber={values.contact || ""}
        onVerify={handleVerifyOtp}
        onResendOtp={handleResendOtp}
      />
    </>
  )
}

export default MarketplaceForm
