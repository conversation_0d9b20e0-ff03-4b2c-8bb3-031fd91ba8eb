"use client"

import { InputLabel } from "@/app/shared/components"
import { sendEmail } from "@/app/shared/services/email.service"
import { getDocumentTypes } from "@/app/shared/services/quote-calculator"
import { courierParcelCalculationService } from "@/app/shared/services/quote-calculator/courier-and-parcel"
import { getCourierParcelEmailHtml } from "@/app/shared/utils/email-templates"
import {
  checkForVerifiedPhone,
  getUserInfo,
} from "@/app/shared/utils/phone-verification"
import { useAuth } from "@/context"
import { useToggle } from "@/hooks"
import { ClockIcon } from "@/Icons"
import { calculateDistance } from "@/lib/google-map-api"
import { QUERY_KEYS } from "@/lib/react-query"
import { zodResolver } from "@hookform/resolvers/zod"
import { useQuery } from "@tanstack/react-query"
import { useEffect, useState } from "react"
import { Form<PERSON>rov<PERSON>, useForm } from "react-hook-form"
import { toast } from "sonner"
import { InputAutoComplete, UserInfoModal } from "../common"
import QuoteCalculatorModal from "../common/QuoteCalculatorModal"
import { QuoteResult } from "../common/types/quote-result.types"
import { Button } from "../ui"
import { CheckboxInputController } from "./controllers/checkbox-field"
import { DdValueSelectInputController } from "./controllers/select-field"
import { TextInputController } from "./controllers/text-field"
import { NumberOnlyInputController } from "./controllers/text-field/NumberOnlyInputController"
import {
  CourierAndParcelFormSchema,
  TCouriercargoFormValues,
} from "./schemas/CourierAndParcel.schema"

const defaultValues: TCouriercargoFormValues = {
  name: "",
  emailId: "",
  phone: "",
  sourceLocation: "",
  destinationLocation: "",
  documentTypeId: 0,
  widthInCms: "",
  heightInCms: "",
  lengthInCms: "",
  weightInKgs: "",
  distance: 0,
  isRiskSurchargeByCarrier: false,
  goodsValue: "",
}

// ---------------------------------------------------------------

const CourierAndParcelForm = () => {
  const methods = useForm<TCouriercargoFormValues>({
    resolver: zodResolver(CourierAndParcelFormSchema),
    defaultValues,
    mode: "onChange",
  })

  // Form state and validation
  const { watch, setValue, formState } = methods
  const { isValid } = formState
  const goodsValue = watch("goodsValue")

  // get user from context
  const { user } = useAuth()
  const {
    value: quoteModalOpen,
    onOpen: openQuoteModal,
    onClose: closeQuoteModal,
  } = useToggle()
  const {
    value: userInfoModalOpen,
    onOpen: openUserInfoModal,
    onClose: closeUserInfoModal,
  } = useToggle()

  // Location and distance state
  const [selectedSourceLocation, setSelectedSourceLocation] =
    useState<google.maps.places.PlaceResult | null>(null)
  const [selectedDestinationLocation, setSelectedDestinationLocation] =
    useState<google.maps.places.PlaceResult | null>(null)
  const [distance, setDistance] = useState<string | null>(null)
  const [distanceValue, setDistanceValue] = useState<number>(0)

  // state for calculation result
  const [calculationResult, setCalculationResult] =
    useState<QuoteResult | null>(null)
  const [isCalculationLoading, setIsCalculationLoading] = useState(false)

  // Calculate distance when both locations are selected
  useEffect(() => {
    if (selectedSourceLocation && selectedDestinationLocation) {
      const getDistance = async () => {
        try {
          toast.loading("Calculating distance...", {
            id: "calculate-distance",
          })

          const distance = await calculateDistance(
            selectedSourceLocation,
            selectedDestinationLocation,
          )
          toast.dismiss("calculate-distance")

          setDistance(distance)
          const numericDistance = parseFloat(
            distance?.replace(/[^0-9.]/g, "") || "0",
          )
          setDistanceValue(numericDistance)

          if (numericDistance > 0) {
            setValue(
              "sourceLocation",
              selectedSourceLocation?.formatted_address || "",
              {
                shouldValidate: true,
              },
            )
            setValue(
              "destinationLocation",
              selectedDestinationLocation?.formatted_address || "",
              {
                shouldValidate: true,
              },
            )
            setValue("distance", numericDistance, {
              shouldValidate: true,
            })
          }
        } catch (error) {
          console.error("Error calculating distance:", error)
          toast.error("Failed to calculate distance. Please try again.", {
            id: "calculate-distance",
          })
        }
      }
      getDistance()
    }
  }, [selectedSourceLocation, selectedDestinationLocation, setValue])

  const destinationLocation = watch("destinationLocation")
  const sourceLocation = watch("sourceLocation")

  // Reset distance when locations are cleared
  useEffect(() => {
    if (sourceLocation === "" || destinationLocation === "") {
      setDistance(null)
      setDistanceValue(0)
      setValue("distance", 0, { shouldValidate: false })
    }
  }, [destinationLocation, sourceLocation, setValue, watch])

  // Fetch document types
  const { data: documentTypes, error: documentTypesError } = useQuery({
    queryKey: [QUERY_KEYS.documentTypes],
    queryFn: getDocumentTypes,
  })

  // Handle user info submission
  const handleUserInfoSubmit = (data: {
    name: string
    email: string
    phone: string
    isPhoneVerified: boolean
  }) => {
    // Close the modal and perform calculation with user info
    closeUserInfoModal()
    performCalculation({
      name: data.name,
      email: data.email,
      phone: data.phone,
    })
  }

  // Perform the actual calculation
  const performCalculation = async (userInfoData?: {
    name: string
    email: string
    phone: string
  }) => {
    openQuoteModal()
    setIsCalculationLoading(true)

    try {
      const formData = methods.getValues()
      const userInfo = userInfoData || getUserInfo()
      const requestData = {
        sourceLocation: formData.sourceLocation,
        destinationLocation: formData.destinationLocation,
        documentTypeId: formData.documentTypeId,
        weightInKgs: Number(formData.weightInKgs),
        widthInCms: Number(formData.widthInCms),
        heightInCms: Number(formData.heightInCms),
        lengthInCms: Number(formData.lengthInCms),
        distance: distanceValue || formData.distance || 0,
        isRiskSurchargeByCarrier: formData.isRiskSurchargeByCarrier,
        goodsValue: formData.goodsValue ? Number(formData.goodsValue) : 0,
        name: userInfo?.name,
        emailId: userInfo?.email,
        phone: userInfo?.phone,
        serviceId: "Courier and Parcel",
        userId: user?.id || 0,
      }
      const result = await courierParcelCalculationService(requestData)
      setCalculationResult(result)

      // Send email notification for new courier & parcel request via backend API
      await sendEmail({
        subject: `New Courier & Parcel Request from ${userInfo?.name || "User"}`,
        html: getCourierParcelEmailHtml({
          name: userInfo?.name || "",
          emailId: userInfo?.email || "",
          phone: userInfo?.phone || "",
          sourceLocation: formData.sourceLocation || "",
          destinationLocation: formData.destinationLocation || "",
          distance: distanceValue || 0,
          documentTypeId: Number(formData.documentTypeId) || 0,
          weightInKgs: Number(formData.weightInKgs) || 0,
          widthInCms: Number(formData.widthInCms) || 0,
          heightInCms: Number(formData.heightInCms) || 0,
          lengthInCms: Number(formData.lengthInCms) || 0,
          isRiskSurchargeByCarrier: !!formData.isRiskSurchargeByCarrier,
          goodsValue: Number(formData.goodsValue) || 0,
        }),
      })
    } catch (error) {
      const errorMsg =
        error instanceof Error ? error.message : "Oh no! Something went wrong."
      toast.error(errorMsg)
      setCalculationResult(null)
    } finally {
      setIsCalculationLoading(false)
    }
  }

  // handle form submission
  const onSubmit = () => {
    // Check if any phone has been verified in this session
    const verifiedPhone = checkForVerifiedPhone()

    if (verifiedPhone) {
      // If any phone is verified in the session, skip the user info modal
      performCalculation()
    } else {
      // Otherwise, show the user info modal
      openUserInfoModal()
    }
  }

  return (
    <div>
      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(onSubmit)}>
          <div className='grid ~gap-3/4'>
            <div>
              <InputLabel label='Source Location' id='sourceLocation' />
              <InputAutoComplete
                onChange={(data) => {
                  if (data) {
                    setSelectedSourceLocation(data)
                    setValue("sourceLocation", data.formatted_address || "")
                  }
                }}
              >
                <TextInputController
                  name='sourceLocation'
                  placeholder='Enter Source Location'
                />
              </InputAutoComplete>
            </div>

            <div>
              <InputLabel
                label='Destination Location'
                id='destinationLocation'
              />
              <InputAutoComplete
                onChange={(data) => {
                  if (data) {
                    setSelectedDestinationLocation(data)
                    setValue(
                      "destinationLocation",
                      data.formatted_address || "",
                    )
                  }
                }}
              >
                <TextInputController
                  name='destinationLocation'
                  placeholder='Enter Destination Location'
                />
              </InputAutoComplete>
            </div>

            {/* Distance Display */}
            {distance && (
              <div className='col-span-full flex items-center gap-2 rounded-md bg-blue-100 text-sm font-medium text-blue-600 ~px-3/5 ~py-2/2.5'>
                <ClockIcon className='flex-shrink-0 rotate-180 text-[1.2em]' />
                <p>
                  Distance between locations:{" "}
                  <span className='text-base font-bold'>{distance}</span>
                </p>
              </div>
            )}

            <div>
              <InputLabel label='Document Type' id='documentTypeId' />
              <DdValueSelectInputController
                name='documentTypeId'
                placeholder='Choose document type'
                disabled={!!documentTypesError}
                options={documentTypes ?? []}
              />
            </div>

            <div>
              <InputLabel label='Weight (kg)' id='weightInKgs' />
              <NumberOnlyInputController
                name='weightInKgs'
                placeholder='Enter weight in kg'
              />
            </div>

            <div className='grid grid-cols-3 gap-4'>
              <div>
                <InputLabel label='Length (cm)' id='lengthInCms' />
                <NumberOnlyInputController
                  name='lengthInCms'
                  placeholder='Enter length'
                />
              </div>
              <div>
                <InputLabel label='Width (cm)' id='widthInCms' />
                <NumberOnlyInputController
                  name='widthInCms'
                  placeholder='Enter width'
                />
              </div>
              <div>
                <InputLabel label='Height (cm)' id='heightInCms' />
                <NumberOnlyInputController
                  name='heightInCms'
                  placeholder='Enter height'
                />
              </div>
            </div>

            <div>
              <InputLabel label='Goods Value' id='goodsValue' />
              <NumberOnlyInputController
                name='goodsValue'
                placeholder='Enter goods value'
              />
            </div>

            {/* Show risk surcharge checkbox only if goods value > 50000 */}
            {Number(goodsValue) > 50000 && (
              <div className='mt-2'>
                <CheckboxInputController
                  name='isRiskSurchargeByCarrier'
                  label='Risk surcharge (risk by carrier)'
                />
                <p className='mt-1 text-sm text-gray-500'>
                  {methods.watch("isRiskSurchargeByCarrier")
                    ? "The carrier will take responsibility for the risk."
                    : "You will be responsible for any risk during transportation."}
                </p>
              </div>
            )}
          </div>

          <div>
            <Button
              size='sm'
              type='submit'
              className='w-full ~mt-6/8'
              disabled={!isValid}
            >
              Calculate
            </Button>
          </div>
        </form>
      </FormProvider>

      {/* User Info Modal */}
      <UserInfoModal
        open={userInfoModalOpen}
        onClose={closeUserInfoModal}
        onSubmit={handleUserInfoSubmit}
      />

      {/* Quote Calculator Modal */}
      <QuoteCalculatorModal
        open={quoteModalOpen}
        onClose={closeQuoteModal}
        loading={isCalculationLoading}
        calculationResult={calculationResult}
        serviceName='Courier and Parcel'
      />
    </div>
  )
}

export default CourierAndParcelForm
