/* eslint-disable @typescript-eslint/no-explicit-any */
"use client"

import { InputLabel } from "@/app/shared/components"
import {
  getHouseholdCapacities,
  getPackageTypes,
  packingAndMovingCalculationService,
} from "@/app/shared/services/quote-calculator/packing-and-moving"
import { useAuth } from "@/context"
import { useToggle } from "@/hooks"
import { calculateDistance } from "@/lib/google-map-api"
import { QUERY_KEYS } from "@/lib/react-query"
import { zodResolver } from "@hookform/resolvers/zod"
import { useQuery } from "@tanstack/react-query"
import { useEffect, useState } from "react"
import { useForm } from "react-hook-form"
import { toast } from "sonner"
import { InputAutoComplete, UserInfoModal } from "../common"
import QuoteCalculatorModal from "../common/QuoteCalculatorModal"
import { Button } from "../ui"
import { CheckboxInputController } from "./controllers/checkbox-field"
import { DdValueSelectInputController } from "./controllers/select-field"
import {
  NumberOnlyInputController,
  TextInputController,
} from "./controllers/text-field"
import { FormProvider } from "./FormProvider"
import { relocationFormSchema, TRelocationFormValuesExtended } from "./schemas"

import { sendEmail } from "@/app/shared/services/email.service"
import { HouseholdCapacity } from "@/app/shared/services/quote-calculator"
import { getPackingAndMovingEmailHtml } from "@/app/shared/utils/email-templates"
import {
  checkForVerifiedPhone,
  getUserInfo,
} from "@/app/shared/utils/phone-verification"
import { ClockIcon } from "@/Icons"
import { QuoteResult } from "../common/types/quote-result.types"

const serviceId = 1

const defaultValues: TRelocationFormValuesExtended = {
  sourceLocation: "",
  destinationLocation: "",
  requireInsurance: false,
  houseHoldCapacityId: 0,
  vehicleId: 0,
  packageTypeId: 0,
  goodsValue: "",
  LabourCount: 0,
  LabourDays: 0,
}

const PackingAndMovingForm = () => {
  // Form setup
  const methods = useForm<TRelocationFormValuesExtended>({
    resolver: zodResolver(relocationFormSchema),
    defaultValues,
    mode: "onChange",
    criteriaMode: "all",
  })
  const { isValid } = methods.formState
  const { user } = useAuth()

  // Location and distance state
  const [selectedSourceLocation, setSelectedSourceLocation] =
    useState<google.maps.places.PlaceResult | null>(null)
  const [selectedDestinationLocation, setSelectedDestinationLocation] =
    useState<google.maps.places.PlaceResult | null>(null)
  const [distance, setDistance] = useState<string | null>(null)
  const [distanceValue, setDistanceValue] = useState<number>(0)

  // User info modal state
  const {
    value: userInfoModalOpen,
    onOpen: openUserInfoModal,
    onClose: closeUserInfoModal,
  } = useToggle()

  // Quote calculator modal state
  const {
    value: quoteModalOpen,
    onOpen: openQuoteModal,
    onClose: closeQuoteModal,
  } = useToggle()
  const [calculationResult, setCalculationResult] =
    useState<QuoteResult | null>(null)
  const [isCalculationLoading, setIsCalculationLoading] = useState(false)

  // We don't need to track userInfo state anymore with the simplified approach

  // Data fetching
  const { data: householdCapacities, error: householdCapacitiesError } =
    useQuery({
      queryKey: [QUERY_KEYS.householdCapacities, serviceId],
      queryFn: () => getHouseholdCapacities(serviceId),
    })

  const { data: packageTypes, error: packageTypesError } = useQuery({
    queryKey: [QUERY_KEYS.packageTypes],
    queryFn: getPackageTypes,
  })

  // Derived state
  const availableVehicles = methods.watch("houseHoldCapacityId")
    ? householdCapacities?.find(
        (capacity: HouseholdCapacity) =>
          capacity.id === methods.watch("houseHoldCapacityId"),
      )?.allowedVehicles || []
    : []

  // Calculate distance when both locations are selected
  useEffect(() => {
    if (selectedSourceLocation && selectedDestinationLocation) {
      const getDistance = async () => {
        try {
          toast.loading("Calculating distance...", {
            id: "calculate-distance",
          })

          const distance = await calculateDistance(
            selectedSourceLocation,
            selectedDestinationLocation,
          )
          toast.dismiss("calculate-distance")

          setDistance(distance)
          const numericDistance = parseFloat(
            distance?.replace(/[^0-9.]/g, "") || "0",
          )
          setDistanceValue(numericDistance)

          if (numericDistance > 0) {
            methods.setValue(
              "sourceLocation",
              selectedSourceLocation?.formatted_address || "",
              { shouldValidate: true },
            )
            methods.setValue(
              "destinationLocation",
              selectedDestinationLocation?.formatted_address || "",
              { shouldValidate: true },
            )
          }
        } catch (error) {
          console.error("Error calculating distance:", error)
          toast.error("Failed to calculate distance. Please try again.", {
            id: "calculate-distance",
          })
        }
      }
      getDistance()
    }
  }, [selectedSourceLocation, selectedDestinationLocation, methods])

  const sourceLocation = methods.watch("sourceLocation")
  const destinationLocation = methods.watch("destinationLocation")
  const houseHoldCapacityId = methods.watch("houseHoldCapacityId")

  // Reset distance when locations are cleared
  useEffect(() => {
    if (sourceLocation === "" || destinationLocation === "") {
      setDistance(null)
      setDistanceValue(0)
    }
  }, [sourceLocation, destinationLocation])

  // Reset vehicle when house type changes
  useEffect(() => {
    methods.setValue("vehicleId", 0, { shouldValidate: false })
  }, [houseHoldCapacityId, methods])

  // Using imported isPhoneVerified function

  // Handle user info submission
  const handleUserInfoSubmit = (data: {
    name: string
    email: string
    phone: string
    isPhoneVerified: boolean
  }) => {
    // Close the modal and perform calculation with user info
    closeUserInfoModal()
    performCalculation({
      name: data.name,
      email: data.email,
      phone: data.phone,
    })
  }

  // Perform the actual calculation
  const performCalculation = async (userInfoData?: {
    name: string
    email: string
    phone: string
  }) => {
    openQuoteModal()
    setIsCalculationLoading(true)

    try {
      const formData = methods.getValues()
      const { ...cleanFormData } = formData
      const userInfo = userInfoData || getUserInfo()
      const result = await packingAndMovingCalculationService({
        data: {
          ...cleanFormData,
          distance: distanceValue,
          name: userInfo?.name,
          emailId: userInfo?.email,
          phone: userInfo?.phone,
        },
        userId: user?.id || 0,
        serviceId: serviceId,
      })
      setCalculationResult(result)

      // Send email notification for new packing and moving request via backend API
      await sendEmail({
        subject: `New Packing & Moving Request from ${userInfo?.name || "User"}`,
        html: getPackingAndMovingEmailHtml({
          name: userInfo?.name || "",
          emailId: userInfo?.email || "",
          phone: userInfo?.phone || "",
          sourceLocation: cleanFormData.sourceLocation || "",
          destinationLocation: cleanFormData.destinationLocation || "",
          distance: distanceValue,
          houseHoldCapacityId: cleanFormData.houseHoldCapacityId ?? 0,
          vehicleId: cleanFormData.vehicleId ?? 0,
          packageTypeId: cleanFormData.packageTypeId ?? 0,
          requireInsurance: !!cleanFormData.requireInsurance,
          goodsValue: cleanFormData.goodsValue || "",
          LabourCount: cleanFormData.LabourCount ?? 0,
          LabourDays: cleanFormData.LabourDays ?? 0,
        }),
      })
    } catch (error) {
      const errorMsg =
        error instanceof Error ? error.message : "Oh no! Something went wrong."
      toast.error(errorMsg)
      setCalculationResult(null)
    } finally {
      setIsCalculationLoading(false)
    }
  }

  // Form submission handler
  const onSubmit = async () => {
    // Check if any phone has been verified in this session
    const verifiedPhone = checkForVerifiedPhone()

    if (verifiedPhone) {
      // If any phone is verified in the session, skip the user info modal
      performCalculation()
    } else {
      // Otherwise, show the user info modal
      openUserInfoModal()
    }
  }

  return (
    <div>
      <FormProvider methods={methods} onSubmit={onSubmit}>
        <div className='grid ~gap-3/4'>
          {/* Source Location */}
          <InputAutoComplete
            onChange={(data) => setSelectedSourceLocation(data!)}
          >
            <div>
              <InputLabel label='Source Location' id='sourceLocation' />
              <TextInputController
                name='sourceLocation'
                placeholder='Enter Source Address'
                overrideValue={
                  selectedSourceLocation?.address_components?.[0]?.long_name
                }
              />
            </div>
          </InputAutoComplete>

          {/* Destination Location */}
          <InputAutoComplete
            onChange={(data) => setSelectedDestinationLocation(data!)}
          >
            <div>
              <InputLabel
                label='Destination Location'
                id='destinationLocation'
              />
              <TextInputController
                name='destinationLocation'
                placeholder='Enter Destination Address'
                overrideValue={
                  selectedDestinationLocation?.address_components?.[0]
                    ?.long_name
                }
              />
            </div>
          </InputAutoComplete>

          {/* Distance Display */}
          {distance && (
            <div className='col-span-full flex items-center gap-2 rounded-md bg-blue-100 text-sm font-medium text-blue-600 ~px-3/5 ~py-2/2.5'>
              <ClockIcon className='flex-shrink-0 rotate-180 text-[1.2em]' />
              <p>
                Distance between locations:{" "}
                <span className='text-base font-bold'>{distance}</span>
              </p>
            </div>
          )}

          {/* House Type Selection */}
          <div>
            <InputLabel label='House Type' id='houseHoldCapacityId' />
            <DdValueSelectInputController
              name='houseHoldCapacityId'
              placeholder='Choose your house capacity'
              disabled={!!householdCapacitiesError}
              options={householdCapacities ?? []}
            />
          </div>

          {/* Vehicle Selection */}
          <div>
            <InputLabel label='Select Vehicle' id='vehicleId' />
            <DdValueSelectInputController
              name='vehicleId'
              placeholder='Select an option'
              onEmptyOptionMessage="Uh-oh! there's no vehicle available, Please try different options"
              options={availableVehicles ?? []}
            />
          </div>

          {/* Package Type Selection */}
          <div>
            <InputLabel label='Package Type' id='packageTypeId' />
            <DdValueSelectInputController
              name='packageTypeId'
              placeholder='Choose your package type'
              disabled={!!packageTypesError}
              options={
                packageTypes?.map((item) => ({
                  id: Number(item.id),
                  name: item.name,
                })) || []
              }
            />
          </div>

          {/* Conditional Goods Value Field */}
          {methods.watch("requireInsurance") && (
            <div>
              <InputLabel label='Goods Value' id='goodsValue' />
              <NumberOnlyInputController
                name='goodsValue'
                placeholder='Enter your goods value'
              />
            </div>
          )}

          {/* Insurance Checkbox */}
          <div className='w-fit'>
            <CheckboxInputController
              name='requireInsurance'
              label='Transit Risk'
              formControlLabelProps={{ labelPlacement: "start" }}
            />
          </div>
        </div>

        {/* Submit Button */}
        <Button
          size='sm'
          type='submit'
          className='w-full ~mt-6/8'
          disabled={!isValid}
        >
          Calculate
        </Button>
      </FormProvider>

      {/* User Info Modal */}
      <UserInfoModal
        open={userInfoModalOpen}
        onClose={closeUserInfoModal}
        onSubmit={handleUserInfoSubmit}
      />

      {/* Results Modal */}
      <QuoteCalculatorModal
        open={quoteModalOpen}
        onClose={closeQuoteModal}
        loading={isCalculationLoading}
        calculationResult={calculationResult}
        fieldConfigs={{
          LabourCount: { stepValue: 1, maxValue: 20 },
          LabourDays: { stepValue: 0.5, maxValue: 10 },
        }}
        serviceName={"Packing And Moving"}
        onFormValueChange={(field, value) => {
          // Update form values directly when changed in the details view
          methods.setValue(field as any, value, { shouldValidate: true })
        }}
        onRecalculate={(updatedFormValues) => {
          // Handle recalculation logic here

          if (updatedFormValues) {
            // Update all form values from the updated values
            Object.entries(updatedFormValues).forEach(([field, value]) => {
              if (value !== undefined) {
                methods.setValue(field as any, value, { shouldValidate: true })
              }
            })

            // Trigger recalculation
            performCalculation()
          }
        }}
      />
    </div>
  )
}

export default PackingAndMovingForm
