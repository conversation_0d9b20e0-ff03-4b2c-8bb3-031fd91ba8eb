"use client"

import { InputLabel } from "@/app/shared/components"
import {
  addContactRequest,
  getContactMethods,
  getR<PERSON><PERSON><PERSON>,
  getSubject,
} from "@/app/shared/services/contact-request/contact-request.service"
import { ContactRequest } from "@/app/shared/services/contact-request/contact-request.type"
import { sendEmail } from "@/app/shared/services/email.service"
import { sendOtp, verifyOtp } from "@/app/shared/services/otp/otp.service"
import { DropDownValue } from "@/app/shared/types/DropDownValue"
import { getContactEmailHtml } from "@/app/shared/utils/email-templates"
import { ChecklistIcon } from "@/Icons"
import { zodResolver } from "@hookform/resolvers/zod"
import { useEffect, useState } from "react"
import { Controller, useForm } from "react-hook-form"
import { toast } from "sonner"
import { Loader, OtpVerificationModal } from "../common"
import { Button } from "../ui"
import { AutocompleteInputController } from "./controllers/autocomplete-field"
import {
  EmailInputController,
  NumberOnlyInputController,
  TextAreaInputController,
  TextOnlyInputController,
} from "./controllers/text-field"
import CustomDropdown from "./custom-dropdown/CustomDropdown"
import { FormProvider } from "./FormProvider"
import { contactFormSchema, ContactFormValues } from "./schemas"

// Define initial form values
const defaultValues: Partial<ContactFormValues> = {
  name: "",
  phone: "",
  emailId: "",
  subject: "",
  reference: "",
  message: "",
  preferredContactMethod: undefined, // Use undefined to match type
  isPhoneVerified: false,
}

const ContactForm = () => {
  const [subjectData, setSubjectData] = useState<DropDownValue[]>(
    [] as DropDownValue[],
  )
  const [referenceData, setReferenceData] = useState<DropDownValue[]>(
    [] as DropDownValue[],
  )
  const [contactMethodData, setContactMethodData] = useState<DropDownValue[]>(
    [] as DropDownValue[],
  )
  const [isOtpModalOpen, setIsOtpModalOpen] = useState(false)
  const [isVerifying, setIsVerifying] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  // We don't need requestId with the phone-verification service

  // Create form methods to be passed to FormProvider
  const methods = useForm<ContactFormValues>({
    defaultValues: defaultValues as ContactFormValues,
    resolver: zodResolver(contactFormSchema),
    mode: "onChange",
    criteriaMode: "all", // Show all validation errors
  })

  // Get form utilities
  const { watch, setValue, formState } = methods
  const values = watch()
  const { isValid } = formState

  // Watch form values for reactive updates

  useEffect(() => {
    Promise.all([getSubject(), getRefernce(), getContactMethods()])
      .then(([subjectData, referenceData, contactMethodsData]) => {
        setSubjectData(subjectData)
        setReferenceData(referenceData)
        setContactMethodData(contactMethodsData)
      })
      .catch(() => {
        // Silent fail - we'll handle this with empty state UI
      })
      .finally(() => {})
  }, [])

  const handleVerifyPhone = async () => {
    const phoneValue = values.phone
    if (!phoneValue) {
      toast.error("Please enter a phone number")
      return
    }
    const phoneNumber = phoneValue.replace(/\D/g, "")
    if (phoneNumber.length !== 10) {
      toast.error("Please enter a valid 10-digit phone number")
      return
    }
    setValue("phone", phoneNumber, { shouldValidate: true })
    try {
      setIsVerifying(true)
      toast.loading("Sending OTP...", { id: "send-otp" })
      const result = await sendOtp(phoneNumber)
      toast.dismiss("send-otp")
      if (result.success) {
        setIsOtpModalOpen(true)
      } else {
        toast.error(result.message || "Failed to send OTP.", { id: "send-otp" })
      }
    } catch (error) {
      console.error("Error sending OTP:", error)
      toast.error("Failed to send OTP. Please try again.", { id: "send-otp" })
    } finally {
      setIsVerifying(false)
    }
  }

  const handleVerifyOtp = async (otp: string) => {
    let phoneNumber = values.phone
    if (!phoneNumber) {
      toast.error("Phone number is missing")
      throw new Error("Phone number is missing")
    }
    phoneNumber = phoneNumber.replace(/\D/g, "")
    if (!otp || otp.length !== 6 || !/^\d+$/.test(otp)) {
      toast.error("Please enter a valid 6-digit OTP")
      throw new Error("Invalid OTP format")
    }
    try {
      const result = await verifyOtp(phoneNumber, otp)
      if (result.isVerified) {
        setValue("isPhoneVerified", true, { shouldValidate: true })
        toast.success("Phone number verified successfully", {
          id: "verify-otp",
        })
        setIsOtpModalOpen(false)
      } else {
        toast.error(result.message || "Invalid OTP. Please try again.", {
          id: "verify-otp",
        })
        throw new Error(result.message || "Invalid OTP. Please try again.")
      }
    } catch (error) {
      console.error("Error verifying OTP:", error)
      toast.error("Failed to verify OTP. Please try again.", {
        id: "verify-otp",
      })
      throw new Error("Failed to verify OTP. Please try again.")
    }
  }

  const handleResendOtp = async () => {
    let phoneNumber = values.phone
    if (!phoneNumber) {
      toast.error("Phone number is missing")
      throw new Error("Phone number is missing")
    }
    phoneNumber = phoneNumber.replace(/\D/g, "")
    try {
      toast.loading("Sending OTP...", { id: "resend-otp" })
      const result = await sendOtp(phoneNumber)
      toast.dismiss("resend-otp")
      if (result.success) {
        toast.success("OTP resent successfully", { id: "resend-otp" })
      } else {
        toast.error(result.message || "Failed to resend OTP.", {
          id: "resend-otp",
        })
        throw new Error(result.message || "Failed to resend OTP.")
      }
    } catch (error) {
      console.error("Error resending OTP:", error)
      toast.error("Failed to resend OTP. Please try again.", {
        id: "resend-otp",
      })
      throw new Error("Failed to resend OTP. Please try again.")
    }
  }

  const onSubmit = async (values: ContactFormValues): Promise<void> => {
    // Validate phone verification
    if (!values.isPhoneVerified) {
      toast.error("Please verify your phone number before submitting")
      return
    }

    // Get subject and reference values
    const subject =
      typeof values.subject === "object" ? values.subject.name : values.subject

    const reference =
      typeof values.reference === "object"
        ? values.reference.name
        : values.reference

    // Validate preferred contact method
    if (!values.preferredContactMethod) {
      toast.error("Please select a preferred contact method")
      return
    }

    // Clean phone number
    const cleanPhone = values.phone.replace(/\D/g, "")

    const postData: ContactRequest = {
      name: values.name.trim(),
      emailId: values.emailId.trim(),
      phone: cleanPhone,
      preferredContactMethodId: values.preferredContactMethod.id,
      statusId: 1,
      subject: subject,
      reference: reference,
      message: values.message.trim(),
      isPhoneVerified: values.isPhoneVerified,
    }

    try {
      setIsSubmitting(true)
      toast.loading("Submitting your request...", {
        id: "contactRequest",
      })

      await addContactRequest(postData)

      // Send email with contact details via backend API
      await sendEmail({
        subject: `New Contact Request from ${postData.name}`,
        html: getContactEmailHtml({
          name: postData.name,
          emailId: postData.emailId,
          phone: postData.phone,
          subject: postData.subject,
          reference: postData.reference,
          message: postData.message,
          preferredContactMethod: values.preferredContactMethod?.name || "",
        }),
      })

      // Reset form with default values
      methods.reset(defaultValues as ContactFormValues)

      // Show success message
      toast.success("Your contact request has been submitted successfully!", {
        id: "contactRequest",
      })
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to submit contact request. Please try again."
      toast.error(errorMessage, { id: "contactRequest" })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <>
      <FormProvider methods={methods} onSubmit={onSubmit}>
        <div className='mx-auto grid max-w-screen-lg ~mt-3/5 ~gap-x-3/5 ~gap-y-3/6 sm:grid-cols-2'>
          <div>
            <InputLabel label='Full Name' id='name' />
            <TextOnlyInputController
              name='name'
              placeholder='Enter Full Name'
            />
          </div>
          <div>
            <InputLabel label='Contact Number' id='phone' />
            <div className='flex items-start gap-2'>
              <NumberOnlyInputController
                name='phone'
                placeholder='Enter Contact Number'
                disabled={values.isPhoneVerified}
                maxLength={10}
              />
              {!values.isPhoneVerified ? (
                <Button
                  size='sm'
                  onClick={handleVerifyPhone}
                  disabled={isVerifying}
                  type='button'
                  className='whitespace-nowrap !px-3 !py-1.5'
                >
                  {isVerifying ? <Loader size={15} /> : "Verify"}
                </Button>
              ) : (
                <div className='flex items-center gap-1 px-2 text-green-600'>
                  <ChecklistIcon className='text-lg' />
                  <span className='text-sm font-medium'>Verified</span>
                </div>
              )}
            </div>
          </div>

          <div>
            <InputLabel label='Email' id='emailId' />
            <EmailInputController
              name='emailId'
              placeholder='Enter Email Address'
            />
          </div>
          <div>
            <InputLabel label='Subject' id='subject' />
            <AutocompleteInputController
              name='subject'
              placeholder='Enter or select a subject'
              options={subjectData}
            />
          </div>

          <div className='sm:col-span-2'>
            <InputLabel label='Message' id='message' />
            <TextAreaInputController
              name='message'
              placeholder='Write your message here...'
              rows={4}
              maxLength={300}
            />
          </div>

          <div>
            <InputLabel
              label='Preferred Contact Method'
              id='preferredContactMethod'
            />
            <Controller
              name='preferredContactMethod'
              render={({ field, fieldState: { error } }) => (
                <CustomDropdown
                  id='preferredContactMethodId'
                  name={field.name}
                  options={contactMethodData}
                  value={field.value?.id || ""}
                  handleChange={(event) => {
                    const selectedOption = contactMethodData.find(
                      (option) => option.id === Number(event.target.value),
                    )
                    field.onChange(selectedOption || null)
                  }}
                  handleBlur={field.onBlur}
                  error={!!error}
                  helperText={error?.message}
                />
              )}
            />
          </div>

          <div>
            <InputLabel label='Reference' id='reference' />
            <AutocompleteInputController
              name='reference'
              placeholder='Enter or select a reference'
              options={referenceData}
            />
          </div>
        </div>
        <div className='flex flex-col items-center gap-4 ~mt-7/10'>
          <div className='flex w-full justify-center gap-4'>
            <Button
              size='sm'
              type='submit'
              className='w-full max-w-80 font-inter uppercase'
              disabled={!isValid || !values.isPhoneVerified || isSubmitting}
            >
              {isSubmitting ? <Loader size={15} /> : "Submit"}
            </Button>
          </div>
        </div>
      </FormProvider>

      {/* OTP Verification Modal */}
      <OtpVerificationModal
        open={isOtpModalOpen}
        onClose={() => setIsOtpModalOpen(false)}
        phoneNumber={values.phone || ""}
        onVerify={handleVerifyOtp}
        onResendOtp={handleResendOtp}
      />
    </>
  )
}

export default ContactForm
