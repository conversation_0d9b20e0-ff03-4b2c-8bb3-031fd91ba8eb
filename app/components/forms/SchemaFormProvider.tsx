/* eslint-disable @typescript-eslint/no-explicit-any */
"use client"

import { zodResolver } from "@hookform/resolvers/zod"
import { ReactNode } from "react"
import {
  FieldValues,
  FormProvider as RHFormProvider,
  useForm,
  UseFormProps,
} from "react-hook-form"
import { ZodSchema } from "zod"

// Interface for SchemaFormProvider that accepts schema and defaultValues
interface SchemaFormProviderProps<T extends FieldValues> {
  schema: ZodSchema<any>
  defaultValues: Partial<T>
  onSubmit: (data: T) => void | Promise<void>
  children: ReactNode
  mode?: "onChange" | "onBlur" | "onSubmit" | "onTouched" | "all"
  formOptions?: Omit<UseFormProps<T>, "resolver" | "defaultValues" | "mode">
  className?: string
}

export const SchemaFormProvider = <T extends FieldValues>({
  schema,
  defaultValues,
  onSubmit,
  children,
  mode = "onChange",
  formOptions = {},
  className,
}: SchemaFormProviderProps<T>) => {
  // Initialize form with react-hook-form
  const methods = useForm<T>({
    resolver: zodResolver(schema),
    defaultValues: defaultValues as any,
    mode,
    ...formOptions,
  })

  // Handle both sync and async submissions
  const handleSubmit = async (data: any) => {
    try {
      await onSubmit(data as T)
    } catch (error) {
      console.error("Form submission error:", error)
    }
  }

  return (
    <RHFormProvider {...methods}>
      <form
        onSubmit={methods.handleSubmit(handleSubmit)}
        className={className}
        noValidate
      >
        {children}
      </form>
    </RHFormProvider>
  )
}
