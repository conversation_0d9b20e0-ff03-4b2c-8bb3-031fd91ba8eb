import { TextInputController } from "./TextInputController"
import { TextFieldProps } from "@mui/material"
import React from "react"

// Define a type that includes only the props we need
type NumberOnlyInputControllerProps = {
  name: string
  placeholder?: string
  validateOnBlur?: boolean
  maxLength?: number
  className?: string
  disabled?: boolean
  error?: boolean
  fullWidth?: boolean
  size?: "small" | "medium"
  variant?: "standard" | "filled" | "outlined"
  sx?: TextFieldProps["sx"]
}

/**
 * A specialized input controller that only accepts numeric characters
 * Returns string values that contain only numeric characters
 */
export const NumberOnlyInputController = ({
  name,
  placeholder,
  validateOnBlur = false,
  maxLength,
  className,
  disabled,
  error,
  fullWidth,
  size = "small",
  variant,
  sx,
}: NumberOnlyInputControllerProps) => {
  // Create a keyboard event handler for numeric input only
  const handleKeyDown = (
    e: React.KeyboardEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLDivElement
    >,
  ) => {
    // Only process if target is an input element
    if (
      !(e.target instanceof HTMLInputElement) &&
      !(e.target instanceof HTMLTextAreaElement)
    ) {
      return
    }

    // Only allow numeric input, backspace, delete, tab, arrows, and control keys
    const allowedKeys = [
      "Backspace",
      "Delete",
      "Tab",
      "ArrowLeft",
      "ArrowRight",
      "ArrowUp",
      "ArrowDown",
      "Home",
      "End",
    ]

    // Allow decimal point
    if (e.key === ".") {
      // Only allow one decimal point
      if (e.target.value.includes(".")) {
        e.preventDefault()
      }
      return
    }

    // Allow numeric input and special keys
    if (
      !/^\d$/.test(e.key) &&
      !allowedKeys.includes(e.key) &&
      !e.ctrlKey &&
      !e.metaKey &&
      !e.altKey
    ) {
      e.preventDefault()
    }

    // Check max length
    if (maxLength && /^\d$/.test(e.key)) {
      const input = e.target
      if (
        input.value.length >= maxLength &&
        input.selectionStart === input.selectionEnd
      ) {
        // Only if not replacing text
        e.preventDefault()
      }
    }
  }

  return (
    <TextInputController
      name={name}
      placeholder={placeholder}
      type='text'
      validateOnBlur={validateOnBlur}
      onKeyDown={handleKeyDown}
      className={className}
      disabled={disabled}
      error={error}
      fullWidth={fullWidth}
      size={size}
      variant={variant}
      sx={sx}
      slotProps={{
        htmlInput: {
          maxLength,
          inputMode: "decimal",
        },
      }}
    />
  )
}
