import { LocationIcon } from "@/Icons"
import {
  CircularProgress,
  FormControl,
  FormHelperText,
  IconButton,
  InputAdornment,
} from "@mui/material"
import { useState } from "react"
import { useFormContext } from "react-hook-form"
import { StyledTextInputController } from "./TextInputController.style"

// Helper to fetch address details from Google Maps Geocoding API
async function fetchAddressFromZip(zip: string): Promise<{
  city?: string
  state?: string
  country?: string
}> {
  const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY
  if (!apiKey) throw new Error("Google Maps API key not set")
  const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(
    zip,
  )}&key=${apiKey}`
  const res = await fetch(url)
  const data = await res.json()
  if (!data.results?.length) return {}
  const address = data.results[0].address_components
  let city, state, country
  for (const comp of address) {
    if (comp.types.includes("locality")) city = comp.long_name
    if (comp.types.includes("administrative_area_level_1"))
      state = comp.long_name
    if (comp.types.includes("country")) country = comp.long_name
  }
  return { city, state, country }
}

export const ZipCodeAutoFill = ({
  name = "zipcode",
  placeholder = "Enter Zip Code",
  onAddressFetched,
}: {
  name?: string
  placeholder?: string
  onAddressFetched?: (address: {
    city?: string
    state?: string
    country?: string
  }) => void
}) => {
  const { setValue, watch } = useFormContext()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const zip = watch(name)

  // Call parent callback if address is fetched
  const handleFetch = async () => {
    if (!zip) return
    setLoading(true)
    setError(null)
    try {
      const result = await fetchAddressFromZip(zip)
      if (onAddressFetched) onAddressFetched(result)
    } catch (error) {
      console.error("Error fetching address:", error)
      setError("Could not fetch address for this zip code.")
    } finally {
      setLoading(false)
    }
  }

  return (
    <FormControl fullWidth variant='outlined' error={!!error}>
      <StyledTextInputController
        size='small'
        name={name}
        id={name}
        value={zip || ""}
        placeholder={placeholder}
        onChange={(e) =>
          setValue(name, e.target.value, { shouldValidate: true })
        }
        onBlur={handleFetch}
        InputProps={{
          endAdornment: (
            <InputAdornment position='end'>
              <IconButton
                onClick={handleFetch}
                disabled={loading || !zip}
                size='small'
                tabIndex={-1}
              >
                {loading ? (
                  <CircularProgress size={18} />
                ) : (
                  <LocationIcon fontSize='small' />
                )}
              </IconButton>
            </InputAdornment>
          ),
        }}
        error={!!error}
        helperText={error || undefined}
        autoComplete='postal-code'
        fullWidth
      />
      {!!error && <FormHelperText>{error}</FormHelperText>}
    </FormControl>
  )
}
