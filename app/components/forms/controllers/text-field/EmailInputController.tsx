import { TextInputController } from "./TextInputController"

type EmailInputControllerProps = {
  name: string
  placeholder?: string
  validateOnBlur?: boolean
  isTextarea?: boolean
  rows?: number
  overrideValue?: string
  // Removed onKeyDown to match expected prop types
}

/**
 * A specialized input controller for email input
 * Uses schema validation for error messages
 */
export const EmailInputController = ({
  name,
  placeholder,
  validateOnBlur = true,
  ...props
}: EmailInputControllerProps) => {
  return (
    <TextInputController
      name={name}
      placeholder={placeholder}
      type={"email"}
      rows={1}
      validateOnBlur={validateOnBlur}
      {...props}
    />
  )
}
