import { FormControl, FormHelperText, TextFieldProps } from "@mui/material"
import { useEffect } from "react"
import { Controller, useFormContext } from "react-hook-form"
import { StyledTextInputController } from "./TextInputController.style"

type TControllerProps = {
  name: string
  isTextarea?: boolean
  rows?: number
  overrideValue?: string
  validateOnBlur?: boolean
  onKeyDown?: React.KeyboardEventHandler<HTMLInputElement | HTMLTextAreaElement>
} & TextFieldProps

export const TextInputController = ({
  name,
  isTextarea = false,
  rows = 4,
  type = "text",
  overrideValue,
  validateOnBlur = false,
  onKeyDown,

  ...textFieldProps
}: TControllerProps) => {
  const { control, setValue, trigger } = useFormContext()

  useEffect(() => {
    if (overrideValue) {
      setValue(name, overrideValue)
    }
  }, [overrideValue, name, setValue])

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => {
        return (
          <FormControl fullWidth variant='outlined' error={!!error}>
            <StyledTextInputController
              size='small'
              {...field}
              id={name}
              value={field.value || ""}
              type={!isTextarea ? type : undefined}
              multiline={isTextarea}
              rows={isTextarea ? rows : undefined}
              error={!!error}
              onChange={(e) => {
                field.onChange(e)
              }}
              onBlur={() => {
                field.onBlur()
                if (validateOnBlur) {
                  trigger(name)
                }
              }}
              onKeyDown={onKeyDown}
              {...textFieldProps}
            />
            {!!error && <FormHelperText>{error.message}</FormHelperText>}
          </FormControl>
        )
      }}
    />
  )
}
