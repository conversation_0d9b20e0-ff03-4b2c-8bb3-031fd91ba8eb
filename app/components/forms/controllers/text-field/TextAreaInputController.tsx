import { TextFieldProps } from "@mui/material"
import { TextInputController } from "./TextInputController"

type TextAreaInputControllerProps = {
  name: string
  placeholder?: string
  rows?: number
  overrideValue?: string
  validateOnBlur?: boolean
  maxLength?: number
} & Omit<TextFieldProps, "multiline" | "rows">

/**
 * A specialized input controller for multiline text input
 */
export const TextAreaInputController = ({
  name,
  placeholder,
  rows = 4,
  overrideValue,
  validateOnBlur = false,
  maxLength,
  ...props
}: TextAreaInputControllerProps) => {
  return (
    <TextInputController
      name={name}
      placeholder={placeholder}
      isTextarea={true}
      rows={rows}
      overrideValue={overrideValue}
      validateOnBlur={validateOnBlur}
      inputProps={{
        maxLength: maxLength,
        ...props.inputProps,
      }}
    />
  )
}
