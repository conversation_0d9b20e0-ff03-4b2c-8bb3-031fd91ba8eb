import { TextFieldProps } from "@mui/material"
import { TextInputController } from "./TextInputController"

type TextOnlyInputControllerProps = {
  name: string
  placeholder?: string
  isTextarea?: boolean
  rows?: number
  overrideValue?: string
  validateOnBlur?: boolean
} & Omit<TextFieldProps, "onKeyDown">

/**
 * A specialized input controller that only accepts text characters (no numbers)
 * Uses schema validation for error messages
 */
export const TextOnlyInputController = ({
  name,
  placeholder,
  isTextarea = false,
  rows = 4,
  overrideValue,
  validateOnBlur = false,
  ...props
}: TextOnlyInputControllerProps) => {
  return (
    <TextInputController
      name={name}
      placeholder={placeholder}
      isTextarea={isTextarea}
      rows={rows}
      overrideValue={overrideValue}
      validateOnBlur={validateOnBlur}
      onKeyDown={(e) => {
        // Prevent numeric input
        if (/^\d$/.test(e.key) && !e.ctrlKey && !e.metaKey && !e.altKey) {
          e.preventDefault()
        }
      }}
      {...props}
    />
  )
}
