"use client"

import { DropDownValue } from "@/app/shared/types/DropDownValue"
import {
  FormControl,
  FormControlProps,
  FormHelperText,
  MenuItem,
  Select,
  SelectProps,
} from "@mui/material"
import { useMemo } from "react"
import { Controller, useFormContext } from "react-hook-form"

type DdValueSelectProps = {
  name: string
  options: DropDownValue[]
  placeholder?: string
  onEmptyOptionMessage?: string
  disabled?: boolean
  onChange?: (selectedOption: DropDownValue | null) => void
} & SelectProps &
  FormControlProps

/**
 * A specialized select input controller for DropDownValue objects
 * Stores the ID in the form value but displays the name in the UI
 */
export const DdValueSelectInputController = ({
  name,
  options,
  placeholder = "Select an option",
  onEmptyOptionMessage = "No options available",
  disabled = false,
  onChange,
  ...muiProps
}: DdValueSelectProps) => {
  const { control, setValue } = useFormContext()

  const menuItems = useMemo(() => {
    if (options.length === 0) {
      return <MenuItem disabled>{onEmptyOptionMessage}</MenuItem>
    } else {
      return options.map((item) => (
        <MenuItem
          key={item.id}
          value={item.id}
          sx={{ display: "flex", alignItems: "center", gap: 1 }}
          disabled={disabled || item.isDisabled}
        >
          {item.name}
        </MenuItem>
      ))
    }
  }, [options, onEmptyOptionMessage, disabled])

  return (
    <Controller
      name={name}
      control={control}
      render={({
        field: { ref, value, onChange: fieldOnChange, ...fieldProps },
        fieldState: { error },
      }) => {
        // Handle internal onChange to store both id and name
        const handleChange = (e: React.ChangeEvent<{ value: unknown }>) => {
          const selectedId = e.target.value as number

          // Find the selected option to get the name
          const selectedOption =
            options.find((opt) => opt.id === selectedId) || null

          // Call the field's onChange with the ID value
          fieldOnChange(selectedId)

          // Store the name in a separate field if needed
          const namePath = `${name}Name`
          setValue(namePath, selectedOption?.name || "", {
            shouldValidate: false,
          })

          // Call the external onChange if provided
          if (onChange && selectedOption) {
            onChange(selectedOption)
          }
        }

        return (
          <FormControl
            fullWidth
            variant='outlined'
            error={!!error}
            {...muiProps}
          >
            <Select
              size='small'
              MenuProps={{
                disableScrollLock: true,
              }}
              id={name}
              {...fieldProps}
              onChange={(event) =>
                handleChange(event as React.ChangeEvent<{ value: unknown }>)
              }
              inputRef={ref}
              displayEmpty
              disabled={disabled}
              value={value || ""}
              renderValue={(selected) => {
                if (!selected) {
                  return <span style={{ color: "#9e9e9e" }}>{placeholder}</span>
                }

                // Find the display text based on the selected ID
                const option = options.find((opt) => opt.id === selected)
                if (option) {
                  return (
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        gap: "8px",
                      }}
                    >
                      {option.name}
                    </div>
                  )
                }

                // Fallback to showing the ID if name not found
                return (
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "8px",
                    }}
                  >
                    {selected}
                  </div>
                )
              }}
            >
              {menuItems}
            </Select>
            {!!error && <FormHelperText>{error.message}</FormHelperText>}
          </FormControl>
        )
      }}
    />
  )
}
