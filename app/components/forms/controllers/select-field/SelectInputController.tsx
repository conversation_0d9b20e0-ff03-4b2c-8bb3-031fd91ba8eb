import { useMemo } from "react"
import { use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Controller } from "react-hook-form"
import {
  Select,
  MenuItem,
  FormControl,
  FormHelperText,
  SelectProps,
  FormControlProps,
} from "@mui/material"
import { DropDownValue } from "@/app/shared/types/DropDownValue"

// Simple select input controller for basic string/number values
type SimpleSelectProps = {
  name: string
  options: { value: string | number; label: string }[]
  placeholder?: string
  onEmptyOptionMessage?: string
  disabled?: boolean
} & SelectProps &
  FormControlProps

// Dropdown value select input controller for DropDownValue objects
type DdValueSelectProps = {
  name: string
  options: DropDownValue[]
  placeholder?: string
  onEmptyOptionMessage?: string
  disabled?: boolean
  valueType: "id" | "name"
} & SelectProps &
  FormControlProps

type TControllerProps = SimpleSelectProps | DdValueSelectProps

export const SelectInputController = ({
  name,
  options,
  placeholder = "Select an option",
  onEmptyOptionMessage = "No options available",
  disabled = false,
  ...muiProps
}: TControllerProps) => {
  const { control } = useFormContext()

  // Check if options is an array of DropDownValue objects
  const isDdValueOptions =
    options.length > 0 && "id" in options[0] && "name" in options[0]

  // For DropDownValue options, determine if we should use id or name as the value
  const valueType =
    isDdValueOptions && "valueType" in muiProps ? muiProps.valueType : undefined

  const menuItems = useMemo(() => {
    if (options.length === 0) {
      return <MenuItem disabled>{onEmptyOptionMessage}</MenuItem>
    } else {
      if (isDdValueOptions) {
        // Handle DropDownValue options
        return (options as DropDownValue[]).map((item) => (
          <MenuItem
            key={item.id}
            value={valueType === "id" ? item.id : item.name}
            sx={{ display: "flex", alignItems: "center", gap: 1 }}
            disabled={disabled}
          >
            {item.name}
          </MenuItem>
        ))
      } else {
        // Handle simple options
        return (options as { value: string | number; label: string }[]).map(
          (item) => (
            <MenuItem
              key={item.value}
              value={item.value}
              sx={{ display: "flex", alignItems: "center", gap: 1 }}
              disabled={disabled}
            >
              {item.label}
            </MenuItem>
          ),
        )
      }
    }
  }, [options, onEmptyOptionMessage, valueType, disabled, isDdValueOptions])

  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { ref, ...field }, fieldState: { error } }) => (
        <FormControl fullWidth variant='outlined' error={!!error} {...muiProps}>
          <Select
            size='small'
            MenuProps={{
              disableScrollLock: true,
            }}
            id={name}
            {...field}
            inputRef={ref}
            displayEmpty
            value={field.value || ""}
            renderValue={(selected) => {
              if (!selected) {
                return <span style={{ color: "#9e9e9e" }}>{placeholder}</span>
              }

              // For DropDownValue options, find the display text
              if (isDdValueOptions && valueType === "id") {
                const option = (options as DropDownValue[]).find(
                  (opt) => opt.id === selected,
                )
                if (option) {
                  return (
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        gap: "8px",
                      }}
                    >
                      {option.name}
                    </div>
                  )
                }
              }

              // For simple options or name-based DropDownValue options
              return (
                <div
                  style={{ display: "flex", alignItems: "center", gap: "8px" }}
                >
                  {selected}
                </div>
              )
            }}
          >
            {menuItems}
          </Select>
          {!!error && <FormHelperText>{error.message}</FormHelperText>}
        </FormControl>
      )}
    />
  )
}
