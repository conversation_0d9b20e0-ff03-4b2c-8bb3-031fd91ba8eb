import {
  Box,
  FormControl,
  FormHelperText,
  MenuItem,
  Select,
  Typography,
} from "@mui/material"
import Image from "next/image"
import { Controller, useFormContext } from "react-hook-form"

export type Vehicle = {
  id: number
  name: string
  length: number
  width: number
  height: number
  cft: number
  minimumCost: number
  maxLoadInKgs: number
  allowPartLoad: boolean
  imageUrls: string[]
}

type VehicleSelectInputControllerProps = {
  name: string
  options: Vehicle[]
  placeholder?: string
  disabled?: boolean
}

export const VehicleSelectInputController = ({
  name,
  options,
  placeholder = "Choose a vehicle",
  disabled = false,
}: VehicleSelectInputControllerProps) => {
  const { control } = useFormContext()

  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { ref, ...field }, fieldState: { error } }) => (
        <FormControl fullWidth variant='outlined' error={!!error}>
          <Select
            size='small'
            MenuProps={{
              disableScrollLock: true,
              PaperProps: {
                style: {
                  maxHeight: 350, // Limit dropdown height
                  overflowY: "auto",
                },
              },
            }}
            id={name}
            {...field}
            inputRef={ref}
            displayEmpty
            value={field.value || ""}
            renderValue={(selected) => {
              if (!selected) {
                return <span style={{ color: "#9e9e9e" }}>{placeholder}</span>
              }
              const vehicle = options.find((v) => v.id === selected)
              if (vehicle) {
                return (
                  <Box display='flex' alignItems='center' gap={1}>
                    {vehicle.imageUrls?.[0] && (
                      <Image
                        src={vehicle.imageUrls[0]}
                        alt={vehicle.name}
                        width={40}
                        height={24}
                        style={{ objectFit: "contain", borderRadius: 4 }}
                      />
                    )}
                    <Typography variant='body2'>{vehicle.name}</Typography>
                  </Box>
                )
              }
              return selected
            }}
          >
            {options.length === 0 ? (
              <MenuItem disabled>No vehicles available</MenuItem>
            ) : (
              options.map((vehicle) => (
                <MenuItem
                  key={vehicle.id}
                  value={vehicle.id}
                  sx={{ display: "flex", alignItems: "start", gap: 1 }}
                  disabled={disabled}
                >
                  <Box
                    display='flex'
                    marginRight={2}
                    alignItems='center'
                    gap={1}
                  >
                    {vehicle.imageUrls?.[0] && (
                      <Image
                        src={vehicle.imageUrls[0]}
                        alt={vehicle.name}
                        width={60}
                        height={60}
                        style={{ objectFit: "contain", borderRadius: 4 }}
                      />
                    )}
                    <Box>
                      <Typography variant='body1' fontWeight={500}>
                        {vehicle.name}
                      </Typography>
                      <Typography
                        variant='caption'
                        color='text.secondary'
                        className='text-xs'
                      >
                        {vehicle.length}L x {vehicle.width}W x {vehicle.height}H
                        ft | {vehicle.cft} CFT | Max Load:{" "}
                        {vehicle.maxLoadInKgs} kg
                      </Typography>
                    </Box>
                  </Box>
                </MenuItem>
              ))
            )}
          </Select>
          {!!error && <FormHelperText>{error.message}</FormHelperText>}
        </FormControl>
      )}
    />
  )
}
