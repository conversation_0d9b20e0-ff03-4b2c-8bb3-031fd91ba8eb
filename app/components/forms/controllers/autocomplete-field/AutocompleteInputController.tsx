"use client"

import { DropDownValue } from "@/app/shared/types/DropDownValue"
import {
  ClickAwayListener,
  FormControl,
  FormHelperText,
  Paper,
  Popper,
  TextField,
} from "@mui/material"
import { styled } from "@mui/material/styles"
import { useCallback, useEffect, useRef, useState } from "react"
import { Controller, useFormContext } from "react-hook-form"

const StyledTextField = styled(TextField)({
  "& .MuiOutlinedInput-root": {
    // Add any custom styling here
  },
})

type AutocompleteInputControllerProps = {
  name: string
  placeholder?: string
  options: DropDownValue[]
  validateOnBlur?: boolean
  disabled?: boolean
}

export const AutocompleteInputController = ({
  name,
  placeholder = "",
  options,
  validateOnBlur = false,
  disabled = false,
}: AutocompleteInputControllerProps) => {
  const { control, trigger, watch, setValue } = useFormContext()
  const [inputValue, setInputValue] = useState("")
  const [filteredOptions, setFilteredOptions] = useState<DropDownValue[]>([])
  const [isOpen, setIsOpen] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)

  // Watch for field value changes
  const fieldValue = watch(name)

  // Create ID field name
  const idFieldName = `${name}Id`

  // Methods
  const filterOptions = useCallback(
    (searchValue: string) => {
      if (searchValue.trim() === "") {
        setFilteredOptions(options)
        return
      }

      const filtered = options.filter((option) =>
        option.name.toLowerCase().includes(searchValue.toLowerCase()),
      )
      setFilteredOptions(filtered)
    },
    [options],
  )

  const handleChange = useCallback(
    (
      e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
      onChange: (value: unknown) => void,
    ) => {
      const newValue = e.target.value
      setInputValue(newValue)
      setIsOpen(true)

      // If the input is empty, clear the field value and ID
      if (newValue === "") {
        onChange("")
        setValue(idFieldName, null, { shouldValidate: false })
      }
    },
    [setValue, idFieldName],
  )

  const handleFocus = useCallback(() => {
    setIsOpen(true)
    if (inputValue.trim() === "") {
      setFilteredOptions(options)
    }
  }, [inputValue, options])

  const handleBlur = useCallback(
    (onBlur: () => void, onChange: (value: unknown) => void) => {
      onBlur()

      // If no option is selected, use the input value as is
      if (inputValue && !fieldValue) {
        onChange(inputValue)
        // Clear the ID field when using custom text
        setValue(idFieldName, null, { shouldValidate: false })
      }

      if (validateOnBlur) {
        trigger(name)
      }
    },
    [
      inputValue,
      fieldValue,
      validateOnBlur,
      trigger,
      name,
      setValue,
      idFieldName,
    ],
  )

  const handleOptionSelect = useCallback(
    (option: DropDownValue, onChange: (value: unknown) => void) => {
      // Store the full option object in the main field
      onChange(option)
      // Also store the ID in a separate field for easier backend integration
      setValue(idFieldName, option.id, { shouldValidate: false })
      setInputValue(option.name)
      setIsOpen(false)
    },
    [setValue, idFieldName],
  )

  const handleClickAway = useCallback(() => {
    setIsOpen(false)
  }, [])

  // Effects
  useEffect(() => {
    filterOptions(inputValue)
  }, [inputValue, filterOptions])

  useEffect(() => {
    if (fieldValue) {
      // Handle both string values and DropDownValue objects
      if (typeof fieldValue === "string") {
        setInputValue(fieldValue)
        // Clear the ID field for string values
        setValue(idFieldName, null, { shouldValidate: false })
      } else {
        // For DropDownValue objects, set both the display value and ID
        setInputValue(fieldValue.name)
        setValue(idFieldName, fieldValue.id, { shouldValidate: false })
      }
    } else {
      // Clear input and ID when field is empty
      setInputValue("")
      setValue(idFieldName, null, { shouldValidate: false })
    }
  }, [fieldValue, setValue, idFieldName])

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <ClickAwayListener onClickAway={handleClickAway}>
          <div className='relative w-full'>
            <FormControl fullWidth variant='outlined' error={!!error}>
              <StyledTextField
                size='small'
                placeholder={placeholder}
                value={inputValue}
                autoComplete='off'
                onChange={(e) => handleChange(e, field.onChange)}
                onFocus={handleFocus}
                onBlur={() => handleBlur(field.onBlur, field.onChange)}
                inputRef={inputRef}
                disabled={disabled}
                error={!!error}
                name={field.name}
              />
              {!!error && <FormHelperText>{error.message}</FormHelperText>}
            </FormControl>

            {isOpen && filteredOptions.length > 0 && (
              <Popper
                open={true}
                anchorEl={inputRef.current}
                placement='bottom-start'
                style={{ width: inputRef.current?.clientWidth, zIndex: 1300 }}
              >
                <Paper elevation={3} className='max-h-60 overflow-y-auto'>
                  <div className='py-1'>
                    {filteredOptions.map((option) => (
                      <div
                        key={option.id}
                        className='cursor-pointer px-4 py-2 hover:bg-gray-100'
                        onClick={() =>
                          handleOptionSelect(option, field.onChange)
                        }
                      >
                        {option.name}
                      </div>
                    ))}
                  </div>
                </Paper>
              </Popper>
            )}
          </div>
        </ClickAwayListener>
      )}
    />
  )
}
