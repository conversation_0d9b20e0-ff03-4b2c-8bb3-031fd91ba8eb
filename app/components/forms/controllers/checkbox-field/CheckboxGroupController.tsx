import React, { useCallback } from "react"
import { Controller, FieldValues, Path, useFormContext } from "react-hook-form"
import {
  Checkbox,
  FormControl,
  FormControlLabel,
  FormControlLabelProps,
  FormGroup,
  FormHelperText,
} from "@mui/material"

interface Option<T extends string | number> {
  label: string
  value: T
}

interface Props<T extends FieldValues> {
  name: Path<T>
  options: Option<string | number>[]
  formControlLabelProps?: Omit<FormControlLabelProps, "control" | "label">
}

export const CheckboxesGroupController = <T extends FieldValues>({
  name,
  options,
  formControlLabelProps,
}: Props<T>) => {
  const { control } = useFormContext()

  const handleCheckboxChange = useCallback(
    (
      currentValues: Array<Option<string | number>["value"]>,
      optionValue: Option<string | number>["value"],
    ) => {
      const newValues = currentValues?.includes(optionValue)
        ? currentValues.filter((v) => v !== optionValue)
        : [...(currentValues || []), optionValue]

      return newValues
    },
    [],
  )

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <FormControl component='fieldset' error={!!error} fullWidth>
          <FormGroup row>
            {options.map((option) => (
              <FormControlLabel
                {...formControlLabelProps}
                key={option.value}
                control={
                  <Checkbox
                    checked={field.value?.includes(option.value) ?? false}
                    onChange={() =>
                      field.onChange(
                        handleCheckboxChange(field.value, option.value),
                      )
                    }
                  />
                }
                label={option.label}
              />
            ))}
          </FormGroup>
          {error && <FormHelperText>{error.message}</FormHelperText>}
        </FormControl>
      )}
    />
  )
}
