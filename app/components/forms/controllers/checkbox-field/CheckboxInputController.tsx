import {
  Checkbox,
  CheckboxProps,
  FormControl,
  FormControlLabel,
  FormControlLabelProps,
  FormHelperText,
} from "@mui/material"
import { Controller, FieldValues, Path, useFormContext } from "react-hook-form"

type CheckboxInputProps<T extends FieldValues> = {
  name: Path<T>
  label: string
  checkboxProps?: CheckboxProps
  formControlLabelProps?: Omit<FormControlLabelProps, "control" | "label">
}

export const CheckboxInputController = <T extends FieldValues>({
  name,
  label,
  checkboxProps,
  formControlLabelProps,
}: CheckboxInputProps<T>) => {
  const { control } = useFormContext<T>()

  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { value, onChange }, fieldState: { error } }) => (
        <FormControl
          error={!!error}
          fullWidth
          variant='standard'
          className='w-full break-words'
        >
          <FormControlLabel
            className='w-full min-w-[120px] max-w-max'
            sx={{
              marginLeft: "0px",
            }}
            {...formControlLabelProps}
            control={
              <Checkbox
                {...checkboxProps}
                checked={Boolean(value)}
                onChange={(_, checked) => onChange(checked)}
                sx={{ flexShrink: 0 }}
                size='small'
              />
            }
            label={label}
          />
          {error && <FormHelperText>{error.message}</FormHelperText>}
        </FormControl>
      )}
    />
  )
}
