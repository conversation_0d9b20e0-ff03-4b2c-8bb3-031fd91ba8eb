import { Controller, useFormContext } from "react-hook-form"
import {
  FormControl,
  FormControlLabel,
  FormHelperText,
  Radio,
  RadioGroup,
} from "@mui/material"

type TControllerProps = {
  name: string
  options: { value: string; label: string }[]
}

const RadioInputController = ({ name, options }: TControllerProps) => {
  const { control } = useFormContext()

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState }) => (
        <FormControl component='fieldset' error={!!fieldState.error}>
          <RadioGroup {...field} value={field.value || ""} row>
            {options.map((option) => (
              <FormControlLabel
                key={option.value}
                value={option.value}
                control={<Radio />}
                label={option.label}
              />
            ))}
          </RadioGroup>
          {fieldState.error && (
            <FormHelperText>{fieldState.error.message}</FormHelperText>
          )}
        </FormControl>
      )}
    />
  )
}

export default RadioInputController
