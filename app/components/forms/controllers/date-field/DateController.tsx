import { Controller, useFormContext } from "react-hook-form"
import { DatePicker } from "@mui/x-date-pickers/DatePicker"
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider"
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs"
import { FormControl, FormHelperText } from "@mui/material"
import dayjs from "dayjs"

type TDateControllerProps = {
  name: string
  label?: string
}

const DateController = ({ name }: TDateControllerProps) => {
  const { control } = useFormContext()

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Controller
        name={name}
        control={control}
        defaultValue={null}
        render={({ field, fieldState: { error } }) => (
          <FormControl fullWidth error={!!error}>
            <DatePicker
              {...field}
              disablePast
              value={field.value ? dayjs(field.value) : null}
              onChange={(newValue) => field.onChange(newValue?.toISOString())}
              slotProps={{
                textField: {
                  inputProps: { readOnly: true },
                  size: "small",
                },
              }}
            />
            {!!error && <FormHelperText>{error.message}</FormHelperText>}
          </FormControl>
        )}
      />
    </LocalizationProvider>
  )
}

export default DateController
