import { CloseIcon, UploadIcon } from "@/Icons"
import { Controller, useFormContext } from "react-hook-form"

type TControllerProps = {
  name: string
  accept?: string
}

export const FileInputController = ({ name, accept }: TControllerProps) => {
  const { control } = useFormContext()

  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange, value, ...field }, fieldState }) => (
        <div className='flex flex-col gap-1'>
          <input
            type='file'
            id={name}
            {...field}
            value={value?.fileName}
            accept={accept}
            onChange={(e) => onChange(e.target.files)}
            className='hidden'
          />
          {!value ? (
            <label
              htmlFor={name}
              className='flex w-fit cursor-pointer items-center gap-2 rounded-md bg-zinc-100 font-medium ~px-3/5 ~py-1/1.5'
            >
              <UploadIcon className='-translate-y-[1px] ~size-4/5' /> Upload
            </label>
          ) : (
            <div className='flex items-center gap-1'>
              <span>{value[0]?.name}</span>
              <button
                type='button'
                className='text-red-500'
                onClick={() => onChange(null)}
              >
                <CloseIcon />
              </button>
            </div>
          )}
          {fieldState.error && (
            <small className='text-red-500'>{fieldState.error.message}</small>
          )}
        </div>
      )}
    />
  )
}
