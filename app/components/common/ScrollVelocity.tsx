"use client"

import {
  motion,
  useAnimation<PERSON>rame,
  useInView,
  useMotionValue,
  useScroll,
  useSpring,
  useTransform,
  useVelocity,
} from "framer-motion"
import React, { useLayoutEffect, useRef, useState } from "react"

type VelocityMapping = {
  input: [number, number]
  output: [number, number]
}

type VelocityItemProps = {
  children: React.ReactNode
  baseVelocity: number
  scrollContainerRef?: React.RefObject<HTMLElement>
  className?: string
  damping?: number
  stiffness?: number
  numCopies?: number
  velocityMapping?: VelocityMapping
}

type ScrollVelocityProps = {
  scrollContainerRef?: React.RefObject<HTMLElement>
  items: React.ReactNode[]
  velocity?: number
  damping?: number
  stiffness?: number
  numCopies?: number
  velocityMapping?: VelocityMapping
}

const useElementWidth = (ref: React.RefObject<HTMLElement>): number => {
  const [width, setWidth] = useState<number>(0)

  useLayoutEffect(() => {
    const updateWidth = () => {
      if (ref.current) {
        setWidth(ref.current.offsetWidth)
      }
    }

    updateWidth()

    // Use ResizeObserver instead of window event
    const resizeObserver = new ResizeObserver(updateWidth)
    if (ref.current) {
      resizeObserver.observe(ref.current)
    }

    return () => {
      resizeObserver.disconnect()
    }
  }, [ref])

  return width
}

const VelocityItem = ({
  children,
  baseVelocity,
  scrollContainerRef,
  damping = 50,
  stiffness = 400,
  numCopies = 6,
  velocityMapping = { input: [0, 1000], output: [0, 5] },
}: VelocityItemProps) => {
  const baseX = useMotionValue(0)
  const scrollOptions = scrollContainerRef
    ? { container: scrollContainerRef }
    : undefined
  const { scrollY } = useScroll(scrollOptions)
  const scrollVelocity = useVelocity(scrollY)
  const smoothVelocity = useSpring(scrollVelocity, {
    damping,
    stiffness,
  })
  const velocityFactor = useTransform(
    smoothVelocity,
    velocityMapping.input,
    velocityMapping.output,
    { clamp: false },
  )

  const copyRef = useRef<HTMLSpanElement>(null)
  // @ts-expect-error: expect error
  const copyWidth = useElementWidth(copyRef)
  const ref = useRef<HTMLDivElement>(null)
  const isInView = useInView(ref)

  const wrap = (min: number, max: number, v: number): number => {
    const range = max - min
    const mod = (((v - min) % range) + range) % range
    return mod + min
  }

  const x = useTransform(baseX, (v) => {
    if (copyWidth === 0) return "0px"
    return `${wrap(-copyWidth, 0, v)}px`
  })

  const directionFactor = useRef<number>(1)
  useAnimationFrame((t, delta) => {
    if (!isInView) return
    let moveBy = directionFactor.current * baseVelocity * (delta / 1000)

    if (velocityFactor.get() < 0) {
      directionFactor.current = -1
    } else if (velocityFactor.get() > 0) {
      directionFactor.current = 1
    }

    moveBy += directionFactor.current * moveBy * velocityFactor.get()
    baseX.set(baseX.get() + moveBy)
  })

  const elements = Array.from({ length: numCopies }, (_, i) => (
    <span className='flex-shrink-0' key={i} ref={i === 0 ? copyRef : undefined}>
      {children}
    </span>
  ))

  return (
    <div ref={ref} className='relative overflow-x-hidden'>
      <motion.div className='flex ~gap-2/4' style={{ x }}>
        {elements}
      </motion.div>
    </div>
  )
}

export const ScrollVelocity: React.FC<ScrollVelocityProps> = ({
  scrollContainerRef,
  items = [],
  velocity = 50,
  damping = 50,
  stiffness = 400,
  numCopies = 6,
  velocityMapping = { input: [0, 1000], output: [0, 5] },
}) => {
  return (
    <section>
      {items.map((item, index) => (
        <VelocityItem
          key={index}
          baseVelocity={index % 2 !== 0 ? -velocity : velocity}
          scrollContainerRef={scrollContainerRef}
          damping={damping}
          stiffness={stiffness}
          numCopies={numCopies}
          velocityMapping={velocityMapping}
        >
          {item}
        </VelocityItem>
      ))}
    </section>
  )
}

export default ScrollVelocity
