/* eslint-disable @next/next/no-img-element */
"use client"

import { useState } from "react"
import { Vehicle } from "./types/quote-result.types"

/**
 * Simple carousel component for vehicle images
 */
const SimpleImageCarousel: React.FC<{
  images: string[]
  vehicleName: string
}> = ({ images, vehicleName }) => {
  const [currentIndex, setCurrentIndex] = useState(0)

  // Default fallback image if no images are provided
  const fallbackImage = "/images/cargo.webp"

  // If no images, show fallback
  if (!images || images.length === 0) {
    return (
      <div className='h-full max-h-36 w-full overflow-hidden rounded-lg bg-gray-100'>
        <img
          src={fallbackImage}
          alt={`${vehicleName} - Default Image`}
          className='h-full w-full object-cover'
        />
      </div>
    )
  }

  const goToPrevious = () => {
    const isFirstSlide = currentIndex === 0
    const newIndex = isFirstSlide ? images.length - 1 : currentIndex - 1
    setCurrentIndex(newIndex)
  }

  const goToNext = () => {
    const isLastSlide = currentIndex === images.length - 1
    const newIndex = isLastSlide ? 0 : currentIndex + 1
    setCurrentIndex(newIndex)
  }

  return (
    <div className='relative h-36 w-full'>
      <div className='h-full w-full overflow-hidden rounded-lg bg-gray-100'>
        <img
          src={images[currentIndex]}
          alt={`${vehicleName} - Image ${currentIndex + 1}`}
          className='h-full w-full object-cover'
        />
      </div>

      {/* Only show navigation buttons if there are multiple images */}
      {images.length > 1 && (
        <>
          {/* Left Arrow */}
          <button
            onClick={goToPrevious}
            className='absolute left-2 top-1/2 flex h-8 w-8 -translate-y-1/2 items-center justify-center rounded-full bg-white/80 text-gray-800 shadow-md transition-colors hover:bg-white'
          >
            <span className='sr-only'>Previous</span>
            <svg
              xmlns='http://www.w3.org/2000/svg'
              viewBox='0 0 20 20'
              fill='currentColor'
              className='h-5 w-5'
            >
              <path
                fillRule='evenodd'
                d='M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z'
                clipRule='evenodd'
              />
            </svg>
          </button>

          {/* Right Arrow */}
          <button
            onClick={goToNext}
            className='absolute right-2 top-1/2 flex h-8 w-8 -translate-y-1/2 items-center justify-center rounded-full bg-white/80 text-gray-800 shadow-md transition-colors hover:bg-white'
          >
            <span className='sr-only'>Next</span>
            <svg
              xmlns='http://www.w3.org/2000/svg'
              viewBox='0 0 20 20'
              fill='currentColor'
              className='h-5 w-5'
            >
              <path
                fillRule='evenodd'
                d='M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z'
                clipRule='evenodd'
              />
            </svg>
          </button>

          {/* Dots indicator */}
          <div className='absolute bottom-2 left-0 right-0 flex justify-center gap-1'>
            {images.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`h-2 w-2 rounded-full transition-colors ${
                  index === currentIndex ? "bg-primary" : "bg-gray-300"
                }`}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        </>
      )}
    </div>
  )
}

/**
 * Vehicle details component that displays vehicle information and images
 */
const VehicleDetails: React.FC<{ vehicle: Vehicle }> = ({ vehicle }) => {
  return (
    <div className='mb-4 rounded-lg bg-white'>
      <div className='flex flex-col gap-4 md:flex-row'>
        {/* Left side - Image carousel */}
        <div className='w-full md:w-1/2'>
          <SimpleImageCarousel
            images={vehicle.imageUrls}
            vehicleName={vehicle.name}
          />
        </div>

        {/* Right side - Vehicle details */}
        <div className='flex w-full flex-col items-start justify-start md:w-1/2'>
          <h3 className='mb-3 text-xl font-semibold'>{vehicle.name}</h3>
          <div className='grid w-full grid-cols-1 gap-2'>
            <div className='flex flex-col'>
              <span className='text-sm text-gray-500'>
                Dimensions{" "}
                <span className='text-xs text-gray-400'>(L x W x H)</span>
              </span>
              <span className='font-medium'>
                ({vehicle.length} ft x {vehicle.width} ft x {vehicle.height} ft)
              </span>
            </div>
            <div className='flex flex-col'>
              <span className='text-sm text-gray-500'>Capacity</span>
              <span className='font-medium'>{vehicle.cft} CFT</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default VehicleDetails
