"use client"

import { useMemo } from "react"
import Loader from "./Loader"
import Modal from "./Modal"
import QuoteResultDetails from "./QuoteResultDetails"
import {
  FieldConfigs,
  FormValues,
  QuoteResult,
} from "./types/quote-result.types"

type TQuoteCalculatorModalProps = {
  open: boolean
  onClose: () => void
  loading: boolean
  calculationResult: QuoteResult | null
  onRecalculate?: (updatedFormValues: FormValues) => void
  stepValue?: number
  serviceName?: string
  formValues?: FormValues
  fieldConfigs?: FieldConfigs
  onFormValueChange?: (field: string, value: string) => void
}

const QuoteCalculatorModal = ({
  open,
  onClose,
  loading,
  calculationResult,
  onRecalculate,
  stepValue = 0.5,
  serviceName,
  fieldConfigs,
  onFormValueChange,
}: TQuoteCalculatorModalProps) => {
  const modalContent = useMemo(() => {
    if (loading) {
      return (
        <div className='flex min-h-[70vh] w-full flex-col items-center justify-center'>
          <div className='flex min-h-20 min-w-20 items-center justify-center rounded-full bg-primary/5 md:min-h-24 md:min-w-24'>
            <Loader size={40} />
          </div>
          <p className='mt-4 text-lg font-medium text-gray-700'>
            Calculating your quote...
          </p>
          <p className='mt-2 text-sm text-gray-500'>
            This may take a few moments
          </p>
        </div>
      )
    }

    if (calculationResult) {
      return (
        <QuoteResultDetails
          result={calculationResult as QuoteResult}
          onRecalculate={onRecalculate}
          stepValue={stepValue}
          fieldConfigs={fieldConfigs}
          serviceName={serviceName}
          onFormValueChange={onFormValueChange}
        />
      )
    }

    return (
      <div className='flex min-h-[300px] flex-col items-center justify-center p-8 text-center'>
        <div className='mb-4 rounded-full bg-amber-50 p-4'>
          <span className='text-2xl text-amber-500'>⚠️</span>
        </div>
        <h3 className='mb-2 text-xl font-medium'>No Results Available</h3>
        <p className='text-gray-600'>
          We couldn't generate a quote with the provided information. Please try
          again with different details.
        </p>
      </div>
    )
  }, [
    calculationResult,
    loading,
    onRecalculate,
    stepValue,
    fieldConfigs,
    onFormValueChange,
    serviceName,
  ])

  return (
    <Modal open={open} onClose={onClose} maxWidth='md' fullWidth={true}>
      {modalContent}
    </Modal>
  )
}

export default QuoteCalculatorModal
