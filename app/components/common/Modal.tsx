import { CloseIcon } from "@/Icons"
import { Dialog } from "@mui/material"

type TModalProps = {
  open: boolean
  onClose: () => void
  children: React.ReactNode
  maxWidth?: "xs" | "sm" | "md" | "lg" | "xl" | false
  fullWidth?: boolean
}

const Modal = (props: TModalProps) => {
  const { open, onClose, children, maxWidth = "md", fullWidth = true } = props

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth={maxWidth}
      fullWidth={fullWidth}
      slotProps={{
        paper: {
          style: {
            minHeight: "300px",
            maxHeight: "90vh",
            borderRadius: "12px",
            overflow: "hidden",
            margin: "10px",
            padding: "0px",
            width: "100%",
          },
        },
      }}
      className='w-full lg:min-w-[600px]'
    >
      <div className='relative overflow-hidden ~p-5/7'>{children}</div>
      <button
        onClick={onClose}
        className='absolute right-4 top-4 rounded p-1 transition-colors hover:bg-red-500 hover:text-white'
      >
        <CloseIcon className='~size-5/6' />
      </button>
    </Dialog>
  )
}

export default Modal
