/* eslint-disable @next/next/no-img-element */

type CostItem = {
  name: string
  cost: number | string
  unit: string
}

type TService = {
  image?: string
  name: string
  currency: string
  costData: CostItem[]
}

type TAirAmbulance = {
  currency: string
  cityCombinationType: string
  costData: CostItem[]
}

type TRelocationResult = TService
type TCourierParcelData = TService
type TTruckingData = TService
type TWarehouseData = TService

type TServiceData =
  | TRelocationResult
  | TCourierParcelData
  | TTruckingData
  | TWarehouseData
  | TAirAmbulance

type TQCResultViewProps = {
  serviceData: TServiceData
}

const QCResultView = ({ serviceData }: TQCResultViewProps) => {
  // Handle the case where serviceData might be undefined or null
  if (!serviceData || !serviceData.costData) {
    return (
      <div className='w-full p-6 text-center'>
        <h2 className='mb-6 text-2xl font-semibold'>No Quote Data Available</h2>
        <p className='text-gray-500'>Please try again or contact support.</p>
      </div>
    )
  }

  return (
    <div className='w-full p-6'>
      <h2 className='mb-6 text-center text-2xl font-semibold'>Quote Details</h2>

      {/* Service header with image and name */}
      {"name" in serviceData && serviceData.name && (
        <div className='mb-8 flex items-center justify-center gap-3'>
          {serviceData.image && (
            <div className='rounded-lg bg-gray-50 p-2'>
              <img
                src={serviceData.image}
                alt={serviceData.name}
                className='h-14 w-14 object-contain'
              />
            </div>
          )}
          <h3 className='text-xl font-medium'>{serviceData.name}</h3>
        </div>
      )}

      {/* Cost items list */}
      <div className='mx-auto grid w-full max-w-3xl ~gap-4/5'>
        {serviceData.costData.map((result, index) => (
          <QCResultItem
            key={index}
            {...result}
            isLast={index === serviceData.costData.length - 1}
          />
        ))}
      </div>

      {/* Disclaimer */}
      <div className='mt-8 text-center text-sm text-gray-500'>
        <p>
          This is an estimated quote and may vary based on actual requirements
        </p>
      </div>
    </div>
  )
}

export default QCResultView

/**
 * Individual cost item component
 */
const QCResultItem = ({
  name,
  cost,
  unit,
  isLast = false,
}: CostItem & { isLast?: boolean }) => {
  const isTotal = name.toUpperCase() === "TOTAL" || isLast

  return (
    <div
      className={`rounded-xl p-5 shadow-sm transition-all duration-200 ${
        isTotal
          ? "border border-green-200 bg-green-100 text-green-900 shadow-green-200"
          : "border border-zinc-100 bg-zinc-50 text-zinc-950 hover:bg-white"
      }`}
    >
      <div className='flex items-center justify-between'>
        <p
          className={`font-medium uppercase ${isTotal ? "text-lg font-bold" : ""}`}
        >
          {name}
        </p>
        <p className={`text-2xl font-semibold ${isTotal ? "text-3xl" : ""}`}>
          {unit === "kg" ? null : unit} {cost} {unit === "₹" ? null : unit}
        </p>
      </div>
    </div>
  )
}
