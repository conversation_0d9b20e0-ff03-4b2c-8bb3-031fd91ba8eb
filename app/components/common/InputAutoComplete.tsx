"use client"

import { Autocomplete } from "@react-google-maps/api"
import { useRef } from "react"

type TProps = {
  children: React.ReactNode
  onChange?: (place: google.maps.places.PlaceResult | undefined) => void
}

const InputAutoComplete = ({ children, onChange }: TProps) => {
  const autoCompleteRef = useRef<google.maps.places.Autocomplete | null>(null)

  const onLoad = (autocomplete: google.maps.places.Autocomplete): void => {
    autoCompleteRef.current = autocomplete
  }

  return (
    <Autocomplete
      onLoad={onLoad}
      onPlaceChanged={() => onChange?.(autoCompleteRef.current?.getPlace())}
      options={{
        // types: ["(regions)"],
        componentRestrictions: { country: "in" },
      }}
    >
      {children}
    </Autocomplete>
  )
}

export default InputAutoComplete
