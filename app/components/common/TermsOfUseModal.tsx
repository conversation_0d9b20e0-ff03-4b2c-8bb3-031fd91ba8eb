"use client"

import { SectionTitle, SectionWrapper } from "@/app/shared/components"
import { PRIVACY_AND_DATA_PROTECTION_POLICY } from "@/config"
import { Button } from "../ui"
import Modal from "./Modal"

interface TermsOfUseModalProps {
  open: boolean
  onClose: () => void
  onAccept?: () => void
}

const TermsOfUseModal = ({ open, onClose, onAccept }: TermsOfUseModalProps) => {
  const handleAccept = () => {
    if (onAccept) {
      onAccept()
    }
    onClose()
  }
  return (
    <Modal open={open} onClose={onClose} maxWidth='lg' fullWidth={true}>
      <div className='flex max-h-[80vh] flex-col'>
        <div className='flex-1 overflow-y-auto'>
          <SectionWrapper className='!px-0 !pb-0 font-inter !leading-tight text-zinc-400 ~text-sm/lg'>
            <SectionTitle
              title='Terms of Use - Third-Party Authentication Consent'
              subtitle='LEGAL'
              className='text-black'
            />
            <p>{PRIVACY_AND_DATA_PROTECTION_POLICY.introduction}</p>
            <div className='~mt-5/7 ~space-y-5/7'>
              {PRIVACY_AND_DATA_PROTECTION_POLICY.data.map((data, index) => (
                <div key={index}>
                  <p className='uppercase text-primary ~mb-3/5'>{data.title}</p>
                  {data.contentList ? (
                    <ul className='list-disc'>
                      {data.contentList.map((list, index) => (
                        <li className='ml-5' key={index}>
                          {list}
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p>{data.content}</p>
                  )}
                </div>
              ))}
            </div>
          </SectionWrapper>
        </div>

        {/* Fixed footer with buttons */}
        <div className='sticky bottom-0 mt-4 flex w-full justify-end gap-4 bg-white pt-4'>
          <Button
            size='sm'
            type='button'
            onClick={onClose}
            className='border border-primary bg-transparent !px-10 text-primary'
          >
            Close
          </Button>
          <Button
            size='sm'
            type='button'
            onClick={handleAccept}
            className='!px-10'
          >
            Accept Terms
          </Button>
        </div>
      </div>
    </Modal>
  )
}

export default TermsOfUseModal
