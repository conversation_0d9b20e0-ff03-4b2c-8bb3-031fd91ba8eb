"use client"

import { InputLabel } from "@/app/shared/components"
import { useCooldown } from "@/hooks"
import { ClockIcon } from "@/Icons"
import { zodResolver } from "@hookform/resolvers/zod"
import { useState } from "react"
import { FormProvider, useForm } from "react-hook-form"
import { toast } from "sonner"
import { z } from "zod"
import { Loader, Modal } from "."
import { NumberOnlyInputController } from "../forms/controllers/text-field"
import { Button } from "../ui"

// OTP form schema
const otpVerificationSchema = z.object({
  code: z
    .string()
    .min(6, "Please enter the 6 digit code sent to your phone")
    .max(6, "OTP should be exactly 6 digits")
    .regex(/^\d{6}$/, "OTP must be 6 digits"),
})

type TOtpVerificationValues = z.infer<typeof otpVerificationSchema>

const defaultValues: TOtpVerificationValues = {
  code: "",
}

interface OtpVerificationModalProps {
  open: boolean
  onClose: () => void
  phoneNumber: string
  onVerify: (otp: string) => Promise<void>
  onResendOtp: () => Promise<void>
}

const OtpVerificationModal = ({
  open,
  onClose,
  phoneNumber,
  onVerify,
  onResendOtp,
}: OtpVerificationModalProps) => {
  const [isVerifying, setIsVerifying] = useState(false)
  const [resendOTPLoading, setResendOTPLoading] = useState(false)
  const { cooldown, startCooldown, isCooling, resetCooldown } = useCooldown(20)

  const methods = useForm<TOtpVerificationValues>({
    resolver: zodResolver(otpVerificationSchema),
    defaultValues,
    mode: "onChange",
    criteriaMode: "all",
  })

  const handleVerify = async () => {
    try {
      // Get the OTP code from the form
      const data = methods.getValues()

      // Validate the form
      const isValid = await methods.trigger()
      if (!isValid) {
        return // Don't proceed if validation fails
      }

      setIsVerifying(true)
      await onVerify(data.code)
      methods.reset()
      onClose()
    } catch (error: Error | unknown) {
      console.error("OTP verification error:", error)
      const errorMessage =
        error instanceof Error ? error.message : "Oh no! Something went wrong."
      toast.error(errorMessage)
    } finally {
      setIsVerifying(false)
    }
  }

  const handleResendOtp = async () => {
    startCooldown()
    try {
      setResendOTPLoading(true)
      await onResendOtp()
    } catch (error) {
      console.error("Error resending OTP:", error)
      const errorMessage =
        error instanceof Error ? error.message : "Oh no! Something went wrong."
      toast.error(errorMessage)
      resetCooldown()
    } finally {
      setResendOTPLoading(false)
    }
  }

  return (
    <Modal maxWidth='xs' open={open} onClose={onClose}>
      <div>
        <div className='~mb-6/8'>
          <h2 className='font-medium ~text-lg/2xl ~pr-8/10'>
            Enter OTP to verify your <br /> phone number
          </h2>
          <div className='flex items-center gap-2 rounded-md bg-blue-50 text-sm font-medium text-blue-600 ~mt-4/5 ~mb-5/8 ~px-3/5 ~py-2/2.5'>
            <ClockIcon className='flex-shrink-0 rotate-180 text-[1.2em]' />
            <span>OTP sent to {phoneNumber}. Please check your messages.</span>
          </div>
        </div>
        <FormProvider {...methods}>
          <form onSubmit={(e) => e.preventDefault()}>
            <div className='grid ~gap-2/3'>
              <div>
                <InputLabel label='OTP' id='code' />
                <NumberOnlyInputController
                  name='code'
                  placeholder='Enter 6-digit OTP'
                  maxLength={6}
                />
              </div>
              <div className='flex justify-end'>
                <button
                  type='button'
                  disabled={isCooling || resendOTPLoading}
                  onClick={handleResendOtp}
                  className='flex min-w-20 items-center justify-center text-sm uppercase text-primary hover:underline disabled:cursor-not-allowed disabled:opacity-50'
                >
                  {!resendOTPLoading ? (
                    isCooling ? (
                      "Resend OTP in " + cooldown + " seconds"
                    ) : (
                      "Resend OTP"
                    )
                  ) : (
                    <Loader size={15} />
                  )}
                </button>
              </div>
            </div>
            <div className='flex w-full justify-end gap-4 ~mt-6/8'>
              <Button
                size='sm'
                type='button'
                onClick={onClose}
                className='border border-primary bg-transparent !px-10 text-primary'
              >
                Cancel
              </Button>
              <Button
                size='sm'
                type='button'
                onClick={handleVerify}
                className='!px-10'
                disabled={isVerifying}
              >
                {isVerifying ? <Loader size={15} /> : "Verify"}
              </Button>
            </div>
          </form>
        </FormProvider>
      </div>
    </Modal>
  )
}

export default OtpVerificationModal
