"use client"
import { InputLabel } from "@/app/shared/components"
import { sendOtp, verifyOtp } from "@/app/shared/services/otp/otp.service"
import {
  checkForVerifiedPhone,
  getUserInfo,
  storeVerifiedPhone,
} from "@/app/shared/utils/phone-verification"
import { useCooldown } from "@/hooks"
import { ChecklistIcon, ClockIcon, CustomerSupportIcon } from "@/Icons"
import { zodResolver } from "@hookform/resolvers/zod"
import { useEffect, useState } from "react"
import { Controller, FormProvider, useForm } from "react-hook-form"
import { toast } from "sonner"
import { z } from "zod"
import { Loader, Modal } from "."
import {
  EmailInputController,
  NumberOnlyInputController,
  TextOnlyInputController,
} from "../forms/controllers/text-field"
import { Button } from "../ui"
import TermsOfUseModal from "./TermsOfUseModal"

// User info form schema
const userInfoSchema = z.object({
  name: z
    .string()
    .min(3, "Name must be at least 3 characters")
    .max(50, "Name must not exceed 50 characters")
    .refine((name) => /^[A-Za-z\s.'-]+$/.test(name), {
      message:
        "Name should only contain letters, spaces, and characters like . ' -",
    }),
  email: z
    .string()
    .min(1, "Email is required")
    .email("Invalid email format")
    .max(100, "Email must not exceed 100 characters"),
  phone: z
    .string()
    .min(1, "Phone number is required")
    .refine(
      (phone) => {
        // Remove any non-digit characters and check if it's exactly 10 digits
        return phone.replace(/\D/g, "").length === 10
      },
      { message: "Please enter a valid 10-digit phone number" },
    ),
  isPhoneVerified: z.boolean().default(false),
  consent: z.boolean().refine((val) => val === true, {
    message: "You must agree to the terms of use to proceed.",
  }),
})

type TUserInfoValues = z.infer<typeof userInfoSchema>

const defaultValues: TUserInfoValues = {
  name: "",
  email: "",
  phone: "",
  isPhoneVerified: false,
  consent: false,
}

interface UserInfoModalProps {
  open: boolean
  onClose: () => void
  onSubmit: (data: TUserInfoValues) => void
}

const UserInfoModal = ({ open, onClose, onSubmit }: UserInfoModalProps) => {
  const [isVerifying, setIsVerifying] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showOtpInput, setShowOtpInput] = useState(false)
  const [otp, setOtp] = useState("")
  const [resendOTPLoading, setResendOTPLoading] = useState(false)
  const [termsModalOpen, setTermsModalOpen] = useState(false)
  const { cooldown, startCooldown, isCooling, resetCooldown } = useCooldown(20)

  const methods = useForm<TUserInfoValues>({
    resolver: zodResolver(userInfoSchema),
    defaultValues,
    mode: "onChange",
    criteriaMode: "all",
  })

  const { handleSubmit, formState, watch, setValue, control } = methods
  const { isValid } = formState
  const values = watch()

  const handleVerifyPhone = async () => {
    let phoneNumber = values.phone
    if (!phoneNumber) {
      toast.error("Please enter a phone number")
      return
    }
    phoneNumber = phoneNumber.replace(/\D/g, "")
    if (phoneNumber.length !== 10) {
      toast.error("Please enter a valid 10-digit phone number")
      return
    }
    setValue("phone", phoneNumber, { shouldValidate: true })
    try {
      setIsVerifying(true)
      toast.loading("Sending OTP...", { id: "send-otp" })
      const result = await sendOtp(phoneNumber)
      toast.dismiss("send-otp")
      if (result.success) {
        toast.success("OTP sent successfully", { id: "send-otp-success" })
        setShowOtpInput(true)
        startCooldown()
      } else {
        toast.error(result.message || "Failed to send OTP.", {
          id: "send-otp",
        })
      }
    } catch (error) {
      console.error("Error sending OTP:", error)
      toast.error("Failed to send OTP. Please try again.", { id: "send-otp" })
    } finally {
      setIsVerifying(false)
    }
  }

  const handleVerifyOtp = async () => {
    let phoneNumber = values.phone
    if (!phoneNumber) {
      toast.error("Phone number is missing")
      return
    }
    phoneNumber = phoneNumber.replace(/\D/g, "")
    if (!otp || otp.length !== 6 || !/^\d+$/.test(otp)) {
      toast.error("Please enter a valid 6-digit OTP")
      return
    }
    try {
      setIsVerifying(true)
      toast.loading("Verifying OTP...", { id: "verify-otp" })
      const result = await verifyOtp(phoneNumber, otp)
      if (result.isVerified) {
        setValue("isPhoneVerified", true, { shouldValidate: true })
        toast.success("Phone number verified successfully", {
          id: "verify-otp",
        })
        // Store verification status and user info in sessionStorage
        const userInfo = {
          name: values.name,
          email: values.email,
          phone: phoneNumber,
        }
        storeVerifiedPhone(phoneNumber, userInfo)
      } else {
        toast.error(result.message || "Invalid OTP. Please try again.", {
          id: "verify-otp",
        })
      }
    } catch (error) {
      console.error("Error verifying OTP:", error)
      toast.error("Failed to verify OTP. Please try again.", {
        id: "verify-otp",
      })
    } finally {
      setIsVerifying(false)
    }
  }

  const handleResendOtp = async () => {
    startCooldown()
    try {
      setResendOTPLoading(true)
      const phoneNumber = values.phone.replace(/\D/g, "")
      const result = await sendOtp(phoneNumber)
      if (result.success) {
        toast.success("OTP resent successfully")
      } else {
        toast.error(result.message || "Failed to resend OTP.")
        resetCooldown()
      }
    } catch (error) {
      console.error("Error resending OTP:", error)
      const errorMessage =
        error instanceof Error ? error.message : "Oh no! Something went wrong."
      toast.error(errorMessage)
      resetCooldown()
    } finally {
      setResendOTPLoading(false)
    }
  }

  const handleFormSubmit = (data: TUserInfoValues) => {
    setIsSubmitting(true)
    try {
      onSubmit(data)
      methods.reset()
      setShowOtpInput(false)
      setOtp("")
    } catch (error) {
      console.error("Form submission error:", error)
      const errorMessage =
        error instanceof Error ? error.message : "Oh no! Something went wrong."
      toast.error(errorMessage)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Check if any phone has been verified in sessionStorage when the modal opens
  useEffect(() => {
    if (open) {
      // Use the utility function to check if any phone is verified
      const isAnyPhoneVerified = checkForVerifiedPhone()
      if (isAnyPhoneVerified) {
        // Mark the current phone as verified without needing the specific number
        setValue("isPhoneVerified", true, { shouldValidate: true })
        // Get stored user info and populate the form
        const storedUserInfo = getUserInfo()
        if (storedUserInfo) {
          setValue("name", storedUserInfo.name, { shouldValidate: true })
          setValue("email", storedUserInfo.email, { shouldValidate: true })
          setValue("phone", storedUserInfo.phone, { shouldValidate: true })
        }
      }
    }
  }, [open, setValue])

  return (
    <Modal maxWidth='sm' open={open} onClose={onClose}>
      <div className='flex max-h-[70vh] min-h-[50vh] w-full flex-col'>
        <div className='mb-4'>
          <h2 className='flex items-center gap-2 font-medium ~text-lg/2xl ~pr-8/10'>
            <span className='rounded-full bg-primary/10 p-2 text-primary'>
              <CustomerSupportIcon className='~h-5/6 ~w-5/6' />
            </span>
            Enter your Details to Calculate Quote
          </h2>
          <p className='mt-3 text-gray-500 ~text-xs/sm md:mt-2'>
            Provide your details for an immediate, personalized quote. Our
            system calculates instantly to give you the most accurate estimate.
          </p>
        </div>
        <FormProvider {...methods}>
          <form
            onSubmit={handleSubmit(handleFormSubmit)}
            className='flex flex-1 flex-col overflow-hidden'
          >
            {/* Scrollable content area */}
            <div className='flex-1 overflow-y-auto pb-4 pr-2'>
              <div className='grid ~gap-3/4'>
                <div>
                  <InputLabel label='Full Name' id='name' />
                  <TextOnlyInputController
                    name='name'
                    placeholder='Enter Full Name'
                  />
                </div>
                <div>
                  <InputLabel label='Email' id='email' />
                  <EmailInputController
                    name='email'
                    placeholder='Enter Email Address'
                  />
                </div>
                <div>
                  <InputLabel label='Phone Number' id='phone' />
                  <div className='flex items-start gap-2'>
                    <NumberOnlyInputController
                      name='phone'
                      placeholder='Enter Phone Number'
                      disabled={values.isPhoneVerified}
                      maxLength={10}
                    />
                    {!showOtpInput && (
                      <Button
                        size='sm'
                        onClick={handleVerifyPhone}
                        disabled={
                          isVerifying ||
                          !values.phone ||
                          values.phone.length !== 10 ||
                          isCooling ||
                          !values.consent
                        }
                        type='button'
                        className={`whitespace-nowrap !px-3 !py-1.5 ${
                          showOtpInput
                            ? "border border-primary bg-transparent text-primary"
                            : ""
                        }`}
                      >
                        {isVerifying ? (
                          <Loader size={15} />
                        ) : showOtpInput ? (
                          "Resend OTP"
                        ) : (
                          "Send OTP"
                        )}
                      </Button>
                    )}
                    {values.isPhoneVerified && (
                      <div className='flex items-center gap-1 text-green-600'>
                        <ChecklistIcon className='~h-4/5 ~w-4/5' />
                        <span className='text-xs'>Verified</span>
                      </div>
                    )}
                  </div>
                </div>
                {showOtpInput && !values.isPhoneVerified && (
                  <div>
                    <div className='flex items-center gap-2 rounded-md bg-blue-50 text-sm font-medium text-blue-600 ~mb-2/3 ~px-3/5 ~py-2/2.5'>
                      <ClockIcon className='flex-shrink-0 rotate-180 text-[1.2em]' />
                      <span>
                        OTP sent to {values.phone}. Please check your messages.
                      </span>
                    </div>
                    <div className='flex flex-col gap-2'>
                      <div className='flex items-center gap-2'>
                        <input
                          type='text'
                          value={otp}
                          onChange={(e) =>
                            setOtp(
                              e.target.value.replace(/\D/g, "").slice(0, 6),
                            )
                          }
                          placeholder='Enter 6-digit OTP'
                          className='flex-1 rounded-md border border-gray-300 px-3 py-1.5 text-left font-normal tracking-wider'
                          maxLength={6}
                          autoComplete='one-time-code'
                        />
                        <Button
                          size='sm'
                          onClick={handleVerifyOtp}
                          disabled={isVerifying || otp.length !== 6}
                          type='button'
                          className='whitespace-nowrap !px-3 !py-1.5'
                        >
                          {isVerifying ? <Loader size={15} /> : "Verify"}
                        </Button>
                      </div>
                      <div className='flex items-center'>
                        <button
                          type='button'
                          disabled={isCooling || resendOTPLoading || !values.consent}
                          onClick={handleResendOtp}
                          className='flex items-center gap-1 text-sm text-primary hover:underline disabled:cursor-not-allowed disabled:opacity-50'
                        >
                          {resendOTPLoading && <Loader size={12} />}
                          {isCooling ? (
                            <span className='flex items-center gap-1'>
                              <ClockIcon className='~h-3/4 ~w-3/4' />
                              Resend OTP in {cooldown} seconds
                            </span>
                          ) : (
                            "Resend OTP"
                          )}
                        </button>
                      </div>
                    </div>
                  </div>
                )}
                <div className='flex items-start justify-start gap-2'>
                  <Controller
                    name='consent'
                    control={control}
                    render={({ field }) => (
                      <input
                        type='checkbox'
                        id='consent'
                        checked={field.value}
                        onChange={(e) => field.onChange(e.target.checked)}
                        className='h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary'
                      />
                    )}
                  />
                  <div className='text-start text-gray-600 ~text-xs/sm'>
                    By proceeding, you agree to our
                    <button
                      type='button'
                      onClick={() => setTermsModalOpen(true)}
                      className='ml-1 underline hover:text-primary'
                    >
                      Terms of Use
                    </button>
                  </div>
                </div>
              </div>
            </div>
            {/* Fixed footer with buttons */}
            <div className='sticky bottom-0 mt-4 flex w-full justify-end gap-4 bg-white pt-4'>
              <Button
                size='sm'
                type='button'
                onClick={onClose}
                className='border border-primary bg-transparent !px-10 text-primary'
              >
                Cancel
              </Button>
              <Button
                size='sm'
                type='submit'
                className='!px-10'
                disabled={!isValid || !values.isPhoneVerified || isSubmitting}
              >
                {isSubmitting ? <Loader size={15} /> : "Calculate"}
              </Button>
            </div>
          </form>
        </FormProvider>
      </div>

      {/* Terms of Use Modal */}
      <TermsOfUseModal
        open={termsModalOpen}
        onClose={() => setTermsModalOpen(false)}
        onAccept={() => {
          setValue("consent", true, { shouldValidate: true })
          setTermsModalOpen(false)
        }}
      />
    </Modal>
  )
}

export default UserInfoModal
