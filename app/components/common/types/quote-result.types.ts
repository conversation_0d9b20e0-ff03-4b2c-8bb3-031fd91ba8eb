// Line item types (matching the backend enum)
export enum EstimationLineItemType {
  Primary = 1,
  Secondary = 2,
  Tertiary = 3,
  Information = 4,
}

// Value types
export enum ValueType {
  Currency = 1,
  Decimal = 2,
  Integer = 3,
}

// Define types for the response data
export type LineItem = {
  name: string
  description: string | null
  value: number
  textValue?: string | null
  estimationType: 1 | 2 | 3
  lineItemType: 1 | 2 | 3 | 4 // 1: Primary, 2: Secondary, 3: Tertiary, 4: Information
  isRecommendedValue: boolean
  isEditable: boolean
  requestPropertyName?: string | null
  requestPropertyDataType?: "text" | "number" | null
  showTextValue?: boolean
}

export type Vehicle = {
  id: number
  name: string
  length: number
  width: number
  height: number
  cft: number
  minimumCost: number
  allowPartLoad: boolean
  imageUrls: string[]
}

export type Estimation = {
  name: string
  lineItems: LineItem[]
  totalCost: number
  insurance: number
  vehicle?: Vehicle
}

export type QuoteResult = {
  estimations: Estimation[]
}

export type FormValues = {
  [key: string]: unknown
}

export type FieldConfig = {
  stepValue: number
  maxValue: number
}

export type FieldConfigs = {
  [key: string]: FieldConfig
}

export type QuoteResultDetailsProps = {
  result: QuoteResult
  onRecalculate?: (updatedFormValues: FormValues) => void
  stepValue?: number
  formValues?: FormValues
  fieldConfigs?: FieldConfigs
  serviceName?: string
  onFormValueChange?: (field: string, value: string) => void
  activeEstimationIndex?: number
  onEstimationChange?: (index: number) => void
}
