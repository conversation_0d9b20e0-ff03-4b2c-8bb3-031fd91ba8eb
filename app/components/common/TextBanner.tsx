import { cn } from "@/app/shared/utils"

type TTextBanner = {
  text: string
  className?: string
}

const TextBanner = ({ text, className }: TTextBanner) => {
  return (
    <div className='bg-gradient-to-r from-primary to-secondary px-5 ~py-5/10'>
      <p
        aria-label={text}
        className={cn(
          "text-center font-semibold text-white ~text-sm/lg",
          className,
        )}
      >
        {text}
      </p>
    </div>
  )
}

export default TextBanner
