import {
  EstimationLineItemType,
  LineItem,
  ValueType,
} from "../types/quote-result.types"
import { formatCurrency } from "@/app/shared/utils"

//  * Get styles for a line item based on its type

export const getItemStyles = (lineItemType: EstimationLineItemType) => {
  switch (lineItemType) {
    case EstimationLineItemType.Primary:
      return "font-medium text-sm"
    case EstimationLineItemType.Secondary:
      return "font-thin text-sm text-gray-600"
    case EstimationLineItemType.Tertiary:
      return "font-thin text-sm text-gray-500"
    case EstimationLineItemType.Information:
      return "font-thin text-xs text-gray-400 italic"
    default:
      return "text-sm text-gray-700"
  }
}

//  * Get background styles for a line item based on its type

export const getBackgroundStyles = (lineItemType: EstimationLineItemType) => {
  switch (lineItemType) {
    case EstimationLineItemType.Primary:
      return "bg-blue-50/70"
    case EstimationLineItemType.Secondary:
      return "bg-gray-100/70"
    case EstimationLineItemType.Tertiary:
      return "bg-gray-50/70"
    case EstimationLineItemType.Information:
      return "bg-white"
    default:
      return "bg-white"
  }
}

//  * Format a value based on its type

export const formatValue = (
  value: number,
  estimationType: ValueType,
): string => {
  switch (estimationType) {
    case ValueType.Currency:
      return formatCurrency(value)
    case ValueType.Decimal:
      return value.toString()
    case ValueType.Integer:
      return value.toString()
    default:
      return value.toString()
  }
}

/**
 * Extract field name from a line item
 */
export const getFieldNameFromItem = (item: LineItem): string | null => {
  // Only use requestPropertyName, no hardcoded mappings
  return item.requestPropertyName || null
}
