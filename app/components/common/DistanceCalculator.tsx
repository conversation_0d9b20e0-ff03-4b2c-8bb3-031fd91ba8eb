"use client"

import { InputLabel } from "@/app/shared/components"
import { ClockIcon, CloseIcon } from "@/Icons"
import { calculateDistance } from "@/lib/google-map-api"
import { useState } from "react"
import { InputAutoComplete } from "."
import { Button } from "../ui"

interface DistanceCalculatorProps {
  onDistanceCalculated?: (
    distance: string | null,
    locations: {
      pickup: google.maps.places.PlaceResult | null
      dropoff: google.maps.places.PlaceResult | null
    },
  ) => void
  initialPickup?: string
  initialDropoff?: string
}

const DistanceCalculator = ({
  onDistanceCalculated,
  initialPickup = "",
  initialDropoff = "",
}: DistanceCalculatorProps) => {
  const [pickup, setPickup] = useState(initialPickup)
  const [dropoff, setDropoff] = useState(initialDropoff)
  const [selectedPickupLocation, setSelectedPickupLocation] =
    useState<google.maps.places.PlaceResult | null>(null)
  const [selectedDropoffLocation, setSelectedDropoffLocation] =
    useState<google.maps.places.PlaceResult | null>(null)
  const [distance, setDistance] = useState<string | null>(null)
  const [isCalculating, setIsCalculating] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Calculate distance when both locations are selected
  const handleCalculateDistance = async () => {
    if (!selectedPickupLocation || !selectedDropoffLocation) {
      setError("Please select both pickup and dropoff locations")
      return
    }

    try {
      setIsCalculating(true)
      setError(null)
      const calculatedDistance = await calculateDistance(
        selectedPickupLocation,
        selectedDropoffLocation,
      )
      setDistance(calculatedDistance)

      if (onDistanceCalculated) {
        onDistanceCalculated(calculatedDistance, {
          pickup: selectedPickupLocation,
          dropoff: selectedDropoffLocation,
        })
      }
    } catch (error) {
      setError("Failed to calculate distance. Please try again.")
      console.error("Error calculating distance:", error)
    } finally {
      setIsCalculating(false)
    }
  }

  return (
    <div className='space-y-4'>
      <div>
        <InputLabel label='Pickup Location' id='pickup' />
        <InputAutoComplete
          onChange={(place) => {
            setSelectedPickupLocation(place || null)
            setPickup(place?.formatted_address || "")
          }}
        >
          <input
            type='text'
            id='pickup'
            value={pickup}
            onChange={(e) => setPickup(e.target.value)}
            placeholder='Enter pickup location'
            className='w-full rounded-md border border-gray-300 p-2 focus:outline-none focus:ring-2 focus:ring-blue-500'
          />
        </InputAutoComplete>
      </div>

      <div>
        <InputLabel label='Dropoff Location' id='dropoff' />
        <InputAutoComplete
          onChange={(place) => {
            setSelectedDropoffLocation(place || null)
            setDropoff(place?.formatted_address || "")
          }}
        >
          <input
            type='text'
            id='dropoff'
            value={dropoff}
            onChange={(e) => setDropoff(e.target.value)}
            placeholder='Enter dropoff location'
            className='w-full rounded-md border border-gray-300 p-2 focus:outline-none focus:ring-2 focus:ring-blue-500'
          />
        </InputAutoComplete>
      </div>

      <Button
        onClick={handleCalculateDistance}
        disabled={isCalculating || !pickup || !dropoff}
        className='w-full'
      >
        {isCalculating ? "Calculating..." : "Calculate Distance"}
      </Button>

      {error && (
        <div className='flex items-center gap-2 rounded-md bg-red-50 px-3 py-2 text-sm font-medium text-red-600'>
          <CloseIcon className='flex-shrink-0 text-[1.2em]' />
          <span>{error}</span>
        </div>
      )}

      {distance && (
        <div className='flex items-center gap-2 rounded-md bg-primary/10 px-3 py-2 text-sm font-medium text-primary'>
          <ClockIcon className='flex-shrink-0 rotate-180 text-[1.2em]' />
          <p>
            Distance between locations:{" "}
            <span className='text-base font-bold'>{distance}</span>
          </p>
        </div>
      )}
    </div>
  )
}

export default DistanceCalculator
