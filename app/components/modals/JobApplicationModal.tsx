"use client"

import { Modal } from "@/app/components/common"
import JobApplicationForm from "@/app/components/forms/JobApplicationForm"
import { JobPosting } from "@/app/shared/services/job-application"

interface JobApplicationModalProps {
  isOpen: boolean
  onClose: () => void
  jobPosting: JobPosting
}

export default function JobApplicationModal({
  isOpen,
  onClose,
  jobPosting,
}: JobApplicationModalProps) {
  return (
    <Modal open={isOpen} onClose={onClose} maxWidth='lg' fullWidth={true}>
      <div className='relative h-full w-full'>
        <h2 className='mb-4 text-xl font-bold text-gray-900'>
          Apply for: {jobPosting.jobPostingTitle}
        </h2>

        <div className='h-full w-full overflow-hidden'>
          <JobApplicationForm
            jobPosting={jobPosting}
            onSuccess={onClose}
            isModal={true}
          />
        </div>
      </div>
    </Modal>
  )
}
