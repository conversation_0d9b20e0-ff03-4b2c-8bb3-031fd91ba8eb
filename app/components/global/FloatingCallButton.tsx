"use client"

import { FOOTER_CONTACT_INFO } from "@/config"
import { useEffect, useState } from "react"

const FloatingCallButton = () => {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const toggleVisibility = () => {
      // Show button after scrolling 300px
      if (window.pageYOffset > 300) {
        setIsVisible(true)
      } else {
        setIsVisible(false)
      }
    }

    window.addEventListener("scroll", toggleVisibility)
    return () => window.removeEventListener("scroll", toggleVisibility)
  }, [])

  const handleCall = () => {
    window.open(`tel:${FOOTER_CONTACT_INFO.phone}`, "_self")
  }

  return (
    <div
      className={`fixed bottom-20 right-6 z-50 transition-all duration-300 ease-in-out md:bottom-24 md:right-7 ${
        isVisible ? "translate-y-0 opacity-100" : "translate-y-16 opacity-0"
      }`}
    >
      {/* Direct Call Button */}
      <button
        onClick={handleCall}
        className='group relative flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-r from-green-500 to-green-600 shadow-lg transition-all duration-300 hover:scale-110 hover:shadow-xl focus:outline-none focus:ring-4 focus:ring-green-300 active:scale-95 md:h-14 md:w-14'
        aria-label={`Call ${FOOTER_CONTACT_INFO.phone}`}
        title='Call us for immediate assistance'
      >
        {/* Phone Icon */}
        <svg
          className='h-5 w-5 text-white transition-transform duration-300 group-hover:rotate-12 md:h-6 md:w-6'
          fill='currentColor'
          viewBox='0 0 20 20'
          xmlns='http://www.w3.org/2000/svg'
        >
          <path d='M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z' />
        </svg>

        {/* Pulse Animation */}
        <div className='absolute -inset-1 animate-ping rounded-full bg-green-400 opacity-20'></div>

        {/* Tooltip - hidden on mobile, shown on desktop */}
        <div className='absolute bottom-full right-0 mb-2 hidden w-max rounded-md bg-gray-800 px-3 py-1 text-sm text-white shadow-lg md:group-hover:block'>
          Call {FOOTER_CONTACT_INFO.phone}
          <div className='absolute left-1/2 top-full h-0 w-0 -translate-x-1/2 border-4 border-transparent border-t-gray-800'></div>
        </div>
      </button>
    </div>
  )
}

export default FloatingCallButton
