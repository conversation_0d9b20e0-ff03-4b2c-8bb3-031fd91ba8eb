"use client"

import { useEffect, useState } from "react"

const BackToTop = () => {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > 300) {
        setIsVisible(true)
      } else {
        setIsVisible(false)
      }
    }

    window.addEventListener("scroll", toggleVisibility)

    return () => window.removeEventListener("scroll", toggleVisibility)
  }, [])

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    })
  }

  if (!isVisible) {
    return null
  }

  return (
    <button
      onClick={scrollToTop}
      className='flex items-center gap-2 rounded-md bg-zinc-100 px-3 py-2 text-zinc-600 transition-all duration-200 ~text-xs/sm hover:bg-zinc-200 hover:text-zinc-900'
      aria-label='Back to top'
    >
      <span>Back to Top</span>
      <span>↑</span>
    </button>
  )
}

export default BackToTop
