"use client"

import { EnhancedNavigation } from "@/app/shared/components"
import { cn } from "@/app/shared/utils"
import { useHasScroll } from "@/hooks"
import Image from "next/image"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useEffect, useState } from "react"
import { ButtonLink } from "../ui"
import MobileNavDrawer from "./MobileNavDrawer"

const excludeHeaderUrls = [
  "/login",
  "/register",
  "/forgot-password",
  "/marketplace/", // This will hide header for all marketplace routes
]

const Header = () => {
  const [isClient, setIsClient] = useState(false)
  const [isHidden, setIsHidden] = useState(false)
  const hasScroll = useHasScroll()
  const pathname = usePathname()

  // Exclude header for specific URLs
  useEffect(() => {
    if (typeof window !== "undefined") {
      const shouldHideHeader = excludeHeaderUrls.some((url) =>
        pathname?.startsWith(url),
      )

      setIsHidden(shouldHideHeader)
    }
  }, [pathname])

  useEffect(() => {
    setIsClient(true)
  }, [])

  return (
    <div
      className={cn(
        "fixed left-0 top-0 z-20 w-full",
        isHidden ? "hidden" : "block",
      )}
    >
      <div
        className={cn(
          "flex items-center py-3 text-white transition-colors duration-300 ~gap-5/10 ~px-4/16",
          isClient && hasScroll && "bg-white shadow-md",
        )}
      >
        <Link
          className='flex-shrink-0 bg-transparent'
          href='/'
          aria-label='NexMove Home - Professional Moving & Logistics Services'
          title='Return to NexMove Homepage'
        >
          {/* <LogoIcon
            className={cn(
              "transition-colors ~w-20/24",
              isClient && hasScroll && "text-black",
            )}
          /> */}
          <Image
            src={
              isClient && hasScroll
                ? "/svgs/logo-black.svg"
                : "/svgs/logo-white.svg"
            }
            alt='NexMove Logo'
            width={300}
            height={130}
            className='object-contain ~w-16/20'
            priority
          />
        </Link>
        <EnhancedNavigation
          variant='header'
          hasScroll={hasScroll}
          isClient={isClient}
        />
        <ul className='flex items-center gap-3'>
          <li>
            <ButtonLink
              size='sm'
              href='/request-quote'
              className={cn(
                "block rounded-[4px] bg-white px-5 py-2.5 font-semibold text-black",
                hasScroll && isClient && "bg-zinc-100",
              )}
              aria-label='Get instant moving quote'
              title='Get free instant quote for moving services'
            >
              Request Quote
            </ButtonLink>
          </li>
          <li className='max-sm:hidden'>
            <ButtonLink
              size='sm'
              href='/contact'
              className='block rounded-[4px] bg-primary px-5 py-2.5 font-semibold text-white'
              aria-label='Contact NexMove for inquiries'
              title='Contact us for logistics and moving services'
            >
              Contact Us
            </ButtonLink>
          </li>
        </ul>
        {/* Mobile Navigation Drawer - Show only on mobile screens */}
        <div className='lg:hidden'>
          {isClient && <MobileNavDrawer hasScrolled={isClient && hasScroll} />}
        </div>
      </div>
    </div>
  )
}

export default Header
