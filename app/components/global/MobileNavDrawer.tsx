"use client"

import { cn } from "@/app/shared/utils"
import { navList, TNavList } from "@/config"
import { useBoolean } from "@/hooks"
import { CloseIcon, MenuIcon } from "@/Icons"
import Drawer from "@mui/material/Drawer"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useEffect } from "react"

const MobileNavDrawer = ({ hasScrolled }: { hasScrolled: boolean }) => {
  const pathname = usePathname()

  const { value: open, onToggle, onFalse, setValue } = useBoolean()

  useEffect(() => {
    setValue(false)
  }, [pathname, setValue])

  return (
    <div className='grid place-content-center'>
      <button aria-label='Opne Menu' onClick={() => onToggle()}>
        <MenuIcon
          className={cn(
            "size-7 transition-colors",
            hasScrolled && "text-black",
          )}
        />
      </button>
      <Drawer open={open} anchor='right' onClose={onFalse}>
        <button
          aria-label='Close Menu'
          onClick={() => onToggle()}
          className='ml-auto block p-3 text-black'
        >
          <CloseIcon className='size-6' />
        </button>
        <div className='w-64 divide-y-[1px] p-4'>
          {navList.map((item: TNavList) => (
            <Link
              href={item.path}
              key={item.value}
              className={cn(
                "block py-3 font-medium",
                pathname === item.path && "text-primary",
              )}
            >
              {item.title}
            </Link>
          ))}
        </div>
      </Drawer>
    </div>
  )
}

export default MobileNavDrawer
