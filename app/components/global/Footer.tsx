import {
  FOOTER_COMPANY_INFO,
  FOOTER_CONTACT_INFO,
  FOOTER_SECTIONS,
} from "@/config"
import Image from "next/image"
import Link from "next/link"
import BackToTop from "./BackToTop"
import FloatingCallButton from "./FloatingCallButton"
import SocialLinks from "./SocialLinks"

const Footer = () => {
  return (
    <>
      <footer className='border-t border-zinc-200 bg-gradient-to-t from-zinc-50 to-white'>
        <div className='container mx-auto ~px-4/8 ~py-8/12'>
          {/* Main Footer Content */}
          <div className='grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-12 lg:gap-8'>
            {/* Brand Section */}
            <div className='space-y-6 lg:col-span-4'>
              <div className='flex items-center ~gap-2/3'>
                <Image
                  src={"/svgs/logo-black.svg"}
                  alt='NexMove Logo'
                  width={300}
                  height={130}
                  className='object-contain ~w-20/24'
                  priority
                />
              </div>
              <p className='max-w-sm leading-relaxed text-zinc-600 ~text-sm/base'>
                {FOOTER_COMPANY_INFO.description}
              </p>

              {/* Contact Info */}
              <div className='space-y-2 text-zinc-600 ~text-sm/base'>
                <div className='flex items-center gap-2'>
                  <span>📧</span>
                  <a
                    href={`mailto:${FOOTER_CONTACT_INFO.email}`}
                    className='transition-colors duration-200 hover:text-primary'
                    aria-label={`Send email to ${FOOTER_CONTACT_INFO.email}`}
                    title='Contact NexMove via email'
                  >
                    {FOOTER_CONTACT_INFO.email}
                  </a>
                </div>
                <div className='flex items-center gap-2'>
                  <span>📞</span>
                  <a
                    href={`tel:${FOOTER_CONTACT_INFO.phone}`}
                    className='transition-colors duration-200 hover:text-primary'
                    aria-label={`Call ${FOOTER_CONTACT_INFO.phone}`}
                    title='Call NexMove for immediate assistance'
                  >
                    {FOOTER_CONTACT_INFO.phone}
                  </a>
                </div>
                <div className='flex items-start gap-2'>
                  <span>📍</span>
                  <span>{FOOTER_CONTACT_INFO.address}</span>
                </div>
              </div>

              {/* Social Links */}
              <div>
                <h4 className='mb-3 font-semibold text-zinc-900 ~text-sm/base'>
                  Follow Us
                </h4>
                <SocialLinks />
              </div>
            </div>

            {/* Links Sections */}
            <div className='grid grid-cols-2 gap-6 lg:col-span-8 lg:grid-cols-4 lg:gap-6'>
              {FOOTER_SECTIONS.map((section, sectionIndex) => (
                <div key={sectionIndex} className='space-y-4'>
                  <h3 className='border-b border-zinc-200 pb-2 font-semibold text-zinc-900 ~text-sm/base'>
                    {section.title}
                  </h3>
                  <ul className='space-y-3'>
                    {section.links.map((link, linkIndex) => (
                      <li key={linkIndex}>
                        <Link
                          href={link.url}
                          className='text-zinc-600 transition-colors duration-200 ~text-xs/sm hover:text-primary hover:underline'
                          aria-label={`Visit ${link.label} page`}
                          title={`Learn more about ${link.label}`}
                        >
                          {link.label}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>

          <p className='container mx-auto font-normal !leading-tight tracking-wide text-zinc-600 ~text-xs/base ~mt-8/10'>
            <b className='font-bold'>Disclaimer:</b> “Nex Move” is a brand
            legally owned by Hallef Technologies Private Limited. All rights
            associated with the brand, including its logo, content, and
            services, are the sole property of Hallef Technologies Private
            Limited.
          </p>

          {/* Improved Bottom Section */}
          <div className='border-t border-zinc-200 ~mt-8/10 ~pt-6/8'>
            <div className='flex flex-col items-center justify-between gap-6 lg:flex-row'>
              {/* Left Side - Company Info */}
              <div className='flex flex-1 flex-col items-center gap-2 lg:items-start'>
                <div className='flex items-center gap-2 text-zinc-600 ~text-xs/sm'>
                  <span>{FOOTER_COMPANY_INFO.copyright}</span>
                </div>
              </div>

              {/* Center - Quick Links */}
              <div className='flex items-center justify-center gap-2 text-zinc-500 ~text-xs/sm'>
                <span>{FOOTER_COMPANY_INFO.tagline}</span>
              </div>

              {/* Right Side - Back to Top */}
              <div className='flex flex-1 items-center justify-end'>
                <BackToTop />
              </div>
            </div>
          </div>
        </div>

        {/* Floating call button */}
        <FloatingCallButton />
      </footer>
    </>
  )
}

export default Footer
