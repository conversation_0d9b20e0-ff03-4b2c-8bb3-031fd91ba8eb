import Link from "next/link"
import { FC } from "react"
import { SOCIAL_LINKS, TSocialLink } from "../../../config/general"

const SocialLinks: FC = () => (
  <div className='mt-4 flex gap-4'>
    {SOCIAL_LINKS.map(({ url, icon: Icon }: TSocialLink, idx: number) => (
      <Link
        key={url}
        href={url}
        target='_blank'
        rel='noopener noreferrer'
        aria-label={`Social link ${idx + 1}`}
        className='transition hover:opacity-80'
      >
        <Icon className='h-6 w-6' />
      </Link>
    ))}
  </div>
)

export default SocialLinks
