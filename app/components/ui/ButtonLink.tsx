import { cn } from "@/app/shared/utils"
import Link from "next/link"

const ButtonLink = ({
  children,
  ...props
}: React.ComponentProps<typeof Link> & {
  size?: "sm" | "lg"
}) => {
  return (
    <Link
      {...props}
      className={cn(
        "block select-none rounded bg-primary text-center font-medium text-white transition-all duration-300",
        "hover:scale-[1.02] hover:shadow-md active:scale-100 active:shadow-none disabled:opacity-40",
        props.size === undefined && "~text-base/lg ~px-3/4 ~py-2/3",
        props.size === "sm" && "~text-sm/base ~px-3/4 ~py-2/3",
        props.size === "lg" && "~text-lg/xl ~px-3.5/5 ~py-2/4",
        props.className,
      )}
    >
      {children ?? "Button Link Text"}
    </Link>
  )
}

export default ButtonLink
