import { cn } from "@/app/shared/utils"
import type { ButtonHTMLAttributes, PropsWithChildren } from "react"

type ButtonProps = ButtonHTMLAttributes<HTMLButtonElement> & {
  size?: "sm" | "lg"
}

const Button = ({
  children,
  className,
  size,
  ...rest
}: PropsWithChildren<ButtonProps>) => {
  return (
    <button
      {...rest}
      className={cn(
        "select-none rounded bg-primary font-medium text-white transition-all duration-300",
        "hover:scale-[1.02] hover:shadow-md active:scale-100 active:shadow-none disabled:opacity-40",
        size === undefined && "~text-base/lg ~px-3/4 ~py-2.5/3",
        size === "sm" && "~text-sm/base ~px-3/4 ~py-2.5/3",
        size === "lg" && "~text-lg/xl ~px-3.5/5 ~py-2.5/4",
        className,
      )}
    >
      {children ?? "Button Text"}
    </button>
  )
}

export default Button
