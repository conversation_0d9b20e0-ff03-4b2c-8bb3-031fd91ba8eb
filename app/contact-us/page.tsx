import { SEOComponent } from "@/app/shared/components"
import { generateSEOMetadata, PAGE_SEO } from "@/app/shared/utils/seo"
import { BREADCRUMB_PATHS } from "@/app/shared/utils/seo-breadcrumbs"
import { ContactUsScreen } from "@/screens"

export const metadata = generateSEOMetadata({
  ...PAGE_SEO.CONTACT,
  url: "https://nex-move.com/contact",
})

const Page = () => {
  const breadcrumbs = [BREADCRUMB_PATHS.HOME, BREADCRUMB_PATHS.CONTACT]

  const localBusinessSchema = {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    name: "Nex-Move",
    description:
      "Professional moving and logistics services in India and globally",
    url: "https://nex-move.com",
    telephone: "+91-1800-419-5949",
    email: "<EMAIL>",
    address: {
      "@type": "PostalAddress",
      addressCountry: "IN",
      addressLocality: "Bangalore",
      addressRegion: "Karnataka",
      postalCode: "560005",
      streetAddress: "ITC Info Tech Park",
    },
    geo: {
      "@type": "GeoCoordinates",
      latitude: "12.9715987",
      longitude: "77.5945627",
    },
    openingHours: "Mo-Su 00:00-24:00",
    serviceArea: [
      {
        "@type": "Country",
        name: "India",
      },
      {
        "@type": "Place",
        name: "Worldwide",
      },
    ],
  }

  return (
    <>
      <SEOComponent
        breadcrumbs={breadcrumbs}
        structuredData={[localBusinessSchema]}
      />
      <ContactUsScreen />
    </>
  )
}

export default Page
