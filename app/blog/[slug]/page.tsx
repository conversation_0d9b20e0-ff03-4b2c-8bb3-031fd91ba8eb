import { sanityClient, urlFor } from "@/lib/sanity/client"
import { getPostBySlugQuery } from "@/lib/sanity/queries"
import { Post } from "@/lib/sanity/types"
import { notFound } from "next/navigation"
import { PortableText } from "@portabletext/react"
import Image from "next/image"
import { PageHeader } from "@/app/shared/components"

type TParams = Promise<{ slug: string[] }>

export default async function Page({ params }: { params: TParams }) {
  const { slug }: { slug: string[] } = await params

  const post: Post | null = await sanityClient.fetch(getPostBySlugQuery, {
    slug,
  })

  if (!post) return notFound()

  return (
    <>
      <PageHeader secondLine={post.title} />
      <article className='mx-auto max-w-2xl pb-10'>
        <Image
          src={urlFor(post.mainImage).url()}
          height={200}
          width={200}
          alt={post.title}
        />
        <h1 className='text-3xl font-bold'>{post.title}</h1>
        <p className='text-gray-500'>
          By {post.author.name} on {new Date(post.publishedAt).toDateString()}
        </p>
        <div className='prose mt-5'>
          <PortableText value={post.body} />
        </div>
      </article>
    </>
  )
}
