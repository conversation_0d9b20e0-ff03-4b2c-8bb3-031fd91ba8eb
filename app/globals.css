@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: var(--font-space-grotesk);
  font-weight: 400;
  overflow-x: clip;
  @apply leading-tight ~text-sm/base;
}

/* Custom Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 12px;
  background-color: theme("colors.gray.50");
  border-left: 1px solid theme("colors.gray.200");
}

::-webkit-scrollbar-thumb {
  background-color: theme("colors.gray.200");
  border: 2px solid theme("colors.gray.50");
  border-radius: theme("borderRadius.lg");
}

::-webkit-scrollbar-thumb:hover {
  background-color: theme("colors.gray.400");
}

::-webkit-scrollbar-track {
  background-color: theme("colors.gray.50");
}

img,
picture,
video,
svg {
  display: block;
  max-width: 100%;
}

/* For Material UI components specifically */
.MuiTypography-root,
.MuiInputBase-root,
.MuiMenuItem-root {
  font-family: var(--font-inter), sans-serif !important;
  font-size: 14px !important;
}

.MuiButton-root {
  font-size: 14px !important;
}

.MuiFormHelperText-root {
  font-family: var(--font-inter), sans-serif !important;
}
