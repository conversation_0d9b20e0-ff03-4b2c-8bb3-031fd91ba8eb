import { SEOComponent } from "@/app/shared/components"
import {
  generateSEOMetadata,
  generateServiceSchema,
  PAGE_SEO,
} from "@/app/shared/utils/seo"
import { BREADCRUMB_PATHS } from "@/app/shared/utils/seo-breadcrumbs"
import NexMoveStorageContainer from "../../containers/services/nexmove-storage/NexMoveStorageContainer"

export const metadata = generateSEOMetadata({
  ...PAGE_SEO.STORAGE,
  url: "https://nex-move.com/nexmove-storage",
})

const Page = () => {
  const breadcrumbs = [
    BREADCRUMB_PATHS.HOME,
    BREADCRUMB_PATHS.SERVICES,
    BREADCRUMB_PATHS.STORAGE,
  ]

  const serviceSchema = generateServiceSchema({
    name: "Storage & Warehousing Solutions",
    description:
      "Secure storage facilities with climate control, 24/7 security, and flexible storage plans. Professional warehousing solutions for all your needs.",
    url: "/nexmove-storage",
    area: [
      "India",
      "Delhi",
      "Mumbai",
      "Bangalore",
      "Chennai",
      "Kolkata",
      "Hyderabad",
      "Pune",
    ],
  })

  return (
    <>
      <SEOComponent
        breadcrumbs={breadcrumbs}
        structuredData={[serviceSchema]}
      />
      <NexMoveStorageContainer />
    </>
  )
}

export default Page
