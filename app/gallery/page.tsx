import { SEOComponent } from "@/app/shared/components"
import { generateSEOMetadata, PAGE_SEO } from "@/app/shared/utils/seo"
import { BREADCRUMB_PATHS } from "@/app/shared/utils/seo-breadcrumbs"
import { GalleryScreen } from "@/screens"

export const metadata = generateSEOMetadata({
  ...PAGE_SEO.GALLERY,
  url: "https://nex-move.com/gallery",
})

const Page = () => {
  const breadcrumbs = [BREADCRUMB_PATHS.HOME, BREADCRUMB_PATHS.GALLERY]

  return (
    <>
      <SEOComponent breadcrumbs={breadcrumbs} />
      <GalleryScreen />
    </>
  )
}

export default Page
