import { SEOComponent } from "@/app/shared/components"
import { generateSEOMetadata, PAGE_SEO } from "@/app/shared/utils/seo"
import { BREADCRUMB_PATHS } from "@/app/shared/utils/seo-breadcrumbs"
import { RequestQuoteScreen } from "@/screens"

export const metadata = generateSEOMetadata({
  ...PAGE_SEO.REQUEST_QUOTE,
  url: "https://nex-move.com/request-quote",
})

const Page = () => {
  const breadcrumbs = [BREADCRUMB_PATHS.HOME, BREADCRUMB_PATHS.REQUEST_QUOTE]

  return (
    <>
      <SEOComponent breadcrumbs={breadcrumbs} />
      <RequestQuoteScreen />
    </>
  )
}

export default Page
