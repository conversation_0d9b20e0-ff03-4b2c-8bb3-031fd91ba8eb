import { SEOComponent } from "@/app/shared/components"
import {
  generateSEOMetadata,
  generateServiceSchema,
  PAGE_SEO,
} from "@/app/shared/utils/seo"
import { BREADCRUMB_PATHS } from "@/app/shared/utils/seo-breadcrumbs"
import NexMoveAirAmbulanceContainer from "../../containers/services/nexmove-ambulance/NexMoveAirAmbulanceContainer"

export const metadata = generateSEOMetadata({
  ...PAGE_SEO.AMBULANCE,
  url: "https://nex-move.com/nexmove-air-ambulance",
})

const Page = () => {
  const breadcrumbs = [
    BREADCRUMB_PATHS.HOME,
    BREADCRUMB_PATHS.SERVICES,
    BREADCRUMB_PATHS.AMBULANCE,
  ]

  const serviceSchema = generateServiceSchema({
    name: "Air Ambulance & Medical Transport Services",
    description:
      "Emergency air ambulance services with trained medical professionals. Safe and dignified medical transport and human remains services.",
    url: "/nexmove-air-ambulance",
    area: [
      "India",
      "Delhi",
      "Mumbai",
      "Bangalore",
      "Chennai",
      "Kolkata",
      "Hyderabad",
      "Pune",
    ],
  })

  return (
    <>
      <SEOComponent
        breadcrumbs={breadcrumbs}
        structuredData={[serviceSchema]}
      />
      <NexMoveAirAmbulanceContainer />
    </>
  )
}

export default Page
