import { SEOComponent } from "@/app/shared/components"
import { generateSEOMetadata, PAGE_SEO } from "@/app/shared/utils/seo"
import { BREADCRUMB_PATHS } from "@/app/shared/utils/seo-breadcrumbs"
import { TrackShipmentScreen } from "@/screens"

export const metadata = generateSEOMetadata({
  ...PAGE_SEO.TRACK_SHIPMENT,
  url: "https://nex-move.com/track-shipment",
})

const Page = () => {
  const breadcrumbs = [BREADCRUMB_PATHS.HOME, BREADCRUMB_PATHS.TRACK_SHIPMENT]

  return (
    <>
      <SEOComponent breadcrumbs={breadcrumbs} />
      <TrackShipmentScreen />
    </>
  )
}

export default Page
