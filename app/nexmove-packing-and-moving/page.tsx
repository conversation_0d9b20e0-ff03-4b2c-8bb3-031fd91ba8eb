import { SEOComponent } from "@/app/shared/components"
import {
  generateSEOMetadata,
  generateServiceSchema,
  PAGE_SEO,
} from "@/app/shared/utils/seo"
import { BREADCRUMB_PATHS } from "@/app/shared/utils/seo-breadcrumbs"
import NexMovePackingMovingContainer from "../../containers/services/nexmove********-moving-container/NexMovePackingMovingContainer"

export const metadata = generateSEOMetadata({
  ...PAGE_SEO.PACKING_MOVING,
  url: "https://nex-move.com/nexmove********-and-moving",
})

const Page = () => {
  const breadcrumbs = [
    BREADCRUMB_PATHS.HOME,
    BREADCRUMB_PATHS.SERVICES,
    BREADCRUMB_PATHS.PACKING_MOVING,
  ]

  const serviceSchema = generateServiceSchema({
    name: "Professional Packing & Moving Services",
    description:
      "Expert packing and moving services for homes and offices. Safe transportation, quality packing materials, and timely delivery across India.",
    url: "/nexmove********-and-moving",
    area: [
      "India",
      "Delhi",
      "Mumbai",
      "Bangalore",
      "Chennai",
      "Kolkata",
      "Hyderabad",
      "Pune",
    ],
  })

  return (
    <>
      <SEOComponent
        breadcrumbs={breadcrumbs}
        structuredData={[serviceSchema]}
      />
      <NexMovePackingMovingContainer />
    </>
  )
}

export default Page
