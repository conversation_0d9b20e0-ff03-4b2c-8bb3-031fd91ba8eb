"use client"

import { AnimatePresence, motion, useScroll } from "framer-motion"
import { useEffect, useRef, useState } from "react"

interface StepProgressProps {
  steps: Array<{ number: number; [key: string]: unknown }>
  className?: string
  children?: React.ReactNode
}

const StepProgress = ({
  steps,
  className = "",
  children,
}: StepProgressProps) => {
  const ref = useRef<HTMLDivElement>(null)
  const [stepsToAnimate, setStepsToAnimate] = useState<number[]>([])

  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start center", "end center"],
  })

  useEffect(() => {
    const unsubscribe = scrollYProgress.on("change", (value) => {
      const numSteps = steps.length
      const stepThreshold = 1 / numSteps

      const newStepsToAnimate = steps.reduce((acc: number[], _, index) => {
        if (value >= index * stepThreshold) {
          acc.push(index)
        }
        return acc
      }, [])

      setStepsToAnimate(newStepsToAnimate)
    })

    return () => unsubscribe()
  }, [scrollYProgress, steps])

  return (
    <div ref={ref} className={`relative isolate grid ~gap-5/10 ${className}`}>
      {steps.map((step, index) => (
        <StepItem
          key={step.number}
          {...step}
          animate={stepsToAnimate.includes(index)}
        />
      ))}

      {/* steps bottom line */}
      <motion.span
        style={{ scaleY: scrollYProgress }}
        className='absolute bottom-5 top-5 -z-10 origin-top border-l-[1px] border-dashed border-primary ~left-5/6 sm:border-l-2'
      />

      {children}
    </div>
  )
}

export const StepItem = ({ animate = false, ...props }) => {
  const { number, title, color, description } = props

  return (
    <div className='flex items-center ~gap-8/12'>
      <div className='relative isolate grid aspect-square flex-shrink-0 place-items-center rounded-full bg-gray-400 text-white ~w-10/12'>
        <AnimatePresence>
          {animate && (
            <motion.span
              animate={{ scale: [0, 1] }}
              exit={{ scale: 0 }}
              className='absolute inset-0 -z-10 rounded-full'
              style={{ backgroundColor: color }}
              transition={{ duration: 1, type: "spring" }}
            />
          )}
        </AnimatePresence>
        <p aria-label={`Step ${number}`} className='font-bold ~text-base/xl'>
          {number}
        </p>
      </div>
      <div className='grid ~gap-0.5/1.5'>
        {/* <Icon className='~w-7/8' /> */}
        <p className='~text-base/xl'>{title}</p>
        {description && (
          <p
            aria-label='Step Description'
            className='text-zinc-400 ~text-base/lg ~pt-0/1'
          >
            {description}
          </p>
        )}
      </div>
    </div>
  )
}

export default StepProgress
