/**
 * Internal Linking Component for SEO Enhancement
 * Provides contextual internal links to improve site navigation and SEO
 */

import { cn } from "@/app/shared/utils"
import Link from "next/link"

interface InternalLinkProps {
  href: string
  children: React.ReactNode
  className?: string
  description?: string
  category?: "service" | "info" | "action" | "navigation"
  priority?: "high" | "medium" | "low"
  variant?: "inline" | "button" | "card"
}

export function InternalLink({
  href,
  children,
  className,
  description,
  category = "navigation",
  priority = "medium",
  variant = "inline",
}: InternalLinkProps) {
  const baseClasses = {
    inline: "text-primary hover:text-primary/80 underline transition-colors",
    button:
      "inline-flex items-center px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors",
    card: "block p-4 bg-white rounded-lg border border-gray-200 hover:border-primary hover:shadow-md transition-all",
  }

  const priorityClasses = {
    high: "font-semibold",
    medium: "font-medium",
    low: "font-normal",
  }

  const categoryAria = {
    service: "Learn more about this service",
    info: "Get additional information",
    action: "Take action now",
    navigation: "Navigate to page",
  }

  return (
    <Link
      href={href}
      className={cn(baseClasses[variant], priorityClasses[priority], className)}
      aria-label={description || `${categoryAria[category]}: ${children}`}
      title={description || `Visit ${children} page`}
    >
      {children}
      <span className='sr-only'>
        {description || `Navigate to ${children} page`}
      </span>
    </Link>
  )
}

interface RelatedLinksProps {
  currentPage?: string
  category?: "services" | "info" | "legal" | "help"
  className?: string
  showDescriptions?: boolean
}

export function RelatedLinks({
  currentPage,
  category = "services",
  className,
  showDescriptions = false,
}: RelatedLinksProps) {
  const linkGroups = {
    services: [
      {
        href: "/nexmove-packing-and-moving",
        title: "Packing & Moving",
        description: "Professional household and office relocation services",
      },
      {
        href: "/nexmove-trucking",
        title: "Trucking Services",
        description: "Reliable freight and goods transportation",
      },
      {
        href: "/nexmove-storage",
        title: "Storage Solutions",
        description: "Secure warehousing and storage facilities",
      },
      {
        href: "/nexmove-courier",
        title: "Courier Services",
        description: "Fast and reliable parcel delivery",
      },
      {
        href: "/nexmove-air-ambulance",
        title: "Air Ambulance",
        description: "Emergency medical transportation services",
      },
    ],
    info: [
      {
        href: "/about-us",
        title: "About NexMove",
        description: "Learn about our company and mission",
      },
      {
        href: "/gallery",
        title: "Service Gallery",
        description: "View our work and customer testimonials",
      },
      {
        href: "/packing-tips",
        title: "Packing Tips",
        description: "Expert advice for safe packing",
      },
      {
        href: "/relocation-checklist",
        title: "Relocation Checklist",
        description: "Complete moving preparation guide",
      },
    ],
    help: [
      {
        href: "/faq",
        title: "FAQ",
        description: "Frequently asked questions and answers",
      },
      {
        href: "/contact",
        title: "Contact Us",
        description: "Get in touch with our experts",
      },
      {
        href: "/track-shipment",
        title: "Track Shipment",
        description: "Monitor your shipment status",
      },
      {
        href: "/request-quote",
        title: "Get Quote",
        description: "Request instant service quotes",
      },
    ],
    legal: [
      {
        href: "/terms-and-conditions",
        title: "Terms & Conditions",
        description: "Our terms of service",
      },
      {
        href: "/privacy-and-data-protection-policy",
        title: "Privacy Policy",
        description: "How we protect your data",
      },
      {
        href: "/refund-and-cancellation-policy",
        title: "Refund Policy",
        description: "Our refund and cancellation terms",
      },
    ],
  }

  const links = linkGroups[category] || linkGroups.services
  const filteredLinks = links.filter((link) => link.href !== currentPage)

  if (filteredLinks.length === 0) return null

  return (
    <div className={cn("space-y-4", className)}>
      <h3 className='text-lg font-semibold'>
        {category === "services" && "Our Services"}
        {category === "info" && "Helpful Resources"}
        {category === "help" && "Need Help?"}
        {category === "legal" && "Legal Information"}
      </h3>
      <div className='grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3'>
        {filteredLinks.map((link) => (
          <InternalLink
            key={link.href}
            href={link.href}
            description={link.description}
            variant='card'
            category={category === "help" ? "action" : "navigation"}
            className='text-left'
          >
            <div>
              <div className='font-medium text-gray-900'>{link.title}</div>
              {showDescriptions && (
                <div className='mt-1 text-sm text-gray-600'>
                  {link.description}
                </div>
              )}
            </div>
          </InternalLink>
        ))}
      </div>
    </div>
  )
}

interface ServiceLinksProps {
  currentService?: string
  className?: string
}

export function ServiceLinks({ currentService, className }: ServiceLinksProps) {
  const services = [
    {
      name: "Packing & Moving",
      href: "/nexmove-packing-and-moving",
      description: "Professional relocation services for homes and offices",
      keywords: ["household moving", "office relocation", "packing services"],
    },
    {
      name: "Trucking Services",
      href: "/nexmove-trucking",
      description: "Reliable freight transportation across India",
      keywords: ["goods transport", "freight services", "logistics"],
    },
    {
      name: "Storage Solutions",
      href: "/nexmove-storage",
      description: "Secure warehousing and storage facilities",
      keywords: ["warehousing", "storage", "secure storage"],
    },
    {
      name: "Courier Services",
      href: "/nexmove-courier",
      description: "Fast parcel and document delivery",
      keywords: ["parcel delivery", "courier", "express shipping"],
    },
    {
      name: "Air Ambulance",
      href: "/nexmove-air-ambulance",
      description: "Emergency medical transportation services",
      keywords: ["medical transport", "air ambulance", "emergency services"],
    },
  ]

  const filteredServices = services.filter(
    (service) => service.href !== currentService,
  )

  return (
    <nav className={cn("space-y-4", className)} aria-label='Related services'>
      <h3 className='text-xl font-semibold'>Explore Our Other Services</h3>
      <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
        {filteredServices.map((service) => (
          <InternalLink
            key={service.href}
            href={service.href}
            description={service.description}
            variant='card'
            category='service'
            priority='high'
            className='group'
          >
            <div>
              <h4 className='font-semibold text-gray-900 transition-colors group-hover:text-primary'>
                {service.name}
              </h4>
              <p className='mt-2 text-sm text-gray-600'>
                {service.description}
              </p>
              <div className='mt-3 flex flex-wrap gap-2'>
                {service.keywords.map((keyword) => (
                  <span
                    key={keyword}
                    className='rounded bg-gray-100 px-2 py-1 text-xs text-gray-700'
                  >
                    {keyword}
                  </span>
                ))}
              </div>
            </div>
          </InternalLink>
        ))}
      </div>
    </nav>
  )
}

export default InternalLink
