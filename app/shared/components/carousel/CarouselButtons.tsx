"use client"

import { But<PERSON> } from "@/app/components/ui"
import { useCanScroll } from "@/hooks"
import { GlobalArrowLeftIcon, GlobalArrowRightIcon } from "@/Icons"
import { UseEmblaCarouselType } from "embla-carousel-react"
import { cn } from "../../utils"

type TCarouselButtonsProps = {
  emblaApi: UseEmblaCarouselType[1]
  className?: string
  hideOnMobile?: boolean
  buttonSize?: string
}

export const CarouselButtons = ({
  emblaApi,
  className,
  hideOnMobile = true,
  buttonSize,
}: TCarouselButtonsProps) => {
  const { canScrollPrev, canScrollNext } = useCanScroll(emblaApi)

  const handleScrollPreviousSlide = (
    e: React.MouseEvent<HTMLButtonElement>,
  ) => {
    e.preventDefault()
    emblaApi?.scrollPrev()
  }
  const handleScrollNextSlide = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault()
    emblaApi?.scrollNext()
  }

  return (
    <div
      className={cn(
        "pointer-events-none flex items-center gap-2 ~px-4/5",
        hideOnMobile && "max-sm:hidden",
        className,
      )}
    >
      <Button
        disabled={!canScrollPrev}
        className={cn(
          "pointer-events-auto rounded-full bg-gradient-to-r from-primary to-zinc-50 !p-[1px] ~size-10/12",
          buttonSize,
        )}
        onClick={handleScrollPreviousSlide}
        size='sm'
        id='carousel-prev-btn'
      >
        <span className='grid size-full place-content-center rounded-full bg-zinc-50 text-black'>
          <GlobalArrowLeftIcon className='size-6 stroke-1' />
        </span>
      </Button>
      <Button
        disabled={!canScrollNext}
        className={cn(
          "pointer-events-auto rounded-full bg-gradient-to-r from-zinc-50 to-primary !p-[1px] ~size-10/12",
          buttonSize,
        )}
        onClick={handleScrollNextSlide}
        size='sm'
        id='carousel-next-btn'
      >
        <span className='grid size-full place-content-center rounded-full bg-zinc-50 text-black'>
          <GlobalArrowRightIcon className='size-6 stroke-1' />
        </span>
      </Button>
    </div>
  )
}
