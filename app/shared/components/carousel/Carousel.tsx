"use client"

import useEmblaCarousel from "embla-carousel-react"
import { CarouselNavigations, Slide } from "./index"
import { CarouselButtons } from "./index"
import { cn } from "../../utils"
import { useIsClient, useWindowSize } from "@/hooks"

// Position classes for buttons
const positionClasses = {
  default: "justify-between absolute left-0 right-0 top-1/2 -translate-y-1/2",
  "bottom-center": "justify-center ~gap-3/4 ~mt-4/6",
  "bottom-right": "justify-end ~gap-3/4 ~mt-4/6",
} as const

// size classes for buttons
const sizeClasses = {
  default: "~size-10/12",
  large: "~size-12/14",
} as const

type TCarouselProps = {
  slideNodes: React.ReactNode[]
  navigationNodes?: React.ReactNode[]
  showActionButtons?: boolean
  actionButtonsContainerClassName?: string
  navigationContainerClassName?: string
  buttonsPosition?: keyof typeof positionClasses
  buttionSize?: keyof typeof sizeClasses
  spacing?: number
  loop?: boolean
  dragFree?: boolean
  fitContent?: boolean
  align?: "center" | "start" | "end"
}

export const Carousel = (props: TCarouselProps) => {
  const {
    slideNodes,
    showActionButtons = true,
    navigationNodes,
    actionButtonsContainerClassName,
    navigationContainerClassName,
    buttonsPosition = "default",
    buttionSize = "default",
    spacing = 1,
    loop = false,
    dragFree = false,
    fitContent = false,
    align = "start",
  } = props
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop, dragFree, align })

  // Reduce card spacing on tablet and mobile overriding the spacing prop
  const { width } = useWindowSize()
  const isTablet = width < 768
  const isClient = useIsClient()
  const spacingValue = isTablet && isClient ? 0.5 : spacing

  return (
    <div className='relative'>
      <div className='overflow-hidden' ref={emblaRef}>
        <div className='flex' style={{ marginLeft: `-${spacingValue}rem` }}>
          {slideNodes.map((slide, index) => (
            <Slide
              className={cn(fitContent && "flex-[0_0_auto]")}
              key={index}
              spacing={spacingValue}
            >
              {slide}
            </Slide>
          ))}
        </div>
      </div>
      {showActionButtons && (
        <CarouselButtons
          buttonSize={sizeClasses[buttionSize]}
          emblaApi={emblaApi}
          className={cn(
            positionClasses[buttonsPosition],
            actionButtonsContainerClassName,
          )}
        />
      )}
      {navigationNodes && (
        <CarouselNavigations
          navigationNodes={navigationNodes}
          className={navigationContainerClassName}
          emblaApi={emblaApi}
        />
      )}
    </div>
  )
}
