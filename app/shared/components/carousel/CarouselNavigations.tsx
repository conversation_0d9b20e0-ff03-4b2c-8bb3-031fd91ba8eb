"use client"

import { cn } from "../../utils"
import { useActiveSlideIndex } from "@/hooks/use-active-slide-index"
import { UseEmblaCarouselType } from "embla-carousel-react"

type TCarouselNavigationsProps = {
  navigationNodes: React.ReactNode[]
  className?: string
  emblaApi: UseEmblaCarouselType[1]
}

export const CarouselNavigations = ({
  navigationNodes,
  className,
  emblaApi,
}: TCarouselNavigationsProps) => {
  const { activeIndex } = useActiveSlideIndex(emblaApi)

  return (
    <div className={cn("flex ~gap-3/5", className)}>
      {navigationNodes.map((node, index) => (
        <button
          className='flex items-start gap-1 text-xs'
          key={index}
          onClick={() => emblaApi?.scrollTo(index)}
        >
          <span
            className={cn(
              "w-[2px] rounded-md bg-black/10 transition-colors ~h-7/8",
              activeIndex === index && "bg-black",
            )}
          ></span>
          {node}
        </button>
      ))}
    </div>
  )
}
