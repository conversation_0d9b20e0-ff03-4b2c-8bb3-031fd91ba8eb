import Image from "next/image"
import { Chip } from "."
import { cn } from "../utils"

type TImageTextWrapper = {
  image: string
  children: React.ReactNode
  labelBoldText?: string
  labelText?: string
  rtl?: boolean
  className?: string
}

const SplitView = ({
  image,
  children,
  rtl = false,
  labelBoldText,
  labelText,
  className,
}: TImageTextWrapper) => {
  return (
    <div
      className={cn(
        "flex max-md:flex-col",
        rtl && "flex-row-reverse",
        className,
      )}
    >
      <div className='relative aspect-video min-h-96 min-w-[50%] flex-1'>
        <Image src={image} alt='About Us' fill className='object-cover' />
        {labelBoldText && labelText && (
          <Chip
            boldText={labelBoldText}
            text={labelText}
            className='absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2'
          />
        )}
      </div>
      <div className='flex-1 bg-zinc-100 ~px-5/10 ~py-8/12 max-sm:pt-5'>
        {children}
      </div>
    </div>
  )
}

export default SplitView
