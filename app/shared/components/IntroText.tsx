import { SectionWrapper } from "./SectionWrapper"

type TIntroText = {
  subtitle?: string
  description: string
  className?: string
}

const IntroText = ({ subtitle, description, className }: TIntroText) => {
  return (
    <SectionWrapper className={className}>
      {subtitle && (
        <strong
          aria-label='Page Subtitle'
          aria-description={subtitle}
          className='font-inter uppercase ~text-xs/sm'
        >
          {subtitle}
        </strong>
      )}
      <p
        aria-hidden
        className='grid ~text-xl/2xl ~gap-5/8 ~pt-1/2 max-sm:leading-tight'
      >
        {description.split("//").map((line, index) => (
          <span key={index}>{line}</span>
        ))}
      </p>
      <p className='sr-only hidden'>{description}</p>
    </SectionWrapper>
  )
}

export default IntroText
