import { cn } from "../utils"

type LabelProps = {
  text: string
  boldText: string
  className?: string
}

const Chip = ({ boldText, text, className }: LabelProps) => {
  return (
    <div
      className={cn(
        "absolute left-[50%] top-[40%] -translate-x-[60%] -rotate-2 whitespace-nowrap rounded bg-mistGradient font-geist uppercase ~text-3xl/5xl ~p-1/2",
        className,
      )}
    >
      <span className='font-medium'>{boldText}</span>{" "}
      <span className='font-thin'>{text}</span>
    </div>
  )
}

export default Chip
