/**
 * Enhanced Navigation Component with SEO-optimized internal linking
 * Supports sitelinks generation and improved semantic structure
 */

"use client"

import { cn } from "@/app/shared/utils"
import {
  MAIN_SERVICE_CATEGORIES,
  SITELINK_PRIORITY_PAGES,
} from "@/app/shared/utils/seo-sitelinks"
import { GlobalArrowRightIcon } from "@/Icons"
import Link from "next/link"
import { usePathname } from "next/navigation"

interface EnhancedNavProps {
  className?: string
  variant?: "header" | "footer" | "sidebar"
  showDescriptions?: boolean
  hasScroll?: boolean
  isClient?: boolean
}

export function EnhancedNavigation({
  className,
  variant = "header",
  showDescriptions = false,
  hasScroll = false,
  isClient = false,
}: EnhancedNavProps) {
  const pathname = usePathname()

  const mainNavItems = [
    {
      name: "Home",
      href: "/",
      description: "Return to NexMove homepage",
      isExternal: false,
    },
    {
      name: "About Us",
      href: "/about-us",
      description: "Learn about our company and mission",
      isExternal: false,
    },
    {
      name: "Service",
      href: "/services-and-pricing",
      description: "View our comprehensive logistics services and pricing",
      isExternal: false,
    },
    {
      name: "Track Shipment",
      href: "/track-shipment",
      description: "Track your shipment status in real-time",
      isExternal: false,
    },
    {
      name: "Gallery",
      href: "/gallery",
      description: "View our service gallery and photos",
      isExternal: false,
    },
    {
      name: "Pay Now",
      href: "/pay-now",
      description: "Make secure online payments",
      isExternal: false,
    },
    {
      name: "FAQs",
      href: "/faq",
      description: "Find answers to frequently asked questions",
      isExternal: false,
    },
    {
      name: "Contact Us",
      href: "/contact",
      description: "Get in touch with our logistics experts",
      isExternal: false,
    },
  ]

  const serviceNavItems = MAIN_SERVICE_CATEGORIES.map((category) => ({
    name: category.name,
    href: category.url,
    description: category.description,
    isExternal: false,
    subcategories: category.subcategories,
  }))

  const quickAccessItems = SITELINK_PRIORITY_PAGES.filter((page) =>
    ["FAQ", "Gallery", "Tracking", "Quote"].includes(page.category),
  ).map((page) => ({
    name: page.name,
    href: page.url,
    description: page.description,
    isExternal: false,
  }))

  if (variant === "header") {
    // Show navigation items excluding Home and Contact Us (they're handled separately)
    const headerNavItems = mainNavItems.filter(
      (item) => item.href !== "/" && item.href !== "/contact",
    )

    return (
      <nav
        className={cn(
          "flex flex-1 items-center justify-between gap-3 whitespace-nowrap font-medium uppercase max-lg:justify-end",
          className,
        )}
        aria-label='Main navigation'
      >
        <ul className='flex items-center max-lg:hidden'>
          {headerNavItems.map((item) => (
            <li key={item.href}>
              <Link
                href={item.href}
                className={cn(
                  "group flex items-center bg-transparent ~px-2/3.5",
                  hasScroll && isClient && "text-black",
                  pathname === item.href && "text-primary",
                )}
                aria-label={item.description}
                title={item.description}
              >
                {item.name}
                {pathname !== item.href && (
                  <GlobalArrowRightIcon className='ml-2 w-0 transition-all group-hover:w-5' />
                )}
                <span className='sr-only'>{item.description}</span>
              </Link>
            </li>
          ))}
        </ul>
      </nav>
    )
  }

  if (variant === "footer") {
    return (
      <div className={cn("grid grid-cols-1 gap-8 md:grid-cols-4", className)}>
        {/* Main Navigation */}
        <div>
          <h3 className='mb-4 text-lg font-semibold text-white'>Quick Links</h3>
          <nav aria-label='Footer navigation'>
            <ul className='space-y-2'>
              {mainNavItems.map((item) => (
                <li key={item.href}>
                  <Link
                    href={item.href}
                    className='text-gray-300 transition-colors hover:text-white'
                    aria-label={item.description}
                    title={item.description}
                  >
                    {item.name}
                    {showDescriptions && (
                      <span className='mt-1 block text-sm text-gray-400'>
                        {item.description}
                      </span>
                    )}
                  </Link>
                </li>
              ))}
            </ul>
          </nav>
        </div>

        {/* Services Navigation */}
        <div>
          <h3 className='mb-4 text-lg font-semibold text-white'>
            Our Services
          </h3>
          <nav aria-label='Services navigation'>
            <ul className='space-y-2'>
              {serviceNavItems.map((item) => (
                <li key={item.href}>
                  <Link
                    href={item.href}
                    className='text-gray-300 transition-colors hover:text-white'
                    aria-label={item.description}
                    title={item.description}
                  >
                    {item.name}
                    {showDescriptions && (
                      <span className='mt-1 block text-sm text-gray-400'>
                        {item.description}
                      </span>
                    )}
                  </Link>
                  {item.subcategories && (
                    <ul className='ml-4 mt-2 space-y-1'>
                      {item.subcategories.map((sub) => (
                        <li key={sub.url}>
                          <Link
                            href={sub.url}
                            className='text-sm text-gray-400 transition-colors hover:text-gray-300'
                            aria-label={`${sub.name} - ${item.description}`}
                            title={`Learn more about ${sub.name}`}
                          >
                            {sub.name}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  )}
                </li>
              ))}
            </ul>
          </nav>
        </div>

        {/* Quick Access */}
        <div>
          <h3 className='mb-4 text-lg font-semibold text-white'>
            Quick Access
          </h3>
          <nav aria-label='Quick access navigation'>
            <ul className='space-y-2'>
              {quickAccessItems.map((item) => (
                <li key={item.href}>
                  <Link
                    href={item.href}
                    className='text-gray-300 transition-colors hover:text-white'
                    aria-label={item.description}
                    title={item.description}
                  >
                    {item.name}
                    {showDescriptions && (
                      <span className='mt-1 block text-sm text-gray-400'>
                        {item.description}
                      </span>
                    )}
                  </Link>
                </li>
              ))}
            </ul>
          </nav>
        </div>

        {/* Contact Information */}
        <div>
          <h3 className='mb-4 text-lg font-semibold text-white'>
            Contact Info
          </h3>
          <div className='space-y-2 text-gray-300'>
            <p>
              <span className='font-medium'>Phone:</span>
              <a
                href='tel:+91XXXXXXXXXX'
                className='ml-2 transition-colors hover:text-white'
              >
                +91-XXXXXXXXXX
              </a>
            </p>
            <p>
              <span className='font-medium'>Email:</span>
              <a
                href='mailto:<EMAIL>'
                className='ml-2 transition-colors hover:text-white'
              >
                <EMAIL>
              </a>
            </p>
            <div className='pt-4'>
              <Link
                href='/contact'
                className='inline-block rounded bg-primary px-4 py-2 text-white transition-colors hover:bg-primary/90'
                aria-label='Contact us for logistics and moving services'
                title='Get in touch with our logistics experts'
              >
                Get In Touch
              </Link>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Sidebar variant
  return (
    <nav className={cn("space-y-6", className)} aria-label='Sidebar navigation'>
      <div>
        <h3 className='mb-3 text-lg font-semibold'>Main Pages</h3>
        <ul className='space-y-2'>
          {mainNavItems.map((item) => (
            <li key={item.href}>
              <Link
                href={item.href}
                className={cn(
                  "block rounded-md px-3 py-2 text-sm transition-colors",
                  pathname === item.href
                    ? "bg-primary text-white"
                    : "text-gray-700 hover:bg-gray-100",
                )}
                aria-label={item.description}
                title={item.description}
              >
                {item.name}
              </Link>
            </li>
          ))}
        </ul>
      </div>

      <div>
        <h3 className='mb-3 text-lg font-semibold'>Services</h3>
        <ul className='space-y-2'>
          {serviceNavItems.map((item) => (
            <li key={item.href}>
              <Link
                href={item.href}
                className={cn(
                  "block rounded-md px-3 py-2 text-sm transition-colors",
                  pathname === item.href
                    ? "bg-primary text-white"
                    : "text-gray-700 hover:bg-gray-100",
                )}
                aria-label={item.description}
                title={item.description}
              >
                {item.name}
              </Link>
            </li>
          ))}
        </ul>
      </div>
    </nav>
  )
}

export default EnhancedNavigation
