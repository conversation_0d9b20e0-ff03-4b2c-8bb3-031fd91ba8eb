import Image from "next/image"
import Link from "next/link"

type TInfoCard = {
  title: string
  image: string
  cardColor: string
  link?: string
}

const InfoCard = ({ title, image, cardColor, link }: TInfoCard) => {
  return (
    <Link href={link ?? ""}>
      <div
        className='grid h-full content-between text-white ~w-60/96'
        style={{ backgroundColor: cardColor }}
      >
        <strong
          aria-label='Industry'
          className='font-medium !leading-tight ~text-lg/2xl ~p-4/6'
        >
          {title}
        </strong>

        <div className='relative aspect-square w-full bg-white/20'>
          <Image
            src={image}
            alt={title}
            fill
            sizes='(max-width: 768px) 100vw, 33vw'
            draggable={false}
            className='object-cover'
          />
        </div>
      </div>
    </Link>
  )
}

export default InfoCard
