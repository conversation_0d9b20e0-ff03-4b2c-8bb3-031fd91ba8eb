import { SectionWrapper } from "@/app/shared/components"
import { GlobalArrowRightIcon, WavePatternIcon } from "@/Icons"
import Link from "next/link"

const QuickActionCards = () => {
  return (
    <SectionWrapper className='~py-5/10'>
      <div className='relative grid grid-cols-2 overflow-hidden ~gap-3/4 max-sm:grid-cols-1'>
        <Link
          // href={"/track-shipment"}
          target='_blank'
          rel='noopener noreferrer'
          href='https://development9p.trackingmore.org/'
          className='relative grid content-between rounded-2xl bg-zinc-200 ~h-44/72 ~p-5/8'
        >
          <span className='relative z-10 text-sm uppercase'>Track Now</span>
          <button
            aria-label='Track Shipment'
            className='group relative z-10 flex w-fit items-center font-inter ~text-lg/2xl ~gap-3/5'
          >
            Track my consignment
            <GlobalArrowRightIcon
              aria-label='Arrow Right Icon'
              className='h-auto transition-transform duration-300 ~w-5/6 group-hover:translate-x-5'
            />
          </button>

          <WavePatternIcon
            aria-label='Wave Pattern Icon'
            className='pointer-events-none absolute sm:-bottom-[25%]'
          />
        </Link>

        <Link
          href={"/contact"}
          className='relative grid content-between rounded-2xl bg-primary ~h-44/72 ~p-5/8'
        >
          <span className='relative z-10 text-sm uppercase'>Find Answers</span>
          <button
            aria-label='Contact Us'
            className='group relative z-10 flex w-fit items-center font-inter ~text-lg/2xl ~gap-3/5'
          >
            Contact Us
            <GlobalArrowRightIcon
              aria-label='Arrow Right Icon'
              className='h-auto transition-transform duration-300 ~w-5/6 group-hover:translate-x-5'
            />
          </button>
          <WavePatternIcon
            aria-label='Wave Pattern Icon'
            className='pointer-events-none absolute sm:-bottom-[25%]'
          />
        </Link>

        {/* <WavePatternIcon className='pointer-events-none absolute sm:-bottom-[25%]' /> */}
      </div>
    </SectionWrapper>
  )
}

export default QuickActionCards
