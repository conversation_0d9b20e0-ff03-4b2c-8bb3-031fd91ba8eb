/**
 * Comprehensive SEO Component
 * Handles breadcrumbs, structured data, and other SEO elements
 * This component generates structured data that search engines can read
 * It does NOT render any visible UI elements - purely for SEO purposes
 */

import {
  generateOrganizationSchema,
  generateSiteNavigationSchema,
  generateWebSiteSchema,
} from "@/app/shared/utils/seo"
import {
  generateBreadcrumbSchema,
  type BreadcrumbItem,
} from "@/app/shared/utils/seo-breadcrumbs"

interface SEOComponentProps {
  breadcrumbs?: BreadcrumbItem[]
  structuredData?: Record<string, unknown>[]
  includeOrganization?: boolean
  includeWebsite?: boolean
  includeSiteNavigation?: boolean
  includeSearchAction?: boolean
}

export function SEOComponent({
  breadcrumbs = [],
  structuredData = [],
  includeOrganization = false,
  includeWebsite = false,
  includeSiteNavigation = false,
  // includeSearchAction = false,
}: SEOComponentProps) {
  const schemas = []

  // Add breadcrumbs if provided
  if (breadcrumbs.length > 0) {
    schemas.push(generateBreadcrumbSchema(breadcrumbs))
  }

  // Add organization schema if requested
  if (includeOrganization) {
    schemas.push(JSON.stringify(generateOrganizationSchema()))
  }

  // Add website schema if requested
  if (includeWebsite) {
    schemas.push(JSON.stringify(generateWebSiteSchema()))
  }

  // Add site navigation schema for enhanced sitelinks
  if (includeSiteNavigation) {
    schemas.push(generateSiteNavigationSchema())
  }

  // Add custom structured data
  structuredData.forEach((data) => {
    schemas.push(JSON.stringify(data))
  })

  if (schemas.length === 0) {
    return null
  }

  return (
    <>
      {schemas.map((schema, index) => (
        <script
          key={index}
          type='application/ld+json'
          dangerouslySetInnerHTML={{
            __html: schema,
          }}
        />
      ))}
    </>
  )
}

// Backward compatibility - keeping the old component name
export function SEOBreadcrumbs({
  breadcrumbs,
}: {
  breadcrumbs: BreadcrumbItem[]
}) {
  return <SEOComponent breadcrumbs={breadcrumbs} />
}

export default SEOComponent
