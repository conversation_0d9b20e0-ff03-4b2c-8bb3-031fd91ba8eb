import { WavePatternIcon } from "@/Icons"

type PageHeaderProps = {
  firstLine?: string
  secondLine?: string
}

const PageHeader = ({ firstLine, secondLine }: PageHeaderProps) => {
  return (
    <section className='relative isolate overflow-hidden bg-darkGray ~mb-14/16 ~px-4/16 ~pt-32/48 ~pb-4/12'>
      <span className='absolute inset-0 -z-10'>
        <WavePatternIcon
          aria-label='Wave Pattern Icon'
          className='w-full -translate-y-[15%] scale-y-75'
        />
      </span>
      <p className='uppercase text-white ~text-3xl/5xl' aria-hidden>
        {firstLine !== undefined && (
          <span className='font-light'>{firstLine}</span>
        )}
        <br />
        {secondLine !== undefined && (
          <span className='font-semibold'>{secondLine}</span>
        )}
      </p>
      <h1 className='sr-only hidden'>{firstLine + "" + secondLine}</h1>
    </section>
  )
}

export default PageHeader
