/**
 * Semantic HTML Components for Enhanced SEO
 * These components provide better semantic structure for search engines
 */

import { cn } from "@/app/shared/utils"

interface SemanticSectionProps {
  children: React.ReactNode
  className?: string
  as?: "section" | "article" | "aside" | "main" | "header" | "footer"
  ariaLabel?: string
  role?: string
}

export function SemanticSection({
  children,
  className,
  as: Component = "section",
  ariaLabel,
  role,
}: SemanticSectionProps) {
  return (
    <Component className={cn("", className)} aria-label={ariaLabel} role={role}>
      {children}
    </Component>
  )
}

interface PageStructureProps {
  children: React.ReactNode
  className?: string
  pageTitle?: string
  breadcrumbs?: React.ReactNode
}

export function PageStructure({
  children,
  className,
  pageTitle,
  breadcrumbs,
}: PageStructureProps) {
  return (
    <main className={cn("min-h-screen", className)} role='main'>
      {breadcrumbs && (
        <nav aria-label='Breadcrumb navigation' className='py-4'>
          {breadcrumbs}
        </nav>
      )}
      {pageTitle && (
        <header className='mb-8'>
          <h1 className='text-3xl font-bold text-gray-900 lg:text-4xl'>
            {pageTitle}
          </h1>
        </header>
      )}
      {children}
    </main>
  )
}

interface ServicePageStructureProps {
  serviceName: string
  description: string
  features?: string[]
  pricing?: React.ReactNode
  relatedServices?: React.ReactNode
  children: React.ReactNode
  className?: string
}

export function ServicePageStructure({
  serviceName,
  description,
  features = [],
  pricing,
  relatedServices,
  children,
  className,
}: ServicePageStructureProps) {
  return (
    <article
      className={cn("space-y-12", className)}
      itemScope
      itemType='https://schema.org/Service'
    >
      {/* Service Header */}
      <header className='space-y-4 text-center'>
        <h1
          className='text-4xl font-bold text-gray-900 lg:text-5xl'
          itemProp='name'
        >
          {serviceName}
        </h1>
        <p
          className='mx-auto max-w-3xl text-xl text-gray-600'
          itemProp='description'
        >
          {description}
        </p>
      </header>

      {/* Service Content */}
      <section aria-label='Service details'>{children}</section>

      {/* Service Features */}
      {features.length > 0 && (
        <section
          aria-label='Service features'
          className='rounded-lg bg-gray-50 px-6 py-12'
        >
          <h2 className='mb-8 text-center text-2xl font-semibold'>
            Key Features
          </h2>
          <ul className='grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3'>
            {features.map((feature, index) => (
              <li
                key={index}
                className='flex items-center space-x-3 rounded-md bg-white p-4 shadow-sm'
              >
                <span className='h-2 w-2 flex-shrink-0 rounded-full bg-primary'></span>
                <span className='text-gray-700'>{feature}</span>
              </li>
            ))}
          </ul>
        </section>
      )}

      {/* Pricing Section */}
      {pricing && (
        <section aria-label='Service pricing' className='bg-white py-12'>
          <h2 className='mb-8 text-center text-2xl font-semibold'>
            Pricing & Packages
          </h2>
          {pricing}
        </section>
      )}

      {/* Related Services */}
      {relatedServices && (
        <aside aria-label='Related services' className='border-t pt-12'>
          {relatedServices}
        </aside>
      )}
    </article>
  )
}

interface ContactSectionProps {
  title?: string
  subtitle?: string
  children?: React.ReactNode
  className?: string
  includeSchema?: boolean
}

export function ContactSection({
  title = "Get In Touch",
  subtitle = "Ready to get started? Contact us today!",
  children,
  className,
  includeSchema = true,
}: ContactSectionProps) {
  const contactSchema = includeSchema
    ? {
        "@context": "https://schema.org",
        "@type": "ContactPoint",
        contactType: "customer service",
        telephone: "+91-XXXXXXXXXX",
        email: "<EMAIL>",
        availableLanguage: ["English", "Hindi"],
        areaServed: "IN",
      }
    : null

  return (
    <section
      className={cn("bg-primary py-16 text-white", className)}
      itemScope
      itemType='https://schema.org/ContactPoint'
      aria-label='Contact information'
    >
      {contactSchema && (
        <script
          type='application/ld+json'
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(contactSchema),
          }}
        />
      )}

      <div className='container mx-auto px-4 text-center'>
        <h2 className='mb-4 text-3xl font-bold' itemProp='name'>
          {title}
        </h2>
        <p className='mb-8 text-xl opacity-90'>{subtitle}</p>

        <div className='mb-8 space-y-4'>
          <p className='flex items-center justify-center space-x-2'>
            <span className='font-medium'>Phone:</span>
            <a
              href='tel:+91XXXXXXXXXX'
              className='hover:underline'
              itemProp='telephone'
            >
              +91-XXXXXXXXXX
            </a>
          </p>
          <p className='flex items-center justify-center space-x-2'>
            <span className='font-medium'>Email:</span>
            <a
              href='mailto:<EMAIL>'
              className='hover:underline'
              itemProp='email'
            >
              <EMAIL>
            </a>
          </p>
        </div>

        {children}
      </div>
    </section>
  )
}

interface FAQSectionProps {
  questions: Array<{
    question: string
    answer: string
  }>
  title?: string
  className?: string
}

export function FAQSection({
  questions,
  title = "Frequently Asked Questions",
  className,
}: FAQSectionProps) {
  const faqSchema = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    mainEntity: questions.map((faq) => ({
      "@type": "Question",
      name: faq.question,
      acceptedAnswer: {
        "@type": "Answer",
        text: faq.answer,
      },
    })),
  }

  return (
    <section
      className={cn("py-16", className)}
      aria-label='Frequently asked questions'
    >
      <script
        type='application/ld+json'
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(faqSchema),
        }}
      />

      <div className='container mx-auto px-4'>
        <h2 className='mb-12 text-center text-3xl font-bold'>{title}</h2>

        <div className='mx-auto max-w-4xl space-y-6'>
          {questions.map((faq, index) => (
            <details
              key={index}
              className='group rounded-lg border border-gray-200 bg-white p-6'
              itemScope
              itemType='https://schema.org/Question'
            >
              <summary className='flex cursor-pointer list-none items-center justify-between text-lg font-semibold'>
                <span itemProp='name'>{faq.question}</span>
                <span className='transform transition-transform group-open:rotate-180'>
                  ▼
                </span>
              </summary>
              <div
                className='prose prose-sm mt-4 max-w-none text-gray-600'
                itemScope
                itemType='https://schema.org/Answer'
                itemProp='acceptedAnswer'
              >
                <div itemProp='text'>{faq.answer}</div>
              </div>
            </details>
          ))}
        </div>
      </div>
    </section>
  )
}

export default SemanticSection
