"use client"

import { cn } from "@/app/shared/utils"
import { GlobalArrowLeftIcon } from "@/Icons"
import { useRouter } from "next/navigation"
import { ElementType } from "react"

type TSectionTitle = {
  subtitle?: string
  title: string
  className?: string
  showBackButton?: boolean
  titleElement?: ElementType
}

export const SectionTitle = ({
  subtitle,
  title,
  className,
  showBackButton,
  titleElement: TitleElement = "h6",
}: TSectionTitle) => {
  const router = useRouter()

  return (
    <div aria-label={title} className={cn("~pb-6/10", className)}>
      {subtitle && (
        <span className='block font-inter uppercase ~text-xs/sm ~pb-0/1'>
          {subtitle}
        </span>
      )}
      <div className='flex items-center ~gap-3/4'>
        {showBackButton && (
          <button
            onClick={() => router.back()}
            className='flex items-center justify-center bg-primary text-white shadow-sm transition-all ~size-6/8 hover:shadow'
          >
            <GlobalArrowLeftIcon className='~h-4/5 ~w-4/5' />
          </button>
        )}
        <p
          aria-label={title}
          className='grid ~text-xl/3xl ~gap-5/8 max-sm:leading-none'
        >
          {title.split("//").map((line, index) => (
            <span key={index}>{line}</span>
          ))}
        </p>

        <TitleElement className='hidden'>{title}</TitleElement>
      </div>
    </div>
  )
}
