"use client"
import { AnimatePresence, motion } from "framer-motion"

type TStepProgress = {
  number: number
  title: string
  color?: string
  icon: React.ElementType | null
  description?: string
  animate?: boolean
}

export const StepProgress = (props: TStepProgress) => {
  const { number, title, color, description, animate = false } = props

  return (
    <div className='flex items-center ~gap-8/12'>
      <div className='relative isolate grid aspect-square flex-shrink-0 place-items-center rounded-full bg-gray-400 text-white ~w-10/12'>
        <AnimatePresence>
          {animate && (
            <motion.span
              animate={{ scale: [0, 1] }}
              exit={{ scale: 0 }}
              className='absolute inset-0 -z-10 rounded-full'
              style={{ backgroundColor: color }}
              transition={{ duration: 1, type: "spring" }}
            />
          )}
        </AnimatePresence>
        <p aria-label={`Step ${number}`} className='font-bold ~text-base/xl'>
          {number}
        </p>
      </div>
      <div className='grid ~gap-0.5/1.5'>
        {/* <Icon className='~w-7/8' /> */}
        <p className='~text-base/xl'>{title}</p>
        {description && (
          <p
            aria-label='Step Description'
            className='text-zinc-400 ~text-base/lg ~pt-0/1'
          >
            {description}
          </p>
        )}
      </div>
    </div>
  )
}

export default StepProgress
