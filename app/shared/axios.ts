import axios from "axios"

const axiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_SERVER_URL ?? "",
})

axiosInstance.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("token")
    if (token) config.headers.Authorization = `Bearer ${token}`
    return config
  },
  (error) =>
    Promise.reject(
      (error.response && error.response.data) || "Something went wrong!",
    ),
)

export default axiosInstance
