import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import dayjs from "dayjs"

/**
 * Merges multiple class names, handling conditional values and Tailwind conflicts.
 * @example
 * ```ts
 * cn("text-red-500", "bg-blue-500"); // "text-red-500 bg-blue-500"
 * cn("px-4", { "py-2": true, "hidden": false }); // "px-4 py-2"
 * ```
 */

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// -------------------------------------

/**
 * Converts a snake_case string to Title Case.
 * @example
 * ```ts
 * snakeToTitle("hello_world"); // "Hello World"
 * snakeToTitle("this_is_a_test"); // "This Is A Test"
 * ```
 */

export function snakeToTitle(str: string) {
  return str
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ")
}

// -----------------------------------

/**
 * Formats a date string into a readable format (e.g., "January 1, 2025").
 * @example
 * ```ts
 * formatDate("2025-01-01"); // "January 1, 2025"
 * ```
 */
export function formatDate(dateString: string) {
  const date = dayjs(dateString)

  if (!date.isValid()) {
    throw new Error("Invalid date string")
  }

  return date.format("MMM D, YYYY") // "Jan 1, 2025"
}

// Formats a number as currency (INR by default)
export function formatCurrency(amount: number, currency: string = "INR") {
  const formatter = new Intl.NumberFormat("en-IN", {
    style: "currency",
    currency: currency,
    minimumFractionDigits: 0,
  })

  return formatter.format(amount)
}
