/**
 * Utility functions for phone verification and user info
 */

// User info type
export type UserInfo = {
  name: string
  email: string
  phone: string
}

/**
 * Check if any phone has been verified in the current session
 * @returns Boolean indicating if any phone is verified
 */
export const checkForVerifiedPhone = (): boolean => {
  if (typeof window !== "undefined") {
    return sessionStorage.getItem("is_phone_verified") === "true"
  }
  return false
}

/**
 * Check if a specific phone number is verified in sessionStorage
 * @param phone The phone number to check
 * @returns Boolean indicating if the phone is verified
 * @deprecated Use checkForVerifiedPhone() instead
 */
export const isPhoneVerified = (phone: string): boolean => {
  if (typeof window !== "undefined") {
    return sessionStorage.getItem(`verified_phone_${phone}`) === "true"
  }
  return false
}

/**
 * Store a verified phone flag and user info in sessionStorage
 * @param phone The phone number that was verified
 * @param userInfo The user information to store
 */
export const storeVerifiedPhone = (
  phone: string,
  userInfo?: UserInfo,
): void => {
  if (typeof window !== "undefined") {
    // Store the specific phone number for reference (might be used in other parts of the app)
    sessionStorage.setItem(`verified_phone_${phone}`, "true")

    // Set the global verification flag
    sessionStorage.setItem("is_phone_verified", "true")

    // Store user info if provided
    if (userInfo) {
      sessionStorage.setItem("user_info", JSON.stringify(userInfo))
    }
  }
}

/**
 * Get user info from sessionStorage
 * @returns The stored user info or null if not found
 */
export const getUserInfo = (): UserInfo | null => {
  if (typeof window !== "undefined") {
    const userInfoStr = sessionStorage.getItem("user_info")
    if (userInfoStr) {
      try {
        return JSON.parse(userInfoStr) as UserInfo
      } catch (e) {
        console.error("Error parsing user info from session storage:", e)
      }
    }
  }
  return null
}
