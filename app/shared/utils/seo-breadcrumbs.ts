/**
 * SEO Breadcrumb utilities for generating JSON-LD structured data
 * These breadcrumbs are for search engines only and will not be displayed on the UI
 */

export interface BreadcrumbItem {
  name: string
  url: string
}

export function generateBreadcrumbSchema(
  breadcrumbs: BreadcrumbItem[],
): string {
  const breadcrumbList = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: breadcrumbs.map((crumb, index) => ({
      "@type": "ListItem",
      position: index + 1,
      name: crumb.name,
      item: `https://nex-move.com${crumb.url}`,
    })),
  }

  return JSON.stringify(breadcrumbList)
}

// Common breadcrumb paths for the website
export const BREADCRUMB_PATHS = {
  HOME: { name: "Home", url: "/" },
  ABOUT: { name: "About Us", url: "/about-us" },
  SERVICES: { name: "Services", url: "/services-and-pricing" },
  PACKING_MOVING: {
    name: "Packing & Moving",
    url: "/nexmove-packing-and-moving",
  },
  TRUCKING: { name: "Trucking", url: "/nexmove-trucking" },
  STORAGE: { name: "Storage", url: "/nexmove-storage" },
  COURIER: { name: "Courier", url: "/nexmove-courier" },
  AMBULANCE: { name: "Air Ambulance", url: "/nexmove-air-ambulance" },
  CONTACT: { name: "Contact Us", url: "/contact" },
  FAQ: { name: "FAQ", url: "/faq" },
  GALLERY: { name: "Gallery", url: "/gallery" },
  MARKETPLACE: { name: "Marketplace", url: "/marketplace" },
  TRACK_SHIPMENT: { name: "Track Shipment", url: "/track-shipment" },
  PAY_NOW: { name: "Pay Now", url: "/pay-now" },
  PACKING_TIPS: { name: "Packing Tips", url: "/packing-tips" },
  RELOCATION_CHECKLIST: {
    name: "Relocation Checklist",
    url: "/relocation-checklist",
  },
  REQUEST_QUOTE: { name: "Request Quote", url: "/request-quote" },
  BLOG: { name: "Blog", url: "/blog" },
} as const
