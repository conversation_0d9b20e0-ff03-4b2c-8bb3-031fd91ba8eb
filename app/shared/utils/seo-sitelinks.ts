/**
 * SEO Sitelinks utilities for generating enhanced navigation structure
 * This helps Google understand the site structure and create sitelinks in search results
 */

import { SITE_CONFIG } from "./seo"

export interface SitelinkItem {
  name: string
  description: string
  url: string
  position: number
  category?: string
}

/**
 * Generate SiteNavigationElement schema for enhanced sitelinks
 */
export function generateSiteNavigationSchema(): string {
  const navigationElements = [
    {
      "@type": "SiteNavigationElement",
      name: "About Us",
      description:
        "Learn about NexMove's mission, values, and professional logistics team",
      url: `${SITE_CONFIG.url}/about-us`,
      position: 1,
    },
    {
      "@type": "SiteNavigationElement",
      name: "Services & Pricing",
      description: "Comprehensive logistics services with transparent pricing",
      url: `${SITE_CONFIG.url}/services-and-pricing`,
      position: 2,
    },
    {
      "@type": "SiteNavigationElement",
      name: "Packing & Moving",
      description: "Professional household and office relocation services",
      url: `${SITE_CONFIG.url}/nexmove-packing-and-moving`,
      position: 3,
    },
    {
      "@type": "SiteNavigationElement",
      name: "Trucking Services",
      description: "Reliable freight and goods transportation across India",
      url: `${SITE_CONFIG.url}/nexmove-trucking`,
      position: 4,
    },
    {
      "@type": "SiteNavigationElement",
      name: "Storage Solutions",
      description: "Secure warehousing and storage facilities",
      url: `${SITE_CONFIG.url}/nexmove-storage`,
      position: 5,
    },
    {
      "@type": "SiteNavigationElement",
      name: "Courier Services",
      description: "Fast and reliable parcel delivery services",
      url: `${SITE_CONFIG.url}/nexmove-courier`,
      position: 6,
    },
    {
      "@type": "SiteNavigationElement",
      name: "Air Ambulance",
      description: "Emergency medical transportation services",
      url: `${SITE_CONFIG.url}/nexmove-air-ambulance`,
      position: 7,
    },
    {
      "@type": "SiteNavigationElement",
      name: "Track Shipment",
      description: "Real-time shipment tracking and status updates",
      url: `${SITE_CONFIG.url}/track-shipment`,
      position: 8,
    },
    {
      "@type": "SiteNavigationElement",
      name: "Get Quote",
      description: "Get instant quotes for moving and logistics services",
      url: `${SITE_CONFIG.url}/request-quote`,
      position: 9,
    },
    {
      "@type": "SiteNavigationElement",
      name: "Contact Us",
      description: "Get in touch with our logistics experts",
      url: `${SITE_CONFIG.url}/contact`,
      position: 10,
    },
  ]

  const schema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    name: SITE_CONFIG.name,
    url: SITE_CONFIG.url,
    mainEntity: {
      "@type": "Organization",
      name: SITE_CONFIG.name,
      url: SITE_CONFIG.url,
    },
    hasPart: navigationElements,
  }

  return JSON.stringify(schema)
}

/**
 * Main service categories for enhanced sitelinks structure
 */
export const MAIN_SERVICE_CATEGORIES = [
  {
    name: "Moving Services",
    description: "Professional packing and moving solutions",
    url: "/nexmove-packing-and-moving",
    subcategories: [
      {
        name: "Household Moving",
        url: "/nexmove-packing-and-moving#household",
      },
      {
        name: "Office Relocation",
        url: "/nexmove-packing-and-moving#office",
      },
      {
        name: "Local Moving",
        url: "/nexmove-packing-and-moving#local",
      },
    ],
  },
  {
    name: "Transport Services",
    description: "Reliable freight and logistics solutions",
    url: "/nexmove-trucking",
    subcategories: [
      {
        name: "Full Load",
        url: "/nexmove-trucking#full-load",
      },
      {
        name: "Part Load",
        url: "/nexmove-trucking#part-load",
      },
      {
        name: "Express Delivery",
        url: "/nexmove-trucking#express",
      },
    ],
  },
  {
    name: "Storage & Warehousing",
    description: "Secure storage and warehousing facilities",
    url: "/nexmove-storage",
    subcategories: [
      {
        name: "Short Term Storage",
        url: "/nexmove-storage#short-term",
      },
      {
        name: "Long Term Storage",
        url: "/nexmove-storage#long-term",
      },
      {
        name: "Climate Controlled",
        url: "/nexmove-storage#climate-controlled",
      },
    ],
  },
  {
    name: "Specialized Services",
    description: "Courier and emergency medical transport",
    url: "/services-and-pricing",
    subcategories: [
      {
        name: "Courier Services",
        url: "/nexmove-courier",
      },
      {
        name: "Air Ambulance",
        url: "/nexmove-air-ambulance",
      },
      {
        name: "Express Delivery",
        url: "/nexmove-courier#express",
      },
    ],
  },
] as const

/**
 * Important pages for sitelinks enhancement
 */
export const SITELINK_PRIORITY_PAGES = [
  {
    name: "Services & Pricing",
    description: "View all our logistics services and transparent pricing",
    url: "/services-and-pricing",
    priority: 1.0,
    category: "Services",
  },
  {
    name: "Get Instant Quote",
    description: "Get free instant quotes for your moving needs",
    url: "/request-quote",
    priority: 0.9,
    category: "Quote",
  },
  {
    name: "Track Your Shipment",
    description: "Real-time tracking for your shipments and deliveries",
    url: "/track-shipment",
    priority: 0.9,
    category: "Tracking",
  },

  {
    name: "Contact Us",
    description: "Get in touch with our logistics experts",
    url: "/contact",
    priority: 0.8,
    category: "Contact",
  },
  {
    name: "About NexMove",
    description: "Learn about our company, mission, and values",
    url: "/about-us",
    priority: 0.8,
    category: "Company",
  },
  {
    name: "FAQ",
    description: "Find answers to frequently asked questions",
    url: "/faq",
    priority: 0.7,
    category: "Help",
  },
  {
    name: "Gallery",
    description: "View our service gallery and customer testimonials",
    url: "/gallery",
    priority: 0.7,
    category: "Portfolio",
  },
] as const
