import axios from "axios"

/**
 * Generate a random 6-digit OTP
 */
function generateOtp(): string {
  return Math.floor(100000 + Math.random() * 900000).toString()
}

/**
 * Send OTP using Fast2SMS API (without DLT template), generate and store OTP in sessionStorage
 * @param phone - Recipient's phone number (10 digits, India)
 * @returns Promise<boolean> - true if sent, false otherwise
 */
export async function sendOtp(phone: string): Promise<boolean> {
  const apiKey = process.env.FAST2SMS_API_KEY
  const route = process.env.FAST2SMS_ROUTE

  if (!apiKey && !route)
    throw new Error(
      "FAST2SMS_API_KEY and FAST2SMS_ROUTE are not set in environment variables",
    )

  // https://www.fast2sms.com/dev/bulkV2?authorization=5RXzsoKYWUj4AnkLPtuxdQb1DTgFB7VZy2H309MpIvhJOimNSc2d9nKXtpM0rkAPxsEiSN38FbY6efCy&route=dlt&sender_id=HALNEX&message=189889&variables_values=596823%7C&flash=0&numbers=7349246986&schedule_time=

  const otp = generateOtp()
  const message = `Your verification code for "Nex Move" is ${otp}. Please do not share this code with anyone.`
  const url = "https://www.fast2sms.com/dev/bulkV2"

  try {
    const response = await axios.post(
      url,
      {
        route,
        message,
        language: "english",
        numbers: phone,
        flash: 0,
      },
      {
        headers: {
          authorization: apiKey,
          "Content-Type": "application/json",
        },
      },
    )
    if (response.data && response.data.return === true) {
      if (typeof window !== "undefined" && window.sessionStorage) {
        window.sessionStorage.setItem("otp", otp)
        window.sessionStorage.setItem("otp_phone", phone)
      }
      return true
    }
    return false
  } catch (error) {
    console.error("Error sending OTP via Fast2SMS:", error)
    return false
  }
}

/**
 * Verify OTP from sessionStorage
 * @param inputOtp - OTP entered by user
 * @returns boolean
 */
export function verifyOtp(inputOtp: string): boolean {
  if (typeof window !== "undefined" && window.sessionStorage) {
    const storedOtp = window.sessionStorage.getItem("otp")
    return inputOtp === storedOtp
  }
  return false
}
