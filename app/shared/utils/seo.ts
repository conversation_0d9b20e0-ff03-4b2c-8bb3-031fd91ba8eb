/**
 * Comprehensive SEO utilities for NexMove website
 * Domain: nex-move.com
 */

import { generateSiteNavigationSchema as generateSitelinkNavSchema } from "./seo-sitelinks"

export const SITE_CONFIG = {
  domain: "nex-move.com",
  url: "https://nex-move.com",
  name: "NexMove",
  title: "Global Moving & Logistics Services | NexMove",
  description:
    "NexMove delivers seamless moving, storage, transportation & relocation services across India and globally. Over a decade of trusted logistics expertise.",
  keywords: [
    "moving services",
    "packing and moving",
    "logistics services",
    "trucking services",
    "storage solutions",
    "courier services",
    "air ambulance",
    "relocation services",
    "professional movers",
    "transport services",
    "India logistics",
    "freight services",
    "global moving",
    "international relocation",
    "domestic moving",
  ],
  contact: {
    phone: "+91-1800-419-5949",
    email: "<EMAIL>",
    address: "ITC Info Tech Park Bangalore, Karnataka, IN 560005",
  },
  social: {
    facebook: "https://www.facebook.com/nexmove2024/",
    twitter: "https://x.com/nex_move",
    linkedin: "https://www.linkedin.com/company/nexmove-hallef/",
    instagram: "https://www.instagram.com/nexmove2024/",
    youtube: "https://www.youtube.com/@nex-move",
  },
} as const

export interface SEOProps {
  title?: string
  description?: string
  keywords?: readonly string[] | string[]
  image?: string
  url?: string
  type?: "website" | "article" | "service"
  noIndex?: boolean
  canonical?: string
  logo?: string
}

export function generateSEOMetadata(props: SEOProps = {}) {
  const {
    title = SITE_CONFIG.title,
    description = SITE_CONFIG.description,
    keywords = SITE_CONFIG.keywords,
    // logo = `${SITE_CONFIG.url}/svgs/logo.svg`,
    url = SITE_CONFIG.url,
    type = "website",
    noIndex = false,
    canonical,
    image = `${SITE_CONFIG.url}/images/og-image.webp`,
  } = props

  const fullTitle =
    title === SITE_CONFIG.title ? title : `${title} | ${SITE_CONFIG.name}`

  return {
    title: fullTitle,
    description,
    keywords: keywords.join(", "),
    robots: noIndex ? "noindex,nofollow" : "index,follow",
    canonical: canonical || url,
    openGraph: {
      title: fullTitle,
      description,
      url,
      siteName: SITE_CONFIG.name,
      images: [
        {
          url: image,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      locale: "en_IN",
      type,
    },
    twitter: {
      card: "summary_large_image",
      title: fullTitle,
      description:
        description.length > 160
          ? description.substring(0, 157) + "..."
          : description,
      images: [image],
      creator: "@nex_move",
      site: "@nex_move",
    },
    alternates: {
      canonical: canonical || url,
    },
  }
}

// Schema.org structured data generators
export function generateOrganizationSchema() {
  return {
    "@context": "https://schema.org",
    "@type": ["Moving", "Logistics"],

    name: SITE_CONFIG.name,
    url: SITE_CONFIG.url,
    logo: `${SITE_CONFIG.url}/svgs/logo.svg`,
    description:
      "Offering reliable relocation, storage and transportation services across India and globally with over a decade of experience.",
    telephone: SITE_CONFIG.contact.phone,
    email: SITE_CONFIG.contact.email,
    foundingDate: "2010", // Update with actual founding year
    serviceArea: ["India", "Worldwide"],
    address: {
      "@type": "PostalAddress",
      streetAddress: "ITC Info Tech Park",
      addressLocality: "Bangalore",
      addressRegion: "Karnataka",
      postalCode: "560005",
      addressCountry: "IN",
    },
    hasOfferCatalog: {
      "@type": "OfferCatalog",
      name: "Moving & Logistics Services",
      itemListElement: [
        {
          "@type": "Offer",
          itemOffered: {
            "@type": "Service",
            name: "Domestic Relocation",
            description:
              "Professional home and office moving services within India",
          },
        },
        {
          "@type": "Offer",
          itemOffered: {
            "@type": "Service",
            name: "International Moving",
            description: "Global relocation services for international moves",
          },
        },
        {
          "@type": "Offer",
          itemOffered: {
            "@type": "Service",
            name: "Storage Solutions",
            description: "Secure storage and warehousing facilities",
          },
        },
        {
          "@type": "Offer",
          itemOffered: {
            "@type": "Service",
            name: "Specialized Transportation",
            description: "Trucking, courier and specialized transport services",
          },
        },
      ],
    },
    sameAs: [
      SITE_CONFIG.social.facebook,
      SITE_CONFIG.social.twitter,
      SITE_CONFIG.social.linkedin,
      SITE_CONFIG.social.instagram,
      SITE_CONFIG.social.youtube,
    ],
  }
}

export function generateServiceSchema(service: {
  name: string
  description: string
  url: string
  price?: string
  area?: string[]
}) {
  return {
    "@context": "https://schema.org",
    "@type": "Service",
    name: service.name,
    description: service.description,
    url: `${SITE_CONFIG.url}${service.url}`,
    provider: {
      "@type": "Organization",
      name: SITE_CONFIG.name,
      url: SITE_CONFIG.url,
    },
    areaServed: service.area || ["India"],
    hasOfferCatalog: {
      "@type": "OfferCatalog",
      name: service.name,
      itemListElement: [
        {
          "@type": "Offer",
          itemOffered: {
            "@type": "Service",
            name: service.name,
          },
        },
      ],
    },
  }
}

export function generateWebSiteSchema() {
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    name: SITE_CONFIG.name,
    url: SITE_CONFIG.url,
    description: SITE_CONFIG.description,
    potentialAction: {
      "@type": "SearchAction",
      target: {
        "@type": "EntryPoint",
        urlTemplate: `${SITE_CONFIG.url}/search?q={search_term_string}`,
      },
      "query-input": "required name=search_term_string",
    },
  }
}

// Enhanced schema generators for sitelinks
export function generateSiteNavigationSchema() {
  return generateSitelinkNavSchema()
}

// Page-specific SEO configurations
export const PAGE_SEO = {
  HOME: {
    title: "Global Moving & Logistics Services | Nex‑Move",
    description:
      "Nex‑Move delivers seamless moving, storage, transportation & relocation services across India and globally. Over a decade of trusted logistics expertise.",
    keywords: [
      "moving services india",
      "professional movers",
      "logistics company",
      "packing moving services",
      "transport services",
      "global moving services",
      "relocation services",
      "international moving",
      "domestic relocation",
    ],
  },
  ABOUT: {
    title: "About NexMove - Leading Logistics & Moving Company",
    description:
      "Learn about NexMove's mission, vision, and commitment to providing exceptional moving and logistics services across India. Our story of reliable transportation solutions.",
    keywords: [
      "about nexmove",
      "moving company history",
      "logistics company profile",
      "professional movers india",
    ],
  },
  SERVICES: {
    title: "Moving & Logistics Services - Pricing & Packages | NexMove",
    description:
      "Comprehensive moving services including packing, trucking, storage, courier & air ambulance. Transparent pricing, professional handling, and nationwide coverage.",
    keywords: [
      "moving services pricing",
      "logistics packages",
      "transport rates",
      "moving cost india",
    ],
  },
  PACKING_MOVING: {
    title: "Professional Packing & Moving Services | NexMove",
    description:
      "Expert packing and moving services for homes and offices. Safe transportation, quality packing materials, and timely delivery across India.",
    keywords: [
      "packing moving services",
      "home relocation",
      "office moving",
      "professional packers",
      "household shifting",
    ],
  },
  TRUCKING: {
    title: "Trucking & Part-Load Services | NexMove",
    description:
      "Reliable trucking services for full and part loads. Vehicle booking, flexible hire options, and efficient goods transportation across India.",
    keywords: [
      "trucking services",
      "part load transport",
      "goods vehicle booking",
      "freight services",
      "cargo transport",
    ],
  },
  STORAGE: {
    title: "Storage & Warehousing Solutions | NexMove",
    description:
      "Secure storage facilities with climate control, 24/7 security, and flexible storage plans. Professional warehousing solutions for all your needs.",
    keywords: [
      "storage services",
      "warehousing solutions",
      "secure storage",
      "climate controlled storage",
      "inventory management",
    ],
  },
  COURIER: {
    title: "Courier & Parcel Services | NexMove",
    description:
      "Fast and reliable courier services for documents and parcels. Same-day delivery, express shipping, and secure parcel handling.",
    keywords: [
      "courier services",
      "parcel delivery",
      "express shipping",
      "document courier",
      "fast delivery",
    ],
  },
  AMBULANCE: {
    title: "Air Ambulance & Medical Transport Services | NexMove",
    description:
      "Emergency air ambulance services with trained medical professionals. Safe and dignified medical transport and human remains services.",
    keywords: [
      "air ambulance",
      "medical transport",
      "emergency services",
      "patient transfer",
      "medical evacuation",
    ],
  },
  CONTACT: {
    title: "Contact NexMove - Get Moving Quote & Support",
    description:
      "Contact NexMove for moving quotes, customer support, and logistics solutions. Multiple contact options for your convenience.",
    keywords: [
      "contact nexmove",
      "moving quote",
      "customer support",
      "logistics inquiry",
      "get quote",
    ],
  },
  FAQ: {
    title: "Frequently Asked Questions | NexMove",
    description:
      "Find answers to common questions about our moving, logistics, and transport services. Get quick solutions to your queries.",
    keywords: [
      "moving faq",
      "logistics questions",
      "shipping queries",
      "transport help",
      "moving guide",
    ],
  },
  GALLERY: {
    title: "Gallery - Our Moving & Logistics Services | NexMove",
    description:
      "View our gallery showcasing professional moving, packing, and logistics operations. See our equipment, facilities, and service quality.",
    keywords: [
      "moving gallery",
      "logistics photos",
      "service showcase",
      "moving equipment",
      "packing photos",
    ],
  },
  MARKETPLACE: {
    title: "NexMove Marketplace - Logistics Service Providers",
    description:
      "Connect with verified logistics service providers on NexMove marketplace. Find trusted partners for your moving and transport needs.",
    keywords: [
      "logistics marketplace",
      "service providers",
      "moving partners",
      "transport network",
      "logistics platform",
    ],
  },
  TRACK_SHIPMENT: {
    title: "Track Your Shipment | NexMove",
    description:
      "Track your shipment in real-time with NexMove's advanced tracking system. Get live updates on your package delivery status.",
    keywords: [
      "track shipment",
      "package tracking",
      "delivery status",
      "shipment updates",
      "logistics tracking",
    ],
  },
  PAY_NOW: {
    title: "Payment Portal | NexMove",
    description:
      "Secure online payment portal for NexMove services. Multiple payment options for your convenience and security.",
    keywords: [
      "nexmove payment",
      "online payment",
      "secure payment",
      "pay logistics bill",
      "moving payment",
    ],
  },
  PACKING_TIPS: {
    title: "Packing Tips & Moving Guide | NexMove",
    description:
      "Expert packing tips and moving guides to help you prepare for your relocation. Professional advice for safe and efficient packing.",
    keywords: [
      "packing tips",
      "moving guide",
      "packing advice",
      "relocation tips",
      "moving checklist",
    ],
  },
  RELOCATION_CHECKLIST: {
    title: "Complete Relocation Checklist | NexMove",
    description:
      "Comprehensive relocation checklist to ensure a smooth moving experience. Step-by-step guide for stress-free relocation.",
    keywords: [
      "relocation checklist",
      "moving checklist",
      "relocation guide",
      "moving preparation",
      "relocation planning",
    ],
  },
  REQUEST_QUOTE: {
    title: "Request Moving Quote | NexMove",
    description:
      "Get instant moving quotes for all logistics services. Free, no-obligation estimates for your packing, moving, and transport needs.",
    keywords: [
      "moving quote",
      "logistics quote",
      "transport estimate",
      "moving cost calculator",
      "instant quote",
    ],
  },
  BLOG: {
    title: "Moving & Logistics Blog | NexMove",
    description:
      "Latest updates, tips, and insights on moving, logistics, and transportation. Expert advice and industry news.",
    keywords: [
      "moving blog",
      "logistics news",
      "transport tips",
      "relocation advice",
      "logistics updates",
    ],
  },
} as const
