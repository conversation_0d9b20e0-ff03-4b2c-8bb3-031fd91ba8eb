// Email templates for NexMove forms

export function getContactEmailHtml(details: {
  name: string
  emailId: string
  phone: string
  subject: string
  reference: string
  message: string
  preferredContactMethod: string
}): string {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #1e293b;">New Contact Request</h2>
      <table style="width: 100%; border-collapse: collapse;">
        <tr><td style="font-weight: bold; padding: 4px 0;">Name:</td><td>${details.name}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Email:</td><td>${details.emailId}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Phone:</td><td>${details.phone}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Subject:</td><td>${details.subject}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Reference:</td><td>${details.reference}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Preferred Contact Method:</td><td>${details.preferredContactMethod}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0; vertical-align: top;">Message:</td><td style="white-space: pre-line;">${details.message}</td></tr>
      </table>
      <p style="color: #64748b; font-size: 13px; margin-top: 24px;">This message was sent from the NexMove website contact form.</p>
    </div>
  `
}

export function getRequestQuoteEmailHtml(details: {
  name: string
  emailId: string
  phone: string
  service: string
  subCategory: string
  origin: string
  destination: string
  distanceInKm: number
  preferredContactMethod: string
  message: string
}): string {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #1e293b;">New Quote Request</h2>
      <table style="width: 100%; border-collapse: collapse;">
        <tr><td style="font-weight: bold; padding: 4px 0;">Name:</td><td>${details.name}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Email:</td><td>${details.emailId}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Phone:</td><td>${details.phone}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Service:</td><td>${details.service}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Sub Category:</td><td>${details.subCategory}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Origin:</td><td>${details.origin}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Destination:</td><td>${details.destination}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Distance (km):</td><td>${details.distanceInKm}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Preferred Contact Method:</td><td>${details.preferredContactMethod}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0; vertical-align: top;">Message:</td><td style="white-space: pre-line;">${details.message}</td></tr>
      </table>
      <p style="color: #64748b; font-size: 13px; margin-top: 24px;">This message was sent from the NexMove website request quote form.</p>
    </div>
  `
}

export function getPackingAndMovingEmailHtml(details: {
  name: string
  emailId: string
  phone: string
  sourceLocation: string
  destinationLocation: string
  distance: number
  houseHoldCapacityId: number
  vehicleId: number
  packageTypeId: number
  requireInsurance: boolean
  goodsValue: string
  LabourCount: number
  LabourDays: number
}): string {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #1e293b;">New Packing & Moving Request</h2>
      <table style="width: 100%; border-collapse: collapse;">
        <tr><td style="font-weight: bold; padding: 4px 0;">Name:</td><td>${details.name}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Email:</td><td>${details.emailId}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Phone:</td><td>${details.phone}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Source Location:</td><td>${details.sourceLocation}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Destination Location:</td><td>${details.destinationLocation}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Distance (km):</td><td>${details.distance}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Household Capacity ID:</td><td>${details.houseHoldCapacityId}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Vehicle ID:</td><td>${details.vehicleId}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Package Type ID:</td><td>${details.packageTypeId}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Require Insurance:</td><td>${details.requireInsurance ? "Yes" : "No"}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Goods Value:</td><td>${details.goodsValue}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Labour Count:</td><td>${details.LabourCount}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Labour Days:</td><td>${details.LabourDays}</td></tr>
      </table>
      <p style="color: #64748b; font-size: 13px; margin-top: 24px;">This message was sent from the NexMove Packing & Moving form.</p>
    </div>
  `
}

export function getTruckingEmailHtml(details: {
  name: string
  emailId: string
  phone: string
  sourceLocation: string
  destinationLocation: string
  distance: number
  truckingTypeId: number
  vehicleId: number
  goodsTypeId: number
  requireInsurance: boolean
  goodsValue: string
  labourCount: number
  labourDays: number
}): string {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #1e293b;">New Trucking Request</h2>
      <table style="width: 100%; border-collapse: collapse;">
        <tr><td style="font-weight: bold; padding: 4px 0;">Name:</td><td>${details.name}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Email:</td><td>${details.emailId}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Phone:</td><td>${details.phone}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Source Location:</td><td>${details.sourceLocation}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Destination Location:</td><td>${details.destinationLocation}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Distance (km):</td><td>${details.distance}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Trucking Type ID:</td><td>${details.truckingTypeId}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Vehicle ID:</td><td>${details.vehicleId}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Goods Type ID:</td><td>${details.goodsTypeId}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Require Insurance:</td><td>${details.requireInsurance ? "Yes" : "No"}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Goods Value:</td><td>${details.goodsValue}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Labour Count:</td><td>${details.labourCount}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Labour Days:</td><td>${details.labourDays}</td></tr>
      </table>
      <p style="color: #64748b; font-size: 13px; margin-top: 24px;">This message was sent from the NexMove Trucking form.</p>
    </div>
  `
}

export function getCourierParcelEmailHtml(details: {
  name: string
  emailId: string
  phone: string
  sourceLocation: string
  destinationLocation: string
  distance: number
  documentTypeId: number
  weightInKgs: number
  widthInCms: number
  heightInCms: number
  lengthInCms: number
  isRiskSurchargeByCarrier: boolean
  goodsValue: number
}): string {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #1e293b;">New Courier & Parcel Request</h2>
      <table style="width: 100%; border-collapse: collapse;">
        <tr><td style="font-weight: bold; padding: 4px 0;">Name:</td><td>${details.name}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Email:</td><td>${details.emailId}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Phone:</td><td>${details.phone}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Source Location:</td><td>${details.sourceLocation}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Destination Location:</td><td>${details.destinationLocation}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Distance (km):</td><td>${details.distance}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Document Type ID:</td><td>${details.documentTypeId}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Weight (kg):</td><td>${details.weightInKgs}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Length (cm):</td><td>${details.lengthInCms}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Width (cm):</td><td>${details.widthInCms}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Height (cm):</td><td>${details.heightInCms}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Goods Value:</td><td>${details.goodsValue}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Risk Surcharge by Carrier:</td><td>${details.isRiskSurchargeByCarrier ? "Yes" : "No"}</td></tr>
      </table>
      <p style="color: #64748b; font-size: 13px; margin-top: 24px;">This message was sent from the NexMove Courier & Parcel form.</p>
    </div>
  `
}

export function getStorageEmailHtml(details: {
  name: string
  emailId: string
  phone: string
  sourceLocation: string
  destinationLocation: string
  distance: number
  houseHoldCapacityId: number
  vehicleId: number
  packageTypeId: number
  requireInsurance: boolean
  goodsValue: string
  labourCount: number
  labourDays: number
  storageDurationInDays: number
}): string {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #1e293b;">New Storage Request</h2>
      <table style="width: 100%; border-collapse: collapse;">
        <tr><td style="font-weight: bold; padding: 4px 0;">Name:</td><td>${details.name}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Email:</td><td>${details.emailId}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Phone:</td><td>${details.phone}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Source Location:</td><td>${details.sourceLocation}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Destination Location:</td><td>${details.destinationLocation}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Distance (km):</td><td>${details.distance}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Household Capacity ID:</td><td>${details.houseHoldCapacityId}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Vehicle ID:</td><td>${details.vehicleId}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Package Type ID:</td><td>${details.packageTypeId}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Require Insurance:</td><td>${details.requireInsurance ? "Yes" : "No"}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Goods Value:</td><td>${details.goodsValue}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Labour Count:</td><td>${details.labourCount}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Labour Days:</td><td>${details.labourDays}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Storage Duration (days):</td><td>${details.storageDurationInDays}</td></tr>
      </table>
      <p style="color: #64748b; font-size: 13px; margin-top: 24px;">This message was sent from the NexMove Storage form.</p>
    </div>
  `
}

export function getAirAmbulanceEmailHtml(details: {
  name: string
  emailId: string
  phone: string
  regionType: string
  sourceCity: string
  destinationCity: string
  cargoWeightInKgs: string | number
}): string {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #1e293b;">New Air Ambulance Request</h2>
      <table style="width: 100%; border-collapse: collapse;">
        <tr><td style="font-weight: bold; padding: 4px 0;">Name:</td><td>${details.name}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Email:</td><td>${details.emailId}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Phone:</td><td>${details.phone}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Region:</td><td>${details.regionType}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Source City:</td><td>${details.sourceCity}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Destination City:</td><td>${details.destinationCity}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Cargo Weight (kg):</td><td>${details.cargoWeightInKgs}</td></tr>
      </table>
      <p style="color: #64748b; font-size: 13px; margin-top: 24px;">This message was sent from the NexMove Air Ambulance form.</p>
    </div>
  `
}

export function getJobApplicationEmailHtml(details: {
  name: string
  emailId: string
  phone: string
}): string {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #1e293b;">New Job Application Received</h2>
      <table style="width: 100%; border-collapse: collapse;">
        <tr><td style="font-weight: bold; padding: 4px 0;">Name:</td><td>${details.name}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Email:</td><td>${details.emailId}</td></tr>
        <tr><td style="font-weight: bold; padding: 4px 0;">Phone:</td><td>${details.phone}</td></tr>
      </table>
      <p style="color: #64748b; font-size: 13px; margin-top: 24px;">A new job application was submitted via the NexMove website.</p>
    </div>
  `
}
