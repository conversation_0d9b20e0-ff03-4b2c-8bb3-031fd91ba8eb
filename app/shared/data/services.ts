/**
 * Static services data for the quote calculator
 */

export interface Service {
  id: number
  name: string
  code: string
  icon: string
  href: string
}

/**
 * Get a service by its ID
 * @param id The service ID to look for
 * @returns The service object or undefined if not found
 */
export const getServiceById = (id: string | number): Service | undefined => {
  // Convert id to string for comparison
  const idStr = id?.toString()
  return services.find((service) => service.id.toString() === idStr)
}

export const services: Service[] = [
  {
    id: 1,
    name: "NEX Packing & Moving Services",
    code: "NEX-PAM",
    icon: "https://i.ibb.co/cDmvzLR/moving-home.webp",
    href: "/services/packing-and-moving",
  },
  {
    id: 4,
    name: "NEX COURIER & PARCEL SERVICE",
    code: "NEX-CPS",
    icon: "https://i.ibb.co/RvPXZJN/cargo-ship.webp",
    href: "/services/courier-and-parcel",
  },
  {
    id: 3,
    name: "NEX Trucking Services",
    code: "NEX-TRK",
    icon: "https://i.ibb.co/2Ktw8Fq/Trucking.webp",
    href: "/services/trucking",
  },
  {
    id: 5,
    name: "NEX AMBULANCE SERVICE",
    code: "NEX-AMB",
    icon: "https://i.ibb.co/DMm3Dfz/plane.webp",
    href: "/services/air-ambulance",
  },
  {
    id: 2,
    name: "NEX STORAGE SERVICES",
    code: "NEX-STG",
    icon: "https://i.ibb.co/GWWhJ3N/WArehousing.webp",
    href: "/services/storage",
  },
]
