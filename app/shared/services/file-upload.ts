import axiosInstance from "../axios"

/**
 * Upload a file to the server
 * @param file The file to upload
 * @param directory Optional directory to upload to
 * @returns Promise with the uploaded file path
 */

export const uploadFile = async (
  file: File,
  directory?: string,
): Promise<string> => {
  try {
    const formData = new FormData()
    formData.append("file", file)

    if (directory) {
      formData.append("directory", directory)
    }

    const response = await axiosInstance.post("/api/files/upload", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    })

    return response.data.url
  } catch (error) {
    console.error("Error uploading file:", error)
    throw error
  }
}

/**
 * Get the download URL for a file
 * @param filePath The path of the file to download
 * @returns The download URL
 */
export const getFileDownloadUrl = (filePath: string): string => {
  return `/api/files/download?filepath=${encodeURIComponent(filePath)}`
}
