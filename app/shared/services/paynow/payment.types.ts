// Types for payment service
import type { PayNowFormValues } from "@/app/components/forms/PayNowForm"

export interface PayNowSubmissionResponse {
  success: boolean
  message?: string
  paymentId?: string
}

export interface PaymentDetails {
  paymentId: string
  payment_link: string
  status: string
  [key: string]: string | number | boolean | null | undefined
}

export type SubmitPayNowFormPayload = PayNowFormValues
