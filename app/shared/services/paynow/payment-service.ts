import type {
  PayNowSubmissionResponse,
  PaymentDetails,
  SubmitPayNowFormPayload,
} from "./payment.types"

// Submit PayNow form details to backend
export async function submitPayNowForm(
  payload: SubmitPayNowFormPayload,
): Promise<PayNowSubmissionResponse> {
  try {
    const res = await fetch("/api/pay-now/submit", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(payload),
    })
    const data = await res.json()
    if (!res.ok || !data.success) {
      throw new Error(data.message || "Failed to submit payment details")
    }
    return data
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : "Unknown error",
    }
  }
}

// Fetch payment details by paymentId (from Razorpay API via our backend)
export async function fetchPaymentDetails(
  paymentId: string,
): Promise<PaymentDetails> {
  try {
    const res = await fetch(
      `/api/pay-now?pay-id=${encodeURIComponent(paymentId)}`,
    )
    const data = await res.json()
    if (!res.ok || !data.payment_link) {
      throw new Error(data.error || "Failed to fetch payment details")
    }
    return data
  } catch (error) {
    throw new Error(
      error instanceof Error
        ? error.message
        : "Unknown error fetching payment details",
    )
  }
}
