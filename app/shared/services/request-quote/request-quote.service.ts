import axios from "@/app/shared/axios"
import { AxiosResponse } from "axios"
import { Service } from "../../data/services"
import { DropDownValue } from "../../types/DropDownValue"
import { RequestQuote } from "./request-quote.type"

/**
 * Submit a new quote request
 * @param postData The quote request data
 * @returns The created quote request
 */
export async function submitQuoteRequest(
  postData: RequestQuote,
): Promise<RequestQuote> {
  try {
    const response: AxiosResponse<RequestQuote> = await axios.post(
      "/api/QuoteRequests",
      postData,
    )
    return response.data
  } catch (error) {
    throw new Error(
      `Failed to submit Quote Request: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    )
  }
}

/**
 * Get all services with their subcategories
 * @returns Array of services with subcategories
 */
export const getServicesWithSubcategories = async () => {
  try {
    const response = await axios.get(
      "/api/masterdata/services?includeSubCategories=true",
    )
    return response.data
  } catch (error) {
    throw new Error(
      `Failed to fetch Services: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    )
  }
}

/**
 * Transform services data for dropdown
 * @param servicesData Raw services data from API
 * @returns Formatted services data for dropdown
 */
export const formatServicesForDropdown = (
  servicesData: Service[],
): DropDownValue[] => {
  return servicesData.map((service) => ({
    id: service.id,
    name: service.name,
    label: service.name,
    value: service.name,
  }))
}
