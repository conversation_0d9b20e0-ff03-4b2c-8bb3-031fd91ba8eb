export type EmailPayload = {
  subject: string
  html: string
}

/**
 * Sends an email by calling the backend API route.
 * @param payload Email subject and HTML body
 * @returns Promise<{ success: boolean; message?: string }>
 */
export async function sendEmail(
  payload: EmailPayload,
): Promise<{ success: boolean; message?: string }> {
  try {
    const response = await fetch("/api/send-email", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(payload),
    })
    if (!response.ok) {
      const error = await response.json().catch(() => ({}))
      return {
        success: false,
        message: error.message || "Failed to send email.",
      }
    }
    return { success: true }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) {
    console.error("Error sending email:", error)
    return {
      success: false,
      message: error?.message || "Failed to send email.",
    }
  }
}
