import {
  GOOGLE_REVIEWS,
  GoogleApiReview,
  GoogleReview,
  GoogleReviewResponse,
} from "@/config"

/**
 * Fetches Google reviews for a business using the Google My Business API
 * Falls back to mock data if the API call fails
 */
export const getGoogleReviews = async (): Promise<GoogleReviewResponse> => {
  try {
    // Check if we have the required API key and credentials
    if (!process.env.GOOGLE_BUSINESS_API_TOKEN) {
      console.warn("Google Business API token not found, using mock data")
      return formatMockReviews()
    }

    // Your Google My Business account and location IDs
    const accountId = process.env.GOOGLE_BUSINESS_ACCOUNT_ID
    const locationId = process.env.GOOGLE_BUSINESS_LOCATION_ID

    if (!accountId || !locationId) {
      console.warn(
        "Google Business account or location ID not found, using mock data",
      )
      return formatMockReviews()
    }

    // Fetch reviews from Google My Business API
    const response = await fetch(
      `https://mybusiness.googleapis.com/v4/accounts/${accountId}/locations/${locationId}/reviews?pageSize=50`,
      {
        headers: {
          Authorization: `Bearer ${process.env.GOOGLE_BUSINESS_API_TOKEN}`,
        },
        next: { revalidate: 3600 }, // Cache for 1 hour
      },
    )

    if (!response.ok) {
      throw new Error(`Failed to fetch Google reviews: ${response.statusText}`)
    }

    const data = await response.json()

    // Check if we have valid data
    if (!data.reviews || data.reviews.length === 0) {
      console.warn("No reviews found in Google API response, using mock data")
      return formatMockReviews()
    }

    const reviews: GoogleReview[] = (data.reviews as GoogleApiReview[]).map(
      (review: GoogleApiReview): GoogleReview => ({
        id: review.reviewId || review.name.split("/").pop()!,
        name: review.reviewer.displayName,
        profileImage: review.reviewer.profilePhotoUrl || "/images/avatar.webp",
        rating: review.starRating,
        review: review.comment,
        date: formatReviewDate(review.createTime),
      }),
    )

    return {
      reviews,
      averageRating: data.averageRating || 4.8,
      totalReviews: data.totalReviewCount || reviews.length,
    }
  } catch (error) {
    console.error("Error fetching Google reviews:", error)
    return formatMockReviews()
  }
}

/**
 * Formats the mock review data to match the API response format
 */
const formatMockReviews = (): GoogleReviewResponse => {
  // Calculate average rating from mock data
  const totalRating = GOOGLE_REVIEWS.reduce(
    (sum, review) => sum + review.rating,
    0,
  )
  const averageRating = totalRating / GOOGLE_REVIEWS.length

  return {
    reviews: GOOGLE_REVIEWS.map((review) => ({
      ...review,
      id: review.id.toString(),
    })),
    averageRating,
    totalReviews: GOOGLE_REVIEWS.length,
  }
}

/**
 * Formats the review date from Google's timestamp
 */
const formatReviewDate = (timestamp: string): string => {
  if (!timestamp) return "Recently"

  try {
    const date = new Date(timestamp)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays < 7) {
      return diffDays <= 1 ? "1 day ago" : `${diffDays} days ago`
    } else if (diffDays < 30) {
      const weeks = Math.floor(diffDays / 7)
      return weeks === 1 ? "1 week ago" : `${weeks} weeks ago`
    } else if (diffDays < 365) {
      const months = Math.floor(diffDays / 30)
      return months === 1 ? "1 month ago" : `${months} months ago`
    } else {
      const years = Math.floor(diffDays / 365)
      return years === 1 ? "1 year ago" : `${years} years ago`
    }
  } catch (error) {
    console.error("Error formatting review date:", error)
    return "Recently"
  }
}
