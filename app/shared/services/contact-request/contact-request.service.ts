import { AxiosResponse } from "axios"
import axios from "@/app/shared/axios"

import { DropDownValue } from "../../types/DropDownValue"
import { ContactRequest } from "./contact-request.type"

export async function addContactRequest(
  postData: ContactRequest,
): Promise<ContactRequest> {
  try {
    const response: AxiosResponse<ContactRequest> = await axios.post(
      "/api/ContactRequests",
      postData,
    )
    return response.data
  } catch (error) {
    throw new Error(
      `Failed to add Contact Request: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    )
  }
}

export const getSubject = async (): Promise<DropDownValue[]> => {
  try {
    const response: AxiosResponse<DropDownValue[]> = await axios.get(
      "/api/masterdata/subjects",
    )
    return response.data
  } catch (error) {
    throw new Error(
      `Failed to fetch Subjects: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    )
  }
}

export const getRefernce = async (): Promise<DropDownValue[]> => {
  try {
    const response: AxiosResponse<DropDownValue[]> = await axios.get(
      "/api/masterdata/references",
    )
    return response.data
  } catch (error) {
    throw new Error(
      `Failed to fetch References: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    )
  }
}

export const getContactMethods = async (): Promise<DropDownValue[]> => {
  try {
    const response: AxiosResponse<DropDownValue[]> = await axios.get(
      "/api/masterdata/contactmethods",
    )
    return response.data
  } catch (error) {
    throw new Error(
      `Failed to fetch Contact Methods: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    )
  }
}
