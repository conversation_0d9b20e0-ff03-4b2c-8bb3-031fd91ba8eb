import { Vehicle } from "../common"

// Define export types for the data structures
export type PackingAndMovingData = {
  sourceLocation: string
  destinationLocation: string
  requireInsurance: boolean
  houseHoldCapacityId: number
  vehicleId: number
  packageTypeId: number
  goodsValue?: string | number
  LabourCount: number
  LabourDays: number
  distance: number
  // User info fields
  name?: string
  emailId?: string
  phone?: string
  // Optional fields for backward compatibility
  pickup?: string
  dropoff?: string
  insurance?: boolean
  vehicle?: Vehicle
  isDifferentState?: boolean
  houseCapacity?: string
  packing?: string
}

// Cost item in response
export type CostItem = {
  name: string
  cost: number | string
  unit: string
}
