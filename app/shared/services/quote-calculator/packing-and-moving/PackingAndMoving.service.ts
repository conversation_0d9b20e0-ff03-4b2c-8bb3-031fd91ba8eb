import { QuoteResult } from "@/app/components/common/types/quote-result.types"
import axiosInstance from "@/app/shared/axios"
import axios from "axios"
import { TPackingType } from "../QuoteCalculator.type"
import { PackingAndMovingData } from "./PackingAndMoving.types"

//old one
// export async function relocationCalculationService({
//   data,
//   userId,
//   serviceId,
// }: {
//   userId: string | number;
//   data: RelocationData;
//   serviceId: string | number;
// }): Promise<TRelocationResponse> {
//   // Create estimates for backward compatibility if needed
//   const estimates = {
//     pickup: data.pickup || data.sourceLocation,
//     dropoff: data.dropoff || data.destinationLocation,
//     insurance: data.insurance || data.requireInsurance,
//     vehicle: data.vehicle,
//     houseCapacity: data.houseCapacity || "",
//     packing: data.packing || "",
//     distance: data.distance,
//     goodsValue: data.goodsValue,
//     isDifferentState: data.isDifferentState || false,
//   };

//   // Create the POST_DATA with the expected structure from the user
//   const POST_DATA = {
//     houseHoldCapacityId: data.houseHoldCapacityId,
//     vehicleId: data.vehicleId,
//     packageTypeId: data.packageTypeId,
//     LabourCount: data.LabourCount,
//     LabourDays: data.LabourDays,
//     sourceLocation: data.sourceLocation,
//     destinationLocation: data.destinationLocation,
//     distance: Math.round(data.distance),
//     requireInsurance: data.requireInsurance,
//     goodsValue:
//       data.requireInsurance && data.goodsValue ? Number(data.goodsValue) : 0,
//     // Include the original estimates for backward compatibility
//     estimates: JSON.stringify(estimates),
//     serviceId: serviceId,
//     userId: userId,
//   };

//   try {
//     const response = await axios.post(`${CALCULATE_URL}/relocation`, POST_DATA);
//     return response.data as TRelocationResponse;
//   } catch (error) {
//     if (axios.isAxiosError(error)) {
//       throw {
//         message:
//           error.response?.data?.error ||
//           error.message ||
//           "Failed to send data.",
//         status: error.response?.status || 500,
//       };
//     }
//     throw { message: "Failed to send data.", status: 500 };
//   }
// }

// Get household capacities with vehicles
export async function getHouseholdCapacities(serviceId: string | number) {
  try {
    const response = await axiosInstance.get(
      `/api/masterdata/HouseHoldCapacities/${serviceId}?withVehicles=true`,
    )
    return response.data
  } catch (error) {
    throw new Error(
      `Failed to fetch Household Capacities: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    )
  }
}

//Get package types
export async function getPackageTypes() {
  try {
    const response = await axiosInstance.get<TPackingType[]>(
      `/api/masterdata/packagetypes`,
    )
    return response.data
  } catch (error) {
    throw new Error(
      `Failed to fetch Subjects: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    )
  }
}

// New service for packing and moving calculation
export async function packingAndMovingCalculationService({
  data,
  userId,
  serviceId,
}: {
  userId: string | number
  data: PackingAndMovingData
  serviceId: string | number
}): Promise<QuoteResult> {
  // Create the payload with the expected structure
  const POST_DATA = {
    ...data,
    serviceId: serviceId,
    userId: userId,
    goodsValue: data.goodsValue ? Number(data.goodsValue) : 0,
  }

  try {
    const response = await axiosInstance.post(
      `/api/calculator/packingandmoving`,
      POST_DATA,
    )
    return response.data
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw {
        message:
          error.response?.data?.error ||
          error.message ||
          "Failed to send data.",
        status: error.response?.status || 500,
      }
    }
    throw { message: "Failed to send data.", status: 500 }
  }
}
