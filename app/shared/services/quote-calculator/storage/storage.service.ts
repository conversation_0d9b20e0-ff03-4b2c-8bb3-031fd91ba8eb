import axios from "axios"
import axiosInstance from "@/app/shared/axios"
import { StorageServiceProps } from "./storage.types"
import { QuoteResult } from "@/app/components/common/types/quote-result.types"

export async function storageCalculationService({
  data,
  userId,
  serviceId,
}: {
  userId: string | number
  data: StorageServiceProps
  serviceId: string | number
}): Promise<QuoteResult> {
  const POST_DATA = {
    HouseHoldCapacityId: data.HouseHoldCapacityId,
    VehicleId: data.VehicleId,
    PackageTypeId: data.PackageTypeId,
    LabourCount: data.LabourCount,
    LabourDays: data.LabourDays,
    SourceLocation: data.SourceLocation,
    DestinationLocation: data.DestinationLocation,
    Distance: data.Distance,
    RequireInsurance: data.RequireInsurance,
    GoodsValue: data.GoodsValue || 0,
    StorageDurationInDays: data.StorageDurationInDays,
    serviceId: serviceId,
    userId: userId,
    // Add user info to the request
    name: data.name,
    emailId: data.emailId,
    phone: data.phone,
  }

  try {
    const response = await axiosInstance.post(
      `/api/calculator/storage`,
      POST_DATA,
    )
    return response.data
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw {
        message:
          error.response?.data?.error ||
          error.message ||
          "Failed to send data.",
        status: error.response?.status || 500,
      }
    }
    throw { message: "Failed to send data.", status: 500 }
  }
}
