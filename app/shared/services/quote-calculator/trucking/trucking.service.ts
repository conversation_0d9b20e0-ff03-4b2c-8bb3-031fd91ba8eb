import { QuoteResult } from "@/app/components/common/types/quote-result.types"
import axiosInstance from "@/app/shared/axios"
import { DropDownValue } from "@/app/shared/types/DropDownValue"
import axios from "axios"
import { TruckingEstimationRequest, VehicleNew } from "./trucking.types"

export async function truckingCalculationService({
  data,
  userId,
  serviceId,
}: {
  userId: string | number
  data: TruckingEstimationRequest
  serviceId: string | number
}): Promise<QuoteResult> {
  try {
    const response = await axiosInstance.post(`/api/calculator/trucking`, {
      ...data,
      serviceId,
      userId,
    })
    return response.data
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw {
        message:
          error.response?.data?.error ||
          error.message ||
          "Failed to send data.",
        status: error.response?.status || 500,
      }
    }
    throw { message: "Failed to send data.", status: 500 }
  }
}

// Get trucking types
export async function getTruckingTypes(): Promise<DropDownValue[]> {
  try {
    const response = await axiosInstance.get("/api/masterdata/truckingTypes")
    return response.data
  } catch (error) {
    throw new Error(
      `Failed to fetch Trucking Types: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    )
  }
}

// Get goods types
export async function getGoodsTypes(): Promise<DropDownValue[]> {
  try {
    const response = await axiosInstance.get("/api/masterdata/goodsTypes")
    return response.data
  } catch (error) {
    throw new Error(
      `Failed to fetch Goods Types: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    )
  }
}

// Get vehicles
export async function getTruckingVehicles(): Promise<DropDownValue[]> {
  try {
    const response = await axiosInstance.get("/api/masterdata/vehicles")
    return response.data
  } catch (error) {
    throw new Error(
      `Failed to fetch Vehicles: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    )
  }
}

//Get all Vehicles
export async function getAllTruckingVehicles(): Promise<VehicleNew[]> {
  try {
    const response = await axiosInstance.get("/api/vehicles")
    return response.data
  } catch (error) {
    throw new Error(
      `Failed to fetch All Vehicles: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    )
  }
}
