import axiosInstance from "@/app/shared/axios"
import { DropDownValue } from "@/app/shared/types/DropDownValue"

// Get region types
export async function getRegionTypes(): Promise<DropDownValue[]> {
  try {
    const response = await axiosInstance.get("/api/masterdata/regionTypes")
    return response.data
  } catch (error) {
    throw new Error(
      `Failed to fetch Region Types: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    )
  }
}

//Get document types
export async function getDocumentTypes(): Promise<DropDownValue[]> {
  try {
    const response = await axiosInstance.get("/api/masterdata/documentTypes")
    return response.data
  } catch (error) {
    throw new Error(
      `Failed to fetch Document Types: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    )
  }
}
