// Basic vehicle type
export type Vehicle = {
  id: number
  name: string
  imageUrl?: string
}

// Household capacity type
export type HouseholdCapacity = {
  id: number
  name: string
  allowedVehicles: Vehicle[]
}

// -------------------------------
// Types for user

export type User = {
  name: string
  email: string
  phone: string
  isEmailVerified: boolean
  isPhoneVerified: boolean
  id: string
}
