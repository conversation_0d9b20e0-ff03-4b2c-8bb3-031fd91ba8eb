import { Quote<PERSON><PERSON>ult } from "@/app/components/common/types/quote-result.types"
import axiosInstance from "@/app/shared/axios"
import axios from "axios"
import { TAirAmbulanceServiceProps } from "./air-ambulance.types"

export async function airAmbulanceCalculationService({
  data,
}: {
  data: TAirAmbulanceServiceProps
}): Promise<QuoteResult> {
  const POST_DATA = {
    sourceCity: data.sourceCity,
    destinationCity: data.destinationCity,
    cargoWeightInKgs: Number(data.cargoWeightInKgs),
    regionTypeId: data.regionTypeId,
    // Add user info to the request
    name: data.name,
    emailId: data.emailId,
    phone: data.phone,
  }

  try {
    const response = await axiosInstance.post(
      `/api/calculator/airambulance`,
      POST_DATA,
    )
    return response.data
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw {
        message:
          error.response?.data?.error ||
          error.message ||
          "Failed to send data.",
        status: error.response?.status || 500,
      }
    }
    throw { message: "Failed to send data.", status: 500 }
  }
}
