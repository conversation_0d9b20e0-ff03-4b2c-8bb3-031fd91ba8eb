import { QuoteResult } from "@/app/components/common/types/quote-result.types"
import axiosInstance from "@/app/shared/axios"
import axios from "axios"
import { TCourierParcelServiceProps } from "./CourierAndParcel.types"

type CourierParcelCalculationServiceProps = TCourierParcelServiceProps & {
  serviceId: string | number
  userId: string | number
}

export async function courierParcelCalculationService(
  data: CourierParcelCalculationServiceProps,
): Promise<QuoteResult> {
  try {
    const response = await axiosInstance.post(
      `/api/calculator/courierandparcel`,
      data,
    )
    return response.data
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw {
        message:
          error.response?.data?.error ||
          error.message ||
          "Failed to send data.",
        status: error.response?.status || 500,
      }
    }
    throw { message: "Failed to send data.", status: 500 }
  }
}
