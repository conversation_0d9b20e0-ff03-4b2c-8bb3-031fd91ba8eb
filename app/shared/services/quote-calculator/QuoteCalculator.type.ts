// ------------------------
// Types for services
export type TServices = {
  code: string
  description: string
  icon: string
  name: string
  id: string
}

// -------------------------------
// Types for relocation house types

export type DistanceRange = {
  minDistance: number
  maxDistance: number
  id: string
}

export type VehicleOld = {
  id: string
  name: string
  length: number
  width: number
  height: number
  imageUrl: string
  isActive: boolean
  isInterStateAllowed: boolean
  relocationRange: DistanceRange[]
  truckingRange: DistanceRange[]
  sort: number
}

export type HouseTypeOld = {
  id: string
  type: string
  allowedVehicles: VehicleOld[]
}

// -------------------------------
// Types for relocation packing types

export type TPackingType = {
  id: string
  code: string
  name: string
}
