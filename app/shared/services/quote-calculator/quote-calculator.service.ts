import axios from "axios"
// ------------------------
// Constants
export const OLD_BASE_URL = "http://localhost:3001/api"
export const CALCULATE_URL = `${OLD_BASE_URL}/calculate`

// ------------------------
// Types Imports
import { HouseTypeOld, TServices } from "./QuoteCalculator.type"
import { User } from "./common"

// ------------------------
// Services
export async function getAllServices() {
  try {
    const response = await axios.get<TServices[]>(`${OLD_BASE_URL}/services`)
    return response.data
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw {
        message:
          error.response?.data?.error ||
          error.message ||
          "Failed to fetch all services.",
        status: error.response?.status || 500,
      }
    }
    throw { message: "Failed to fetch all services.", status: 500 }
  }
}

export async function getRelocationHouseType() {
  try {
    const response = await axios.get<HouseTypeOld[]>(
      `${OLD_BASE_URL}/housetypes`,
    )
    return response.data
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw {
        message:
          error.response?.data?.error ||
          error.message ||
          "Failed to fetch house types.",
        status: error.response?.status || 500,
      }
    }
    throw { message: "Failed to fetch house types.", status: 500 }
  }
}

export async function getTruckingVehicleRange() {
  try {
    const response = await axios.get(`${OLD_BASE_URL}/vehiclerange`)
    return response.data
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw {
        message:
          error.response?.data?.error ||
          error.message ||
          "Failed to fetch vehicle range.",
        status: error.response?.status || 500,
      }
    }
    throw { message: "Failed to fetch vehicle range.", status: 500 }
  }
}

export async function getVehicles() {
  try {
    const response = await axios.get(`${OLD_BASE_URL}/vehicles`)
    return response.data
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw {
        message:
          error.response?.data?.error ||
          error.message ||
          "Failed to fetch vehicles.",
        status: error.response?.status || 500,
      }
    }
    throw { message: "Failed to fetch vehicles.", status: 500 }
  }
}

export async function getAllCities() {
  try {
    const response = await axios.get(`${OLD_BASE_URL}/cities`)
    return response.data
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw {
        message:
          error.response?.data?.error ||
          error.message ||
          "Failed to fetch all cities.",
        status: error.response?.status || 500,
      }
    }
    throw { message: "Failed to fetch all cities.", status: 500 }
  }
}

// ------------ User Authentication Services -------------
export async function verifyPhoneService(payload: {
  code: number
  phone: number
}): Promise<User> {
  try {
    const { data } = await axios.post(
      `${OLD_BASE_URL}/otp/verify_user`,
      payload,
    )
    const userObject: User = {
      name: data.user.name,
      email: data.user.email,
      phone: data.user.phone,
      isEmailVerified: data.user.isEmailVerified,
      isPhoneVerified: data.user.isPhoneVerified,
      id: data.user._id,
    }
    return userObject
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw {
        message:
          error.response?.data?.error ||
          error.message ||
          "Failed to verify phone.",
        status: error.response?.status || 500,
      }
    }
    throw { message: "Failed to verify phone.", status: 500 }
  }
}

export async function createUserService(data: {
  name: string
  email: string
  phone: string
  service: string
}): Promise<User> {
  const payload = {
    ...data,
    phone: Number(data.phone),
  }

  try {
    const { data } = await axios.post(`${OLD_BASE_URL}/user/create`, payload)
    const userObject: User = {
      name: data.user.name,
      email: data.user.email,
      phone: data.user.phone,
      isEmailVerified: data.user.isEmailVerified,
      isPhoneVerified: data.user.isPhoneVerified,
      id: data.user._id,
    }
    return userObject
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw {
        message:
          error.response?.data?.error ||
          error.message ||
          "Failed to create user.",
        status: error.response?.status || 500,
      }
    }
    throw { message: "Failed to create user.", status: 500 }
  }
}

export async function resendOtpService({ phone }: { phone: string }) {
  const payload = {
    phone: Number(phone),
  }
  try {
    const { data } = await axios.post(
      `${OLD_BASE_URL}/otp/user/resend`,
      payload,
    )
    return data
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw {
        message:
          error.response?.data?.error ||
          error.message ||
          "Failed to resend OTP.",
        status: error.response?.status || 500,
      }
    }
    throw { message: "Failed to resend OTP.", status: 500 }
  }
}
