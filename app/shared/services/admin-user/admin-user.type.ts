import { DropDownValue } from "../../types/DropDownValue"

export interface AdminUser {
  id: number
  firstName: string
  lastName: string
  role?: DropDownValue
  roleId: number
  emailId: string
  emailConfirmed?: boolean
  phoneNumber?: string
  phoneNumberConfirmed?: boolean
  passwordHash?: string
  passwordSalt?: string
  lastLogin?: string // ISO date string
  isDeleted: boolean
  lastModified: string // ISO date string
  statusId: number
  status: DropDownValue
  isExternalUser?: boolean
  entityId?: number
}
