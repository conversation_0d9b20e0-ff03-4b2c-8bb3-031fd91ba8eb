import axios from "axios"
import { TTrackingServiceProps } from "./index"

export async function trackingService({ tracking_id }: TTrackingServiceProps) {
  try {
    const { data } = await axios.get(`/api/tracking?tracking_id=${tracking_id}`)
    return data
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw {
        message:
          error.response?.data?.error ||
          error.message ||
          "Something went wrong.",
        status: error.response?.status || 500,
      }
    }
    throw { message: "Something went wrong.", status: 500 }
  }
}
