export type TTrackingServiceProps = {
  tracking_id: string
}

type TrackingState = {
  location: string
  date: string // ISO timestamp
  carrier: number
  status: string
}

type ExternalTracking = {
  slug: string
  url: string
  method: string
  title: string
}

type Service = {
  slug: string
  name: string
}

type DetectedCarrier = {
  slug: string
  name: string
}

type Attribute = {
  l: string
  val: string
}

type TrackingData = {
  states: TrackingState[]
  carriers: string[]
  externalTracking: ExternalTracking[]
  status: string
  services: Service[]
  trackingId: string
  detectedCarrier: DetectedCarrier
  lastState: TrackingState
  attributes: Attribute[]
}

export type TPreTrackResponse = {
  uuid?: string
  shipments: TrackingData[]
}

export type TTrackingResponse = {
  shipments: TrackingData[]
}
