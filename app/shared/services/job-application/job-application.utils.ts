import { JobPosting } from "./job-application.types"

// Helper function to get the description from a job posting
// If jobPostingDescription is null/empty, use jobPosition.jobDescription instead
export function getJobDescription(jobPosting: JobPosting): string {
  if (
    jobPosting.jobPostingDescription &&
    jobPosting.jobPostingDescription.trim() !== ""
  ) {
    return jobPosting.jobPostingDescription
  }

  return jobPosting.jobPosition?.jobDescription || ""
}

// Helper function to get the requirements from a job posting
// If jobPostingRequirements is null/empty, use jobPosition.jobRequirements instead
export function getJobRequirements(jobPosting: JobPosting): string[] {
  let requirementsText = ""

  if (
    jobPosting.jobPostingRequirements &&
    jobPosting.jobPostingRequirements.trim() !== ""
  ) {
    requirementsText = jobPosting.jobPostingRequirements
  } else {
    requirementsText = jobPosting.jobPosition?.jobRequirements || ""
  }

  // Split by newlines and filter out empty strings
  return requirementsText
    .split("\n")
    .map((line) => line.trim())
    .filter((line) => line !== "")
}

// Helper function to get the job type from a job posting
export function getJobType(jobPosting: JobPosting): string {
  return jobPosting.jobPosition?.jobPositionType?.name || "Full-Time"
}

// Helper function to get the job locations from a job posting
export function getJobLocations(jobPosting: JobPosting): string[] {
  return jobPosting.jobLocations || []
}

// Helper function to get the job title from a job posting
export function getJobTitle(jobPosting: JobPosting): string {
  return (
    jobPosting.jobPostingTitle || jobPosting.jobPosition?.positionTitle || ""
  )
}

// Helper function to format a job posting for display
export function formatJobPosting(jobPosting: JobPosting) {
  return {
    id: jobPosting.id,
    title: getJobTitle(jobPosting),
    type: getJobType(jobPosting),
    description: getJobDescription(jobPosting),
    locations: getJobLocations(jobPosting),
    requirements: getJobRequirements(jobPosting),
    minimumExperienceInYears: jobPosting.minimumExperienceInYears,
    jobPositionId: jobPosting.jobPositionId,
    department: jobPosting.jobPosition?.department?.name || "",
    createdDate: jobPosting.createdDate,
  }
}
