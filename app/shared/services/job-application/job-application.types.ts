// Dropdown Value type
export type DDValue = {
  id: number
  name: string
  code?: string
  colorCode?: string
  isDisabled?: boolean
}

// App User type
export type AppUser = {
  id: number
  firstName: string
  lastName: string
  emailId: string
  phoneNumber?: string
  roleId: number
  role?: DDValue
}

// Job Position Type
export type JobPosition = {
  id: number
  departmentId: number
  positionTitle: string
  jobPositionTypeId: number
  jobPositionStatusId: number
  jobDescription: string
  jobRequirements: string
  jobCode: string
  lastModified: string // ISO date string
  createdDate: string // ISO date string
  createdById: number
  lastModifiedById: number
  createdByUserName?: string | null
  lastModifiedByUserName?: string | null
  department?: DDValue | null
  jobPositionType?: DDValue | null
  jobPositionStatus?: DDValue | null
}

// Job Posting Status type
export type JobPostingStatus = {
  id: number
  name: string
}

// Job Posting Type
export type JobPosting = {
  id: number
  jobPositionId: number
  jobPostingStatusId: number
  jobPostingTitle: string
  jobPostingDescription?: string | null
  jobPostingRequirements?: string | null
  minimumExperienceInYears: number
  jobLocations: string[]
  lastModified: string // ISO date string
  createdDate: string // ISO date string
  createdBy: number
  lastModifiedBy: number
  shouldKeepOpenUntilStopped: boolean
  schedulePostingStartDate?: string | null
  schedulePostingEndTime?: string | null
  jobPosition?: JobPosition | null
  jobPostingStatus?: JobPostingStatus | null
}

// Job Application Request Type (for creating a new application)
export type JobApplicationRequest = {
  name: string
  emailId: string
  phone: string
  addressLine1: string
  addressLine2?: string
  zipcode: string
  landmark?: string | null
  totalRelevantExperienceInYears?: number
  previousEmployer?: string | null
  previousJobPosition?: string | null
  previousExploymentInYears?: string | null
  preferredContactMethodId?: number
  jobPositionId: number
  jobApplicationStatusId: number
  referenceId?: number
  assignedAdminId?: number
  availableFromDate: string
  isAuthorizedToWorkInIndia: boolean
  requireVisaSponsorship: boolean
  isTCAccepted: boolean
  jobLocations: string
  resumeBase64?: string
  resumeFileName?: string
  resumeContentType?: string
  resumeDocumentUrl?: string
  adminActionRemark?: string | null
}

// Job Application Response Type (returned after submitting an application)
export type JobApplicationResponse = {
  id: number
  name: string
  emailId: string
  phone: string
  addressLine1: string
  addressLine2?: string
  zipcode: string
  city: string
  state: string
  country: string
  landmark?: string | null
  totalRelevantExperienceInYears: number
  previousEmployer?: string | null
  previousJobPosition?: string | null
  previousExploymentInYears?: string | null
  preferredContactMethodId: number
  jobPositionId: number
  jobApplicationStatusId: number
  referenceId: number
  assignedAdminId: number
  availableFromDate: string
  isAuthorizedToWorkInIndia: boolean
  requireVisaSponsorship: boolean
  isTCAccepted: boolean
  jobLocations: string
  resumeDocumentUrl: string
  adminActionRemark?: string | null
  lastModified: string
  createdDate: string
  preferredContactMethod?: DDValue | null
  jobPosition?: JobPosition | null
  jobApplicationStatus?: DDValue | null
  reference?: DDValue | null
  assignedAdmin?: AppUser | null
}

// Job Application Type (alias for JobApplicationResponse for backward compatibility)
export type JobApplication = JobApplicationResponse
