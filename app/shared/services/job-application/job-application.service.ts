import axios from "@/app/shared/axios"
import { DropDownValue } from "@/app/shared/types/DropDownValue"
import { isAxiosError } from "axios"
import {
  JobApplicationRequest,
  JobApplicationResponse,
  JobPosition,
  JobPosting,
} from "./job-application.types"

// Fetch job position statuses from the API
export async function getJobPositionStatuses(): Promise<DropDownValue[]> {
  try {
    const response = await axios.get("/api/MasterData/JobPositionStatuses")
    return response.data
  } catch (error) {
    throw new Error(
      `Failed to fetch Job Position Statuses: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    )
  }
}

// Fetch job position types from the API
export async function getJobPositionTypes(): Promise<DropDownValue[]> {
  try {
    const response = await axios.get("/api/MasterData/JobPositionTypes")
    return response.data
  } catch (error) {
    throw new Error(
      `Failed to fetch Job Position Types: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    )
  }
}

// Fetch job application statuses from the API
export async function getJobApplicationStatuses(): Promise<DropDownValue[]> {
  try {
    const response = await axios.get("/api/MasterData/JobApplicationStatuses")
    return response.data
  } catch (error) {
    throw new Error(
      `Failed to fetch Job Application Statuses: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    )
  }
}

// Fetch job posting statuses from the API
export async function getJobPostingStatuses(): Promise<DropDownValue[]> {
  try {
    const response = await axios.get("/api/MasterData/JobPostingStatuses")
    return response.data
  } catch (error) {
    throw new Error(
      `Failed to fetch Job Posting Statuses: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    )
  }
}

// Fetch departments from the API
export async function getDepartments(): Promise<DropDownValue[]> {
  try {
    const response = await axios.get("/api/MasterData/Departments")
    return response.data
  } catch (error) {
    throw new Error(
      `Failed to fetch Departments: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    )
  }
}

// Fetch contact methods from the API
export async function getContactMethods(): Promise<DropDownValue[]> {
  try {
    const response = await axios.get("/api/MasterData/ContactMethods")
    return response.data
  } catch (error) {
    throw new Error(
      `Failed to fetch Contact Methods: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    )
  }
}

// Fetch reference sources from the API
export async function getReferenceSources(): Promise<DropDownValue[]> {
  try {
    const response = await axios.get("/api/MasterData/ReferenceSources")
    return response.data
  } catch (error) {
    throw new Error(
      `Failed to fetch Reference Sources: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    )
  }
}

// Fetch all job positions from the API
export async function getJobPositions(
  pageNumber = 1,
  pageSize = 10,
): Promise<{ items: JobPosition[]; totalCount: number }> {
  try {
    const response = await axios.post("/api/JobPositions/search", {
      searchKey: "",
      filters: [],
      pagination: {
        pageNumber,
        pageSize,
      },
    })
    return {
      items: response.data.searchResult,
      totalCount: response.data.count,
    }
  } catch (error) {
    throw new Error(
      `Failed to fetch Job Positions: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    )
  }
}

// Fetch active job postings from the API
export async function getActiveJobPostings(
  pageNumber = 1,
  pageSize = 10,
): Promise<{ items: JobPosting[]; totalCount: number }> {
  try {
    const response = await axios.post("/api/JobPostings/search", {
      searchKey: "",
      filters: [{ field: "jobPostingStatusId", value: "1" }],
      pagination: {
        pageNumber,
        pageSize,
      },
    })

    return {
      items: response.data.searchResult,
      totalCount: response.data.count,
    }
  } catch (error) {
    throw new Error(
      `Failed to fetch Job Postings: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    )
  }
}

// Get job posting by ID
export async function getJobPostingById(id: number): Promise<JobPosting> {
  try {
    const response = await axios.get(`/api/JobPostings/${id}`)
    return response.data
  } catch (error) {
    throw new Error(
      `Failed to fetch Job Posting: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    )
  }
}

// Fetch job position by ID from the API
export async function getJobPositionById(id: number): Promise<JobPosition> {
  try {
    const response = await axios.get(`/api/JobPositions/${id}`)
    return response.data
  } catch (error) {
    throw new Error(
      `Failed to fetch Job Position: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    )
  }
}

// Submit a job application to the API
export async function submitJobApplication(
  application: JobApplicationRequest,
): Promise<JobApplicationResponse> {
  try {
    const response = await axios.post("/api/JobApplications", application)
    return response.data
  } catch (error) {
    if (isAxiosError(error)) {
      throw new Error(
        error.response?.data?.message ||
          error.message ||
          "Failed to submit job application",
      )
    }
    throw new Error(
      `Failed to submit Job Application: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    )
  }
}
