// otp.service.ts

const BASE_URL = "/api/phone-verification"

/**
 * Send OTP by calling the backend API route
 */
export async function sendOtp(
  phoneNumber: string,
): Promise<{ success: boolean; message?: string }> {
  try {
    const res = await fetch(`${BASE_URL}/send-otp`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ phoneNumber }),
    })
    const data = await res.json()
    if (res.ok && data.success) {
      return { success: true }
    } else {
      return { success: false, message: data.error || data.message }
    }
  } catch (error) {
    console.error("Error sending OTP:", error)
    return { success: false, message: "Failed to send OTP. Please try again." }
  }
}

/**
 * Verify OTP by calling the backend API route
 */
export async function verifyOtp(
  phoneNumber: string,
  otp: string,
): Promise<{ isVerified: boolean; message?: string }> {
  try {
    const res = await fetch(`${BASE_URL}/verify-otp`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ phoneNumber, otp }),
    })
    const data = await res.json()
    if (res.ok && data.isVerified) {
      return { isVerified: true }
    } else {
      return { isVerified: false, message: data.message }
    }
  } catch (error) {
    console.error("Error verifying OTP:", error)
    return {
      isVerified: false,
      message: "Failed to verify OTP. Please try again.",
    }
  }
}
