import axios from "axios"
import { NextRequest, NextResponse } from "next/server"
const BASE_TRACKING_URL = "https://api.trackingmore.com/v4"
const API_KEY = process.env.TRACKING_API_KEY

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const tracking_id = searchParams.get("tracking_id")

  if (!tracking_id) {
    return NextResponse.json(
      { error: "Tracking ID is required" },
      { status: 400 },
    )
  }
  try {
    const courier_code = await detectCourier(tracking_id)
    if (!courier_code) {
      throw new Error("Courier code not found")
    }

    // create shipment entry in the tracking-more database
    const create_tracking_res = await createTracking(tracking_id, courier_code)

    if (create_tracking_res.meta.code === 4101) {
      // if the shipment is already created, fetch the shipment details
      const get_tracking_res = await getTracking(tracking_id)
      return NextResponse.json(
        { messsage: "Tracking Found", data: get_tracking_res.data[0] },
        { status: 200 },
      )
    } else if (create_tracking_res.meta.code === 200) {
      // on successfully shipment creation, we receive back the shipment details
      return NextResponse.json(
        { messsage: "Tracking Created", data: create_tracking_res.data },
        { status: 200 },
      )
    }
    // if either of the above fails, return an error
    return NextResponse.json(
      { messsage: "Something went wrong" },
      { status: 400 },
    )
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Something went wrong"
    return NextResponse.json({ error: errorMessage }, { status: 500 })
  }
}

// ------------------------------------------------------------------------------------

// method to detect the courier from the tracking id
const detectCourier = async (tracking_id: string) => {
  const options = {
    method: "POST",
    url: `${BASE_TRACKING_URL}/couriers/detect`,
    headers: {
      "Tracking-Api-Key": API_KEY,
    },
    data: { tracking_number: tracking_id },
  }
  const { data } = await axios.request(options)
  if (data.meta.code === 200) {
    return data.data[0]?.courier_code || null
  } else {
    throw new Error(data.meta.message)
  }
}

// method to create a shipment entry in the tracking-more database
const createTracking = async (tracking_id: string, courier_code: string) => {
  const payload = {
    tracking_number: tracking_id,
    courier_code: courier_code,
  }
  const options = {
    method: "POST",
    url: `${BASE_TRACKING_URL}/trackings/create`,
    headers: {
      "Tracking-Api-Key": API_KEY,
    },
    data: payload,
  }
  try {
    const { data } = await axios.request(options)
    return data
  } catch (error) {
    if (axios.isAxiosError(error)) {
      return error.response?.data
    }
    return { error: "Something went wrong" }
  }
}

// method to fetch the shipment details
const getTracking = async (tracking_id: string) => {
  const options = {
    method: "GET",
    url: `${BASE_TRACKING_URL}/trackings/get?tracking_numbers=${tracking_id}`,
    headers: {
      "Tracking-Api-Key": API_KEY,
    },
  }
  try {
    const { data } = await axios.request(options)
    return data
  } catch (error) {
    if (axios.isAxiosError(error)) {
      return error.response?.data
    }
    return { error: "Something went wrong" }
  }
}
