import { NextRequest, NextResponse } from "next/server"

export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url)
  const payId = searchParams.get("pay-id")
  if (!payId) {
    return NextResponse.json({ error: "Missing pay-id" }, { status: 400 })
  }

  const RAZORPAY_KEY_ID = process.env.NEXT_PUBLIC_RAZORPAY_TEST_KEY_ID
  const RAZORPAY_SECRET = process.env.RAZORPAY_TEST_SECRET_KEY
  if (!RAZORPAY_KEY_ID || !RAZORPAY_SECRET) {
    return NextResponse.json(
      { error: "Razorpay credentials missing" },
      { status: 500 },
    )
  }

  const auth = Buffer.from(`${RAZORPAY_KEY_ID}:${RAZORPAY_SECRET}`).toString(
    "base64",
  )
  const apiUrl = `https://api.razorpay.com/v1/payment_links/${payId}`

  try {
    const res = await fetch(apiUrl, {
      headers: {
        Authorization: `Basic ${auth}`,
      },
    })
    if (!res.ok) {
      const err = await res.json()
      return NextResponse.json(
        { error: err.error?.description || "Failed to fetch payment link" },
        { status: res.status },
      )
    }
    const data = await res.json()
    // Map Razorpay response to frontend fields
    return NextResponse.json({
      name: data.customer?.name || "",
      email: data.customer?.email || "",
      phone: data.customer?.contact || "",
      amount: data.amount ? data.amount / 100 : 0,
      payment_link: data.short_url,
      status: data.status,
      pay_id: data.id,
    })
  } catch (error) {
    console.error(error)
    return NextResponse.json(
      { error: "Error fetching payment link" },
      { status: 500 },
    )
  }
}
