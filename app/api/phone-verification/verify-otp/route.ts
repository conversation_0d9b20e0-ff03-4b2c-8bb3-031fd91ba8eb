/* eslint-disable no-var */
import { NextRequest, NextResponse } from "next/server"

// Access the same in-memory OTP store
// In a real implementation, this would be a database or Redis
declare global {
  var otpStore: Record<string, { otp: string; timestamp: number }>
}

// Initialize the global store if it doesn't exist
if (!global.otpStore) {
  global.otpStore = {}
}

export async function POST(request: NextRequest) {
  try {
    const { phoneNumber, otp } = await request.json()

    if (!phoneNumber || !otp) {
      return NextResponse.json(
        { error: "Phone number and OTP are required" },
        { status: 400 },
      )
    }

    const storedData = global.otpStore[phoneNumber]

    // Check if O<PERSON> exists and is valid
    if (!storedData) {
      return NextResponse.json(
        {
          success: false,
          isVerified: false,
          message: "OTP not found or expired. Please request a new OTP.",
        },
        { status: 400 },
      )
    }

    // Check if OTP has expired
    if (Date.now() > storedData.timestamp) {
      // Remove expired OTP
      delete global.otpStore[phoneNumber]

      return NextResponse.json(
        {
          success: false,
          isVerified: false,
          message: "OTP has expired. Please request a new OTP.",
        },
        { status: 400 },
      )
    }

    // Optionally, you could verify with Fast2SMS API if you used their verify endpoint
    // For now, we just check our in-memory store
    if (storedData.otp === otp) {
      // OTP verified successfully, remove it from store
      delete global.otpStore[phoneNumber]

      return NextResponse.json(
        {
          success: true,
          isVerified: true,
          message: "Phone number verified successfully",
        },
        { status: 200 },
      )
    } else {
      return NextResponse.json(
        {
          success: false,
          isVerified: false,
          message: "Invalid OTP. Please try again.",
        },
        { status: 400 },
      )
    }
  } catch (error) {
    console.error("Error verifying OTP:", error)
    return NextResponse.json({ error: "Failed to verify OTP" }, { status: 500 })
  }
}
