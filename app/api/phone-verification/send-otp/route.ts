/* eslint-disable no-var */
import axios from "axios"
import { NextRequest, NextResponse } from "next/server"

declare global {
  var otpStore: Record<string, { otp: string; timestamp: number }>
}

if (!global.otpStore) {
  global.otpStore = {}
}

export async function POST(request: NextRequest) {
  try {
    const { phoneNumber } = await request.json()

    if (!phoneNumber) {
      return NextResponse.json(
        { error: "Phone number is required" },
        { status: 400 },
      )
    }

    const otp = Math.floor(100000 + Math.random() * 900000).toString()

    const apiKey = process.env.FAST2SMS_API_KEY
    const route = process.env.FAST2SMS_ROUTE || "q"
    const senderId = process.env.FAST2SMS_SENDER_ID || "HALNEX"
    const templateId = process.env.FAST2SMS_TEMPLATE_ID || "189889"

    // const url = "https://www.fast2sms.com/dev/bulkV2"

    const url = `https://www.fast2sms.com/dev/bulkV2?authorization=${apiKey}&route=${route}&sender_id=${senderId}&message=${templateId}&variables_values=${otp}&flash=0&numbers=${phoneNumber}&schedule_time=`

    try {
      const response = await axios.get(
        url,

        {
          // params: {
          //   authorization: apiKey,
          //   route,
          //   sender_id: "HALNEX",
          //   message,
          //   variables_values: otp,
          //   flash: 0,
          //   numbers: phoneNumber,
          // },
          headers: {
            authorization: apiKey,
            "Content-Type": "application/json",
          },
        },
      )
      if (!response.data || response.data.return !== true) {
        return NextResponse.json(
          { error: "Failed to send OTP via Fast2SMS" },
          { status: 500 },
        )
      }
    } catch (err) {
      return NextResponse.json(
        { error: err || "Error sending OTP via Fast2SMS" },
        { status: 500 },
      )
    }

    global.otpStore[phoneNumber] = {
      otp,
      timestamp: Date.now() + 5 * 60 * 1000,
    }

    return NextResponse.json(
      {
        success: true,
        requestId: Date.now().toString(),
        message: "OTP sent successfully",
      },
      { status: 200 },
    )
  } catch (error) {
    console.error("Error sending OTP:", error)
    return NextResponse.json({ error: "Failed to send OTP" }, { status: 500 })
  }
}
