import { sendEmail } from "@/app/shared/utils/email"
import { NextRequest, NextResponse } from "next/server"

export async function POST(req: NextRequest) {
  try {
    const { subject, html } = await req.json()
    if (!subject || !html) {
      return NextResponse.json(
        { success: false, message: "Missing subject or html." },
        { status: 400 },
      )
    }

    // Use environment variables for from and to
    const from = process.env.NEXT_PUBLIC_EMAIL_FROM as string
    const to = process.env.NEXT_PUBLIC_EMAIL_TO as string
    if (!from || !to) {
      return NextResponse.json(
        { success: false, message: "Email configuration missing." },
        { status: 500 },
      )
    }

    await sendEmail({ from, to, subject, html })
    return NextResponse.json({ success: true })
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Failed to send email."
    return NextResponse.json(
      { success: false, message: errorMessage },
      { status: 500 },
    )
  }
}
