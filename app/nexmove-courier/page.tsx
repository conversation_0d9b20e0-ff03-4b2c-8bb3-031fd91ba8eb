import { SEOComponent } from "@/app/shared/components"
import {
  generateSEOMetadata,
  generateServiceSchema,
  PAGE_SEO,
} from "@/app/shared/utils/seo"
import { BREADCRUMB_PATHS } from "@/app/shared/utils/seo-breadcrumbs"
import NexMoveCourierContainer from "../../containers/services/nexmove-courier/NexMoveCourierContainer"

export const metadata = generateSEOMetadata({
  ...PAGE_SEO.COURIER,
  url: "https://nex-move.com/nexmove-courier",
})

const Page = () => {
  const breadcrumbs = [
    BREADCRUMB_PATHS.HOME,
    BREADCRUMB_PATHS.SERVICES,
    BREADCRUMB_PATHS.COURIER,
  ]

  const serviceSchema = generateServiceSchema({
    name: "Courier & Parcel Services",
    description:
      "Fast and reliable courier services for documents and parcels. Same-day delivery, express shipping, and secure parcel handling.",
    url: "/nexmove-courier",
    area: [
      "India",
      "Delhi",
      "Mumbai",
      "Bangalore",
      "Chennai",
      "Kolkata",
      "Hyderabad",
      "Pune",
    ],
  })

  return (
    <>
      <SEOComponent
        breadcrumbs={breadcrumbs}
        structuredData={[serviceSchema]}
      />
      <NexMoveCourierContainer />
    </>
  )
}

export default Page
